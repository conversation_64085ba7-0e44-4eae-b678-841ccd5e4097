<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>移动端App测试Agent设计方案 - 完整报告</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;600;700&amp;family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <style>
        :root {
            --primary: #1a365d;
            --secondary: #2d3748;
            --accent: #4a5568;
            --neutral: #f7fafc;
            --base-100: #ffffff;
            --base-content: #1a202c;
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-accent: #edf2f7;
            --border-color: #e2e8f0;
            --success: #065f46;
            --info: #0369a1;
            --warning: #92400e;
            --error: #991b1b;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background-color: var(--bg-primary);
            font-weight: 500;
        }
        
        .serif {
            font-family: 'Noto Serif SC', serif;
        }
        
        .toc {
            position: fixed;
            left: 0;
            top: 0;
            width: 300px;
            height: 100vh;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            z-index: 1000;
            overflow-y: auto;
            padding: 2rem 1.5rem;
            color: #ffffff;
            box-shadow: 2px 0 8px rgba(0, 0, 0, 0.1);
        }
        
        .toc::-webkit-scrollbar {
            width: 4px;
        }
        
        .toc::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }
        
        .toc::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
        }
        
        .main-content {
            margin-left: 300px;
            min-height: 100vh;
        }
        
        .toc a {
            display: block;
            padding: 0.5rem 0;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            border-bottom: 1px dashed rgba(255,255,255,0.2);
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .toc a:hover {
            color: #ffffff;
            padding-left: 0.5rem;
            background-color: rgba(255,255,255,0.15);
            border-radius: 4px;
            transform: translateX(2px);
        }
        
        .toc .toc-level-2 {
            padding-left: 1rem;
            font-size: 0.9em;
        }
        
        .toc .toc-level-3 {
            padding-left: 2rem;
            font-size: 0.85em;
        }

        .chapter-divider {
            margin: 4rem 0;
            padding: 2rem 0;
            border-top: 3px dashed var(--border-color);
            border-bottom: 3px dashed var(--border-color);
            background: linear-gradient(135deg, var(--bg-accent), var(--bg-secondary));
            text-align: center;
        }

        .chapter-divider h1 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            margin: 0;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #e2e8f0 100%);
            min-height: 60vh;
            position: relative;
            overflow: hidden;
            border-bottom: 2px dashed var(--border-color);
        }
        
        .hero-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
            height: 100%;
        }
        
        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: var(--primary);
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .hero-visual {
            position: relative;
            height: 400px;
            background: linear-gradient(45deg, rgba(26, 54, 93, 0.1), rgba(74, 85, 104, 0.1));
            border-radius: 16px;
            overflow: hidden;
            border: 2px dashed var(--border-color);
        }
        
        .state-diagram {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
        }
        
        .section-header {
            border-left: 6px dashed var(--primary);
            padding-left: 1.5rem;
            margin: 3rem 0 2rem 0;
        }
        
        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--primary);
            text-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
        }
        
        .callout {
            background: linear-gradient(135deg, var(--bg-accent), var(--bg-secondary));
            border-left: 6px dashed var(--primary);
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
            border: 2px dashed var(--border-color);
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }
        
        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .architecture-card {
            background: var(--bg-primary);
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 12px -2px rgba(0, 0, 0, 0.1);
            border: 2px dashed var(--border-color);
            transition: all 0.3s ease;
        }
        
        .architecture-card:hover {
            transform: translateY(-4px);
            box-shadow: 0 12px 28px -4px rgba(0, 0, 0, 0.15);
            border-color: var(--primary);
            border-style: solid;
        }
        
        .code-block {
            background: #0f172a;
            color: #f1f5f9;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin: 1.5rem 0;
            border: 2px dashed #334155;
            box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        
        .phase-timeline {
            position: relative;
            margin: 3rem 0;
        }
        
        .phase-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
            background-image: repeating-linear-gradient(
                45deg,
                transparent,
                transparent 5px,
                var(--primary) 5px,
                var(--primary) 10px
            );
        }
        
        .phase-item {
            display: flex;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .phase-number {
            width: 2.5rem;
            height: 2.5rem;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1.5rem;
            flex-shrink: 0;
            border: 2px dashed #ffffff;
        }
        
        .phase-content {
            background: var(--bg-primary);
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            flex: 1;
            border: 2px dashed var(--border-color);
        }
        
        .highlight-box {
            background: linear-gradient(135deg, var(--bg-accent), var(--bg-secondary));
            border: 2px dashed var(--border-color);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
        }

        /* 将所有实线边框改为虚线 */
        .bg-white {
            border: 2px dashed var(--border-color) !important;
        }

        .bg-gray-50 {
            border: 2px dashed var(--border-color) !important;
        }

        .border-l-6 {
            border-left-style: dashed !important;
        }

        .border-l-4 {
            border-left-style: dashed !important;
        }

        .border-2 {
            border-style: dashed !important;
        }
        
        @media (max-width: 1024px) {
            .toc {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .toc.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .hero-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .hero-visual {
                height: 300px;
            }
            
            .section-header h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>

<body>
    <!-- Table of Contents -->
    <nav class="toc">
        <div class="mb-8">
            <h3 class="text-xl font-bold text-white mb-4">目录导航</h3>
        </div>
        
        <!-- 第一章 -->
        <div class="mb-6">
            <h4 class="text-lg font-semibold text-white mb-2 border-b border-dashed border-white/20 pb-2">第一章 - 问题分析与理论基础</h4>
            <a href="#chapter1">第一章概述</a>
            <a href="#problem-analysis" class="toc-level-2">1.1 当前Agent面临的挑战</a>
            <a href="#solution-overview" class="toc-level-2">1.2 状态机模式的核心价值</a>
            <a href="#architecture-overview" class="toc-level-2">1.3 整体架构设计</a>
            <a href="#core-design" class="toc-level-2">1.4 核心模块设计思路</a>
        </div>

        <!-- 第二章 -->
        <div>
            <h4 class="text-lg font-semibold text-white mb-2 border-b border-dashed border-white/20 pb-2">第二章 - 详细实现方案</h4>
            <a href="#chapter2">第二章概述</a>
            <a href="#state-machine" class="toc-level-2">2.1 状态机模块实现</a>
            <a href="#blackboard" class="toc-level-2">2.2 黑板记忆模块</a>
            <a href="#tools-api" class="toc-level-2">2.3 工具执行与API Server</a>
            <a href="#autonomy" class="toc-level-2">2.4 自主性与异常处理</a>
            <a href="#llm-integration" class="toc-level-2">2.5 与LLM的集成交互</a>
            <a href="#implementation" class="toc-level-2">2.6 实施步骤与路线图</a>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container mx-auto px-8 py-12 h-full">
                <div class="hero-grid">
                    <div class="hero-content">
                        <h1 class="serif">基于状态机的移动端App测试Agent设计方案</h1>
                        <p class="text-xl mb-6" style="color: var(--text-secondary);">解决长序列任务中的计划遗忘与自主决策挑战</p>
                        <div class="flex items-center space-x-6 text-sm" style="color: var(--text-muted);">
                            <span><i class="fas fa-calendar mr-2"></i>2025年8月14日</span>
                            <span><i class="fas fa-code mr-2"></i>技术架构</span>
                            <span><i class="fas fa-mobile-alt mr-2"></i>移动端测试</span>
                        </div>
                    </div>
                    <div class="hero-visual">
                        <div class="state-diagram">
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-blue-100 to-indigo-100 rounded-lg border-2 border-dashed border-gray-300">
                                <div class="text-center text-gray-600">
                                    <i class="fas fa-project-diagram text-6xl mb-4 opacity-60"></i>
                                    <p class="text-lg font-semibold">状态机架构图</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 第一章分隔符 -->
        <div class="chapter-divider" id="chapter1">
            <h1 class="serif">第一章 - 问题分析与理论基础</h1>
            <p class="mt-4 text-lg" style="color: var(--text-secondary);">深入分析现有问题，探索解决方案的理论基础</p>
        </div>

        <!-- Main Content Area -->
        <div class="container mx-auto px-8 py-12 max-w-5xl">

            <!-- Section 1: Problem Analysis -->
            <section id="problem-analysis" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">1.1 当前Agent面临的挑战</h2>
                </div>

                <div class="highlight-box">
                    <p class="text-lg font-medium mb-4" style="color: var(--text-primary);">
                        <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
                        现有移动端App测试Agent在执行长序列任务时面临着严重的计划遗忘问题和自主性不足的挑战，需要系统性的解决方案。
                    </p>
                </div>

                <div class="architecture-grid">
                    <div class="architecture-card">
                        <h4 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                            <i class="fas fa-brain text-red-500 mr-2"></i>
                            LLM上下文窗口限制
                        </h4>
                        <p style="color: var(--text-secondary);">
                            尽管本地模型拥有高达40k的上下文窗口，Agent在执行5-6步后仍会出现"计划遗忘"，偏离预设的测试流程。LLM的注意力机制会逐渐稀释早期的关键指令。
                        </p>
                    </div>

                    <div class="architecture-card">
                        <h4 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                            <i class="fas fa-exclamation-triangle text-orange-500 mr-2"></i>
                            缺乏自主异常处理
                        </h4>
                        <p style="color: var(--text-secondary);">
                            当遇到元素查找失败、页面加载延迟等常见异常时，Agent往往直接报错终止，缺乏自动重试、回退等基本的自我修复能力。
                        </p>
                    </div>

                    <div class="architecture-card">
                        <h4 class="text-xl font-semibold mb-3" style="color: var(--text-primary);">
                            <i class="fas fa-balance-scale text-blue-500 mr-2"></i>
                            执行与自主性平衡
                        </h4>
                        <p style="color: var(--text-secondary);">
                            在严格遵循测试计划的机械执行与应对变化的灵活性之间难以找到平衡点，需要一种既能保证确定性又能处理意外情况的机制。
                        </p>
                    </div>
                </div>
            </section>

            <!-- Section 2: Solution Overview -->
            <section id="solution-overview" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">1.2 状态机模式的核心价值</h2>
                </div>

                <div class="callout">
                    <p class="mb-4" style="color: var(--text-secondary);">
                        状态机模式将复杂的测试流程分解为以<strong>App界面状态为核心"主键"</strong>的离散状态，通过状态转换严格控制流程，并利用黑板记忆管理上下文，实现精确执行与自主决策的平衡。
                    </p>
                </div>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
                    <div class="bg-white p-6 rounded-lg shadow-md border-l-6 border-green-600 border-2">
                        <h4 class="font-semibold mb-2" style="color: var(--text-primary);">界面状态为核心</h4>
                        <p class="text-sm" style="color: var(--text-secondary);">以App页面跳转为状态边界，每个状态内操作上下文高度相关，降低决策复杂性</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md border-l-6 border-blue-600 border-2">
                        <h4 class="font-semibold mb-2" style="color: var(--text-primary);">黑板记忆优化</h4>
                        <p class="text-sm" style="color: var(--text-secondary);">通过中央知识库存储跨状态信息，实现上下文精简与历史信息持久化的平衡</p>
                    </div>
                    <div class="bg-white p-6 rounded-lg shadow-md border-l-6 border-purple-600 border-2">
                        <h4 class="font-semibold mb-2" style="color: var(--text-primary);">可配置自主性</h4>
                        <p class="text-sm" style="color: var(--text-secondary);">基于规则的重试逻辑与LLM智能决策相结合，实现可控的自主异常处理</p>
                    </div>
                </div>
            </section>

            <!-- Section 3: Architecture Overview -->
            <section id="architecture-overview" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">1.3 整体架构设计</h2>
                </div>

                <div class="bg-white rounded-lg shadow-lg p-8 mb-8 border-2" style="border-color: var(--border-color);">
                    <div class="w-full h-64 bg-gradient-to-br from-gray-100 to-blue-50 rounded-lg mb-6 flex items-center justify-center border-2 border-dashed border-gray-200">
                        <div class="text-center text-gray-600">
                            <i class="fas fa-layer-group text-6xl mb-4 opacity-60"></i>
                            <p class="text-lg font-semibold">三层架构示意图</p>
                        </div>
                    </div>

                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-cogs text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">核心引擎层</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">状态机 + 黑板记忆</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-tools text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">工具执行层</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">API Server + 工具封装</p>
                        </div>
                        <div class="text-center">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                                <i class="fas fa-brain text-white text-xl"></i>
                            </div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">决策规划层</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">LLM + 规则引擎</p>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 4: Core Design -->
            <section id="core-design" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">1.4 核心模块设计思路</h2>
                </div>

                <div class="space-y-6">
                    <div class="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg border-2" style="border-color: var(--border-color);">
                        <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">1</div>
                        <div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">状态机驱动执行</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">以App界面状态为核心构建状态机，每个状态封装相关操作，通过状态转换控制执行流程</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg border-2" style="border-color: var(--border-color);">
                        <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">2</div>
                        <div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">黑板记忆管理</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">建立中央知识库存储设备信息、页面状态、执行历史等跨状态共享信息</p>
                        </div>
                    </div>

                    <div class="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg border-2" style="border-color: var(--border-color);">
                        <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">3</div>
                        <div>
                            <h4 class="font-semibold mb-2" style="color: var(--text-primary);">分层异常处理</h4>
                            <p class="text-sm" style="color: var(--text-secondary);">结合规则引擎和LLM决策，实现从简单重试到复杂决策的分层异常处理机制</p>
                        </div>
                    </div>
                </div>
            </section>

        </div>

        <!-- 第二章分隔符 -->
        <div class="chapter-divider" id="chapter2">
            <h1 class="serif">第二章 - 详细实现方案</h1>
            <p class="mt-4 text-lg" style="color: var(--text-secondary);">具体的技术实现细节和开发指导</p>
        </div>

        <!-- 第二章内容 -->
        <div class="container mx-auto px-8 py-12 max-w-5xl">

            <!-- Section 1: State Machine -->
            <section id="state-machine" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">2.1 状态机模块实现</h2>
                </div>

                <div class="callout">
                    <h4 class="font-semibold mb-3" style="color: var(--text-primary);">关键设计原则</h4>
                    <p style="color: var(--text-secondary);">状态识别严格以<strong>App界面状态为核心"主键"</strong>，状态的边界由明确的页面跳转事件界定。</p>
                </div>

                <h4 class="text-xl font-semibold mb-4 mt-8" style="color: var(--text-primary);">状态数据结构示例</h4>
                <div class="code-block">
                    <pre>{
"MainPage": {
  "actions": [
    {
      "tool": "find_element",
      "parameters": {"element": "左上角地址"},
      "expected_result": "element_found"
    }
  ],
  "transitions": {
    "element_found": "AddressSelectionPage",
    "element_not_found": "ErrorState"
  }
},
"AddressSelectionPage": {
  "actions": [
    {
      "tool": "ocr_validate_text",
      "parameters": {"target_text": "搜索城市/区县/地点"},
      "expected_result": "text_validated"
    },
    {
      "tool": "tap",
      "parameters": {"element": "搜索框"},
      "expected_result": "tap_executed"
    }
  ],
  "transitions": {
    "text_validated": "AddressSelectionPage",
    "tap_executed": "SearchPage"
  }
}
}</pre>
                </div>
            </section>

            <!-- Section 2: Blackboard -->
            <section id="blackboard" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">2.2 黑板记忆模块</h2>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 mb-6 border-2" style="border-color: var(--border-color);">
                    <h4 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">核心数据结构设计</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h5 class="font-medium mb-2" style="color: var(--text-primary);">设备信息</h5>
                            <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                                <li>• udid: 设备唯一标识符</li>
                                <li>• platform: 测试平台 (ios/android)</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium mb-2" style="color: var(--text-primary);">页面信息</h5>
                            <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                                <li>• current_page_name: 当前状态名称</li>
                                <li>• current_page_screenshot: 页面截图</li>
                                <li>• current_page_layout: 布局信息</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium mb-2" style="color: var(--text-primary);">元素信息</h5>
                            <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                                <li>• elements: 关键元素坐标缓存</li>
                                <li>• found_texts: OCR识别文本</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium mb-2" style="color: var(--text-primary);">执行日志</h5>
                            <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                                <li>• execution_history: 动作执行历史</li>
                                <li>• error_log: 异常记录</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="highlight-box">
                    <h4 class="font-semibold mb-3" style="color: var(--text-primary);">记忆更新策略</h4>
                    <p class="text-sm mb-3" style="color: var(--text-secondary);">
                        状态切换时执行策略性的"记忆更新"与"上下文清除"：
                    </p>
                    <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                        <li>• 离开旧状态：持久化关键信息到黑板</li>
                        <li>• 进入新状态：更新页面信息，清除旧状态上下文</li>
                        <li>• 保留跨状态共享信息（如设备UDID）</li>
                    </ul>
                </div>
            </section>

            <!-- Section 3: Tools and API -->
            <section id="tools-api" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">2.3 工具执行与API Server</h2>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 mb-6 border-2" style="border-color: var(--border-color);">
                    <h4 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">技术选型：基于Flask的轻量级服务器</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                            <h5 class="font-medium mb-2" style="color: var(--text-primary);">优势特性</h5>
                            <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                                <li>• 轻量快速，低性能开销</li>
                                <li>• 易于与Python工具函数集成</li>
                                <li>• 灵活性强，便于扩展</li>
                                <li>• 活跃的社区支持</li>
                            </ul>
                        </div>
                        <div>
                            <h5 class="font-medium mb-2" style="color: var(--text-primary);">部署配置</h5>
                            <ul class="text-sm space-y-1" style="color: var(--text-secondary);">
                                <li>• 独立进程运行</li>
                                <li>• 监听 localhost:5000</li>
                                <li>• RESTful API设计</li>
                                <li>• 统一的JSON请求/响应格式</li>
                            </ul>
                        </div>
                    </div>
                </div>

                <div class="space-y-6">
                    <div class="bg-gray-50 rounded-lg p-6 border-2" style="border-color: var(--border-color);">
                        <h5 class="font-semibold mb-3" style="color: var(--text-primary);">POST /find_element - 查找页面元素</h5>
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <h6 class="text-sm font-medium mb-2" style="color: var(--text-primary);">请求示例</h6>
                                <div class="code-block text-xs">
                                    <pre>{
"udid": "device123",
"element": "左上角地址"
}</pre>
                                </div>
                            </div>
                            <div>
                                <h6 class="text-sm font-medium mb-2" style="color: var(--text-primary);">成功响应</h6>
                                <div class="code-block text-xs">
                                    <pre>{
"status": "success",
"result": "element_found",
"data": {"x": 100, "y": 200}
}</pre>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Section 4: Autonomy -->
            <section id="autonomy" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">2.4 自主性与异常处理机制</h2>
                </div>

                <div class="bg-white rounded-lg shadow-md p-6 mb-6 border-2" style="border-color: var(--border-color);">
                    <h4 class="text-lg font-semibold mb-4" style="color: var(--text-primary);">规则定义与配置</h4>
                    <div class="code-block">
                        <pre>{
"rules": [
  {
    "trigger": "element_not_found",
    "action": "retry",
    "parameters": {
      "wait_seconds": 3,
      "max_retries": 2
    },
    "scope": "global"
  },
  {
    "trigger": "tap_failed",
    "action": "call_llm",
    "parameters": {
      "timeout": 30
    },
    "scope": "tap_tools"
  }
]
}</pre>
                    </div>
                </div>

                <div class="space-y-4">
                    <div class="bg-green-50 border-l-6 border-green-500 p-4 rounded border-2" style="border-color: var(--success);">
                        <h5 class="font-semibold text-green-800 mb-2">元素查找失败重试流程</h5>
                        <ol class="text-sm text-green-700 space-y-1">
                            <li>1. 首次查找失败，返回"element_not_found"</li>
                            <li>2. 规则引擎匹配重试规则，等待3秒</li>
                            <li>3. 自动重试查找，最多重试2次</li>
                            <li>4. 若重试成功，继续执行；否则上报LLM</li>
                        </ol>
                    </div>

                    <div class="bg-blue-50 border-l-6 border-blue-500 p-4 rounded border-2" style="border-color: var(--info);">
                        <h5 class="font-semibold text-blue-800 mb-2">页面加载超时处理</h5>
                        <ol class="text-sm text-blue-700 space-y-1">
                            <li>1. 点击操作后连续查找元素失败</li>
                            <li>2. 调用get_current_page检查页面状态</li>
                            <li>3. 页面未变：重试点击；已变：增加等待时间</li>
                            <li>4. 再次尝试查找目标元素</li>
                        </ol>
                    </div>
                </div>
            </section>

            <!-- Section 5: LLM Integration -->
            <section id="llm-integration" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">2.5 与LLM的集成交互</h2>
                </div>

                <div class="architecture-grid">
                    <div class="architecture-card">
                        <div class="text-center mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
                                <i class="fas fa-decision text-white text-xl"></i>
                            </div>
                        </div>
                        <h4 class="text-xl font-semibold mb-3 text-center" style="color: var(--text-primary);">决策引擎</h4>
                        <p class="text-center text-sm" style="color: var(--text-secondary);">
                            处理规则引擎无法解决的复杂异常，基于当前上下文进行智能决策，提供恢复建议
                        </p>
                    </div>

                    <div class="architecture-card">
                        <div class="text-center mb-4">
                            <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
                                <i class="fas fa-route text-white text-xl"></i>
                            </div>
                        </div>
                        <h4 class="text-xl font-semibold mb-3 text-center" style="color: var(--text-primary);">规划器（可选）</h4>
                        <p class="text-center text-sm" style="color: var(--text-secondary);">
                            根据高层次测试需求自动生成详细的测试计划JSON，降低测试用例编写门槛
                        </p>
                    </div>
                </div>
            </section>

            <!-- Section 6: Implementation -->
            <section id="implementation" class="mb-16">
                <div class="section-header">
                    <h2 class="serif">2.6 阶段性测试与实现步骤</h2>
                </div>

                <div class="phase-timeline">
                    <div class="phase-item">
                        <div class="phase-number">1</div>
                        <div class="phase-content">
                            <h4 class="text-lg font-semibold mb-3" style="color: var(--text-primary);">第一阶段：基础模块搭建与测试</h4>
                            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                                <div class="bg-green-50 p-4 rounded-lg border-2" style="border-color: var(--success);">
                                    <h5 class="font-medium text-green-800 mb-2">黑板记忆模块</h5>
                                    <ul class="text-sm text-green-700 space-y-1">
                                        <li>• 定义Blackboard类</li>
                                        <li>• 实现核心API</li>
                                        <li>• 编写单元测试</li>
                                    </ul>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg border-2" style="border-color: var(--info);">
                                    <h5 class="font-medium text-blue-800 mb-2">API Server</h5>
                                    <ul class="text-sm text-blue-700 space-y-1">
                                        <li>• 搭建Flask应用</li>
                                        <li>• 封装find_element工具</li>
                                        <li>• 实现统一响应格式</li>
                                    </ul>
                                </div>
                                <div class="bg-purple-50 p-4 rounded-lg border-2" style="border-color: var(--primary);">
                                    <h5 class="font-medium text-purple-800 mb-2">状态机框架</h5>
                                    <ul class="text-sm text-purple-700 space-y-1">
                                        <li>• 定义核心StateMachine类</li>
                                        <li>• 实现状态转换逻辑</li>
                                        <li>• 测试简单流程</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="text-sm" style="color: var(--text-secondary);">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                <strong>交付物：</strong>通过单元测试的blackboard.py、可运行的api_server.py、state_machine.py模块
                            </div>
                        </div>
                    </div>

                    <div class="phase-item">
                        <div class="phase-number">2</div>
                        <div class="phase-content">
                            <h4 class="text-lg font-semibold mb-3" style="color: var(--text-primary);">第二阶段：核心流程集成与测试</h4>
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                                <div class="bg-green-50 p-4 rounded-lg border-2" style="border-color: var(--success);">
                                    <h5 class="font-medium text-green-800 mb-2">模块集成</h5>
                                    <ul class="text-sm text-green-700 space-y-1">
                                        <li>• 状态机调用API Server</li>
                                        <li>• 状态转换更新黑板</li>
                                        <li>• 验证数据流通畅性</li>
                                    </ul>
                                </div>
                                <div class="bg-blue-50 p-4 rounded-lg border-2" style="border-color: var(--info);">
                                    <h5 class="font-medium text-blue-800 mb-2">配置转换</h5>
                                    <ul class="text-sm text-blue-700 space-y-1">
                                        <li>• 开发plan_converter.py</li>
                                        <li>• 解析测试计划JSON</li>
                                        <li>• 生成状态机配置</li>
                                    </ul>
                                </div>
                            </div>
                            <div class="text-sm" style="color: var(--text-secondary);">
                                <i class="fas fa-check-circle text-green-500 mr-2"></i>
                                <strong>交付物：</strong>集成状态机、转换器脚本、单步执行验证报告
                            </div>
                        </div>
                    </div>
                </div>
            </section>

            <!-- Conclusion -->
            <section class="mb-16">
                <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-8 border-2" style="border-color: var(--border-color);">
                    <h2 class="text-2xl font-semibold mb-4 serif" style="color: var(--text-primary);">总结与展望</h2>
                    <p class="mb-4" style="color: var(--text-secondary);">
                        基于状态机的移动端App测试Agent设计方案，通过将复杂的测试流程分解为以App界面状态为核心的离散状态，有效解决了长序列任务中的计划遗忘问题。结合黑板记忆机制和分层决策系统，在保证测试计划精确执行的同时，赋予了Agent基于规则的自主决策能力。
                    </p>
                    <p class="mb-6" style="color: var(--text-secondary);">
                        该方案不仅适用于当前的移动端App测试场景，其模块化设计和可扩展的架构也为未来的功能扩展奠定了基础。随着规则的不断丰富和LLM决策能力的提升，Agent将能够处理更加复杂的测试场景，成为移动端App质量保障的强大工具。
                    </p>
                    <div class="flex items-center justify-center space-x-8 text-sm" style="color: var(--text-muted);">
                        <span><i class="fas fa-calendar mr-2"></i>2025年8月14日</span>
                        <span><i class="fas fa-user mr-2"></i>架构设计文档</span>
                        <span><i class="fas fa-code mr-2"></i>技术方案</span>
                    </div>
                </div>
            </section>
        </div>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.toc a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu toggle
        function toggleMobileMenu() {
            const toc = document.querySelector('.toc');
            toc.classList.toggle('open');
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const toc = document.querySelector('.toc');
            const menuButton = document.querySelector('.mobile-menu-button');
            
            if (toc.classList.contains('open') && 
                !toc.contains(event.target) && 
                event.target !== menuButton) {
                toc.classList.remove('open');
            }
        });

        // Handle window resize
        function handleResize() {
            const toc = document.querySelector('.toc');
            if (window.innerWidth >= 1024) {
                toc.classList.remove('open');
            }
        }

        handleResize();
        window.addEventListener('resize', handleResize);
    </script>

</body>
</html>