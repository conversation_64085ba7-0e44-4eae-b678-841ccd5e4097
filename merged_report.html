<!DOCTYPE html><html lang="zh-CN"><head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>LLM-Agent在长步骤UI测试中的记忆与自主性平衡问题解决方案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        body { 
            font-family: 'Inter', sans-serif; 
            background-color: #ffffff;
            color: #000000;
            line-height: 1.7;
        }
        .toc-fixed { 
            position: fixed; 
            top: 0; 
            left: 0; 
            width: 280px; 
            height: 100vh; 
            overflow-y: auto; 
            z-index: 40; 
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%); 
            border-right: 1px solid #cbd5e1; 
            padding: 2rem 1.5rem;
            color: white;
        }
        .main-content { 
            margin-left: 280px; 
            min-height: 100vh; 
            background-color: #ffffff;
        }
        .hero-gradient { 
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%); 
        }
        .text-shadow { 
            text-shadow: 0 2px 4px rgba(0,0,0,0.3); 
        }
        .glass-effect { 
            backdrop-filter: blur(10px); 
            background: rgba(255,255,255,0.9); 
        }
        .citation { 
            color: #3b82f6; 
            text-decoration: none; 
            font-weight: 500; 
        }
        .citation:hover { 
            text-decoration: underline; 
        }
        .section-divider { 
            background: linear-gradient(90deg, #3b82f6, #60a5fa, #3b82f6); 
            height: 2px; 
        }
        
        /* Chapter dividers */
        .chapter-divider {
            background: linear-gradient(135deg, #1e40af 0%, #3b82f6 100%);
            color: white;
            padding: 3rem 0;
            margin: 4rem 0;
            text-align: center;
        }
        
        .chapter-title {
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 1rem;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        .chapter-subtitle {
            font-size: 1.25rem;
            opacity: 0.9;
        }
        
        /* TOC styling */
        .toc-fixed a {
            display: block;
            padding: 0.5rem 0;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }
        
        .toc-fixed a:hover {
            color: white;
            padding-left: 0.5rem;
        }
        
        .toc-level-2 {
            padding-left: 1rem !important;
            font-size: 0.9em;
        }
        
        .toc-level-3 {
            padding-left: 2rem !important;
            font-size: 0.85em;
        }
        
        /* Page 2 specific styling - white background, black text */
        .page2-content {
            background-color: #ffffff;
            color: #000000;
        }
        
        .page2-content .section-header {
            border-left: 4px solid #1e40af;
            padding-left: 1.5rem;
            margin: 3rem 0 2rem 0;
        }
        
        .page2-content .section-header h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: #1e40af;
        }
        
        .page2-content .callout {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.05), rgba(59, 130, 246, 0.05));
            border-left: 4px solid #3b82f6;
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
            color: #000000;
        }
        
        .page2-content .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .page2-content .architecture-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
            color: #000000;
        }
        
        .page2-content .architecture-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }
        
        .page2-content .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin: 1.5rem 0;
        }
        
        .page2-content .highlight-box {
            background: linear-gradient(135deg, rgba(30, 64, 175, 0.1), rgba(59, 130, 246, 0.05));
            border: 1px solid #3b82f6;
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
            color: #000000;
        }
        
        .page2-content .hero-section {
            background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
            min-height: 60vh;
            position: relative;
            overflow: hidden;
        }
        
        .page2-content .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            color: #1e40af;
        }
        
        .page2-content .hero-visual {
            position: relative;
            height: 400px;
            background: linear-gradient(45deg, rgba(30, 64, 175, 0.1), rgba(59, 130, 246, 0.1));
            border-radius: 16px;
            overflow: hidden;
        }
        
        .page2-content .phase-timeline {
            position: relative;
            margin: 3rem 0;
        }
        
        .page2-content .phase-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, #1e40af, #3b82f6);
        }
        
        .page2-content .phase-item {
            display: flex;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .page2-content .phase-number {
            width: 2.5rem;
            height: 2.5rem;
            background: #1e40af;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1.5rem;
            flex-shrink: 0;
        }
        
        .page2-content .phase-content {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
            color: #000000;
        }
        
        @media (max-width: 1024px) {
            .toc-fixed { 
                width: 0;
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .toc-fixed.active {
                transform: translateX(0);
                width: 280px;
            }
            .main-content { 
                margin-left: 0;
                transition: margin-left 0.3s ease;
            }
            .main-content.toc-active {
                margin-left: 280px;
            }
        }
        
        @media (max-width: 768px) {
            .hero-gradient {
                padding: 1rem;
            }
            .hero-gradient h1 {
                font-size: 2.25rem;
                line-height: 1.2;
            }
            .hero-gradient p {
                font-size: 1rem;
            }
            section {
                padding-left: 1rem;
                padding-right: 1rem;
            }
        }
    </style>
  </head>

  <body class="bg-white text-black overflow-x-hidden">
    <!-- Table of Contents -->
    <nav class="toc-fixed p-6">
      <div class="mb-8">
        <h2 class="text-lg font-bold text-white mb-4">目录导航</h2>
        <div class="space-y-2">
          <!-- Chapter 1 -->
          <div class="mb-4">
            <h3 class="text-base font-bold text-white mb-2">第一章：理论基础与问题分析</h3>
            <a href="#problem-diagnosis" class="block text-sm text-white hover:text-blue-200 transition-colors">1. 核心问题诊断</a>
            <div class="ml-3 space-y-1">
              <a href="#context-window" class="block text-xs text-gray-200 hover:text-blue-200">1.1 上下文窗口限制</a>
              <a href="#model-limitations" class="block text-xs text-gray-200 hover:text-blue-200">1.2 模型能力局限</a>
              <a href="#memory-strategy" class="block text-xs text-gray-200 hover:text-blue-200">1.3 记忆策略不足</a>
            </div>

            <a href="#memory-optimization" class="block text-sm text-white hover:text-blue-200 transition-colors">2. 记忆管理优化</a>
            <div class="ml-3 space-y-1">
              <a href="#long-term-memory" class="block text-xs text-gray-200 hover:text-blue-200">2.1 长期记忆系统</a>
              <a href="#prompt-engineering" class="block text-xs text-gray-200 hover:text-blue-200">2.2 提示工程优化</a>
            </div>

            <a href="#multi-model" class="block text-sm text-white hover:text-blue-200 transition-colors">3. 多模型协作架构</a>
            <div class="ml-3 space-y-1">
              <a href="#factored-agent" class="block text-xs text-gray-200 hover:text-blue-200">3.1 因子化代理模式</a>
              <a href="#autogen-framework" class="block text-xs text-gray-200 hover:text-blue-200">3.2 AutoGen框架</a>
            </div>

            <a href="#state-machine" class="block text-sm text-white hover:text-blue-200 transition-colors">4. 状态机流程控制</a>
            <div class="ml-3 space-y-1">
              <a href="#state-core" class="block text-xs text-gray-200 hover:text-blue-200">4.1 核心思想</a>
              <a href="#state-implementation" class="block text-xs text-gray-200 hover:text-blue-200">4.2 实现方案</a>
            </div>

            <a href="#autonomy" class="block text-sm text-white hover:text-blue-200 transition-colors">5. 自主性实现机制</a>
            <div class="ml-3 space-y-1">
              <a href="#error-handling" class="block text-xs text-gray-200 hover:text-blue-200">5.1 错误处理</a>
              <a href="#early-termination" class="block text-xs text-gray-200 hover:text-blue-200">5.2 提前终止</a>
            </div>

            <a href="#ui-perception" class="block text-sm text-white hover:text-blue-200 transition-colors">6. UI感知能力增强</a>
            <div class="ml-3 space-y-1">
              <a href="#ocr-integration" class="block text-xs text-gray-200 hover:text-blue-200">6.1 OCR集成</a>
              <a href="#multimodal-models" class="block text-xs text-gray-200 hover:text-blue-200">6.2 多模态模型</a>
            </div>
          </div>

          <!-- Chapter 2 -->
          <div class="mb-4">
            <h3 class="text-base font-bold text-white mb-2">第二章：技术架构与实现方案</h3>
            <a href="#overview" class="block text-sm text-white hover:text-blue-200 transition-colors">1. 核心问题与解决方案概述</a>
            <a href="#architecture" class="block text-sm text-white hover:text-blue-200 transition-colors">2. 整体架构设计</a>
            <a href="#core-modules" class="block text-sm text-white hover:text-blue-200 transition-colors">3. 核心模块详细设计</a>
            <a href="#tools-api" class="block text-sm text-white hover:text-blue-200 transition-colors">4. 工具执行与API Server</a>
            <a href="#autonomy-impl" class="block text-sm text-white hover:text-blue-200 transition-colors">5. 自主性与异常处理机制</a>
            <a href="#llm-integration" class="block text-sm text-white hover:text-blue-200 transition-colors">6. 与LLM的集成与交互</a>
            <a href="#implementation" class="block text-sm text-white hover:text-blue-200 transition-colors">7. 阶段性测试与实现步骤</a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Hero Section -->
      <section class="hero-gradient text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 py-16 lg:py-24">
          <!-- Bento Grid Layout -->
          <div class="bento-grid grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <!-- Title Card -->
            <div class="lg:col-span-2 space-y-6">
              <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight text-shadow">
                解决LLM-Agent在长步骤UI测试中的
                <span class="italic text-blue-200">记忆与自主性</span>
                平衡问题
              </h1>
              <p class="text-lg sm:text-xl lg:text-2xl text-blue-100 font-light leading-relaxed max-w-full sm:max-w-2xl">
                通过状态机解耦流程控制与模型决策，实现严格遵循测试计划的同时保持智能自主性
              </p>
            </div>

            <!-- Visual Element -->
            <div class="relative">
              <div class="glass-effect rounded-2xl p-4 sm:p-8 border border-white border-opacity-20">
                <div class="text-center space-y-4">
                  <div class="text-3xl sm:text-4xl font-bold text-slate-800">30B</div>
                  <div class="text-sm sm:text-base text-slate-600">本地模型规模</div>
                  <div class="h-px bg-slate-300"></div>
                  <div class="text-xl sm:text-2xl font-bold text-slate-800">20+</div>
                  <div class="text-sm sm:text-base text-slate-600">测试步骤</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Chapter 1 Divider -->
      <div class="chapter-divider">
        <div class="max-w-7xl mx-auto px-4 sm:px-6">
          <h1 class="chapter-title">第一章</h1>
          <p class="chapter-subtitle">理论基础与问题分析</p>
        </div>
      </div>

      <!-- Key Highlights -->
      <section class="bg-white py-12">
        <div class="max-w-7xl mx-auto px-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center space-y-3">
              <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-brain text-2xl text-blue-600"></i>
              </div>
              <h3 class="text-lg font-semibold text-slate-800">宏观控制</h3>
              <p class="text-sm text-slate-600">状态机管理整体流程，确保计划严格执行</p>
            </div>
            <div class="text-center space-y-3">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-robot text-2xl text-green-600"></i>
              </div>
              <h3 class="text-lg font-semibold text-slate-800">微观自主</h3>
              <p class="text-sm text-slate-600">LLM在单个步骤内智能决策与自适应调整</p>
            </div>
            <div class="text-center space-y-3">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-eye text-2xl text-purple-600"></i>
              </div>
              <h3 class="text-lg font-semibold text-slate-800">视觉感知</h3>
              <p class="text-sm text-slate-600">OCR与多模态模型增强UI理解能力</p>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Core Problem Analysis -->
      <section id="problem-diagnosis" class="py-16 bg-slate-50">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">核心问题诊断：模型为何会"忘记"测试计划</h2>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div class="space-y-6">
              <p class="text-lg text-slate-700 leading-relaxed">
                在构建由大型语言模型（LLM）驱动的自主代理（Agent）以执行复杂的UI自动化测试时，一个核心的挑战在于如何平衡严格遵循预设计划与保持自主决策能力。
              </p>
              <p class="text-lg text-slate-700 leading-relaxed">
                用户提供的场景中，一个本地的30B模型在执行一个包含20个步骤的测试计划时，往往在完成前几个步骤后便开始偏离预定轨道。这种现象并非偶然，而是由LLM在架构和实现上的固有特性所决定的。
              </p>
            </div>
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-slate-200">
              <img src="https://kimi-web-img.moonshot.cn/img/moonlight-paper-snapshot.s3.ap-northeast-2.amazonaws.com/28ea435fbb30e58efd5cbbff4488dc7d90774daf.png" alt="LLM记忆限制的抽象概念图" class="w-full rounded-lg" size="medium" aspect="wide" query="大型语言模型记忆限制" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>
            </div>
          </div>

          <!-- Context Window Limitations -->
          <div id="context-window" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <i class="fas fa-window-maximize text-blue-600 mr-3"></i>
              上下文窗口限制：长序列任务的天然瓶颈
            </h3>
            <div class="bg-white rounded-xl p-8 shadow-lg border border-slate-200">
              <p class="text-slate-700 mb-6">
                大型语言模型（LLM）的核心工作机制是基于对输入文本（即"上下文"）的理解和生成。然而，任何模型能够处理的上下文长度都是有限的，这个长度被称为"上下文窗口"（Context Window）。
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="space-y-4">
                  <h4 class="font-semibold text-slate-800">问题表现</h4>
                  <ul class="space-y-2 text-slate-700">
                    <li class="flex items-start">
                      <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-1"></i>
                      当任务序列过长时，信息量很容易超出本地30B模型的上下文窗口限制
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-1"></i>
                      模型为了处理新信息，不得不丢弃早期部分上下文
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-1"></i>
                      导致忘记测试计划的初始目标和已完成步骤
                    </li>
                  </ul>
                </div>
                <div class="space-y-4">
                  <h4 class="font-semibold text-slate-800">影响后果</h4>
                  <ul class="space-y-2 text-slate-700">
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-red-500 mr-2 mt-1"></i>
                      无法做出与全局计划一致的决策
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-red-500 mr-2 mt-1"></i>
                      只能基于最近几步进行模糊推测
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-red-500 mr-2 mt-1"></i>
                      最终导致测试流程中断或偏离
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Model Limitations -->
          <div id="model-limitations" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <i class="fas fa-cogs text-blue-600 mr-3"></i>
              本地30B模型能力局限：规划与记忆保持的挑战
            </h3>
            <div class="bg-white rounded-xl p-8 shadow-lg border border-slate-200">
              <blockquote class="border-l-4 border-blue-500 pl-6 italic text-lg text-slate-700 mb-6">
                "尽管30B参数的模型在许多自然语言处理任务上表现出色，但在处理需要长期规划和精确记忆保持的复杂任务时，其能力仍然存在局限性。"
              </blockquote>
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="bg-slate-50 rounded-lg p-6">
                  <h4 class="font-semibold text-slate-800 mb-3">推理深度限制</h4>
                  <p class="text-sm text-slate-600">与更大规模模型相比，30B模型在推理深度和指令遵循精确性上较弱</p>
                </div>
                <div class="bg-slate-50 rounded-lg p-6">
                  <h4 class="font-semibold text-slate-800 mb-3">长程依赖捕捉</h4>
                  <p class="text-sm text-slate-600">对长程依赖关系的捕捉能力不足，影响对"大局"的把握</p>
                </div>
                <div class="bg-slate-50 rounded-lg p-6">
                  <h4 class="font-semibold text-slate-800 mb-3">动态适应困难</h4>
                  <p class="text-sm text-slate-600">缺乏稳定、持久的外部记忆系统，难以从经验中学习</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Memory Strategy Issues -->
          <div id="memory-strategy" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <i class="fas fa-database text-blue-600 mr-3"></i>
              现有记忆策略的不足：简单存储无法替代动态推理
            </h3>
            <div class="bg-white rounded-xl p-8 shadow-lg border border-slate-200">
              <p class="text-slate-700 mb-6">
                为了解决上下文窗口的限制，开发者通常会尝试引入外部记忆系统。然而，简单的记忆策略往往难以满足复杂UI测试的需求。
              </p>

              <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-red-800 mb-3">常见问题模式</h4>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="fas fa-times-circle text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">上下文消耗：</strong>
                      <span class="text-red-700">将所有历史信息都塞入提示，迅速消耗宝贵上下文窗口</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-times-circle text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">效率低下：</strong>
                      <span class="text-red-700">模型需从冗长历史记录中筛选相关信息，效率低且易出错</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-times-circle text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">自主性剥夺：</strong>
                      <span class="text-red-700">模型变成简单脚本执行器，失去根据实时反馈决策的能力</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h4 class="font-semibold text-blue-800 mb-3">根本矛盾</h4>
                <p class="text-blue-700">
                  用户期望的自主性，如"元素查找失败时等待并重试"，在这种僵化的记忆策略下很难实现，因为模型的决策空间被严格限制在预设的步骤之内。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Memory Optimization -->
      <section id="memory-optimization" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案一：优化记忆管理策略</h2>

          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              为了克服本地30B模型在长步骤UI测试中因上下文窗口限制而导致的"遗忘"问题，优化记忆管理策略是首要且关键的步骤。
              需要转向更智能、更高效的记忆系统，在保证模型能够获取足够历史上下文的同时，为模型的自主推理和决策保留充足的"思考空间"。
            </p>
          </div>

          <!-- Long-term Memory Systems -->
          <div id="long-term-memory" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-archive text-blue-600 mr-3"></i>
              引入长期记忆系统
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
              <!-- JSON Storage -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-file-code text-yellow-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">JSON文件存储</h4>
                </div>
                <p class="text-slate-600 mb-4 text-sm">
                  直接且易于实现的持久化存储方案，结构化记录每一步操作和状态信息。
                </p>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">实现简单，无需额外数据库</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-times text-red-500 mr-2"></i>
                    <span class="text-sm text-slate-700">检索效率低，缺乏智能过滤</span>
                  </div>
                </div>
              </div>

              <!-- Vector Store -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-vector-square text-blue-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">向量数据库</h4>
                </div>
                <p class="text-slate-600 mb-4 text-sm">
                  通过嵌入模型将文本转换为向量，支持基于语义的智能检索。
                </p>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">语义理解，智能检索</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">高效相似度搜索</span>
                  </div>
                </div>
              </div>

              <!-- A-Mem -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-brain text-purple-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">A-Mem高级系统</h4>
                </div>
                <p class="text-slate-600 mb-4 text-sm">
                  灵感来源于Zettelkasten，实现动态自我组织的记忆网络。
                </p>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">动态记忆连接</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">上下文长度缩减85%</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-slate-50 rounded-xl p-6">
              <h4 class="font-semibold text-slate-800 mb-4">A-Mem核心机制</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                  <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-pencil-alt text-blue-600 text-xl"></i>
                  </div>
                  <h5 class="font-medium text-slate-800">笔记构建</h5>
                  <p class="text-sm text-slate-600">将观察和思考转化为原子化笔记</p>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-link text-green-600 text-xl"></i>
                  </div>
                  <h5 class="font-medium text-slate-800">链接生成</h5>
                  <p class="text-sm text-slate-600">分析笔记间的潜在联系并生成链接</p>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-sync-alt text-purple-600 text-xl"></i>
                  </div>
                  <h5 class="font-medium text-slate-800">记忆演化</h5>
                  <p class="text-sm text-slate-600">不断精炼和深化记忆网络</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Prompt Engineering -->
          <div id="prompt-engineering" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-terminal text-blue-600 mr-3"></i>
              优化提示工程（Prompt Engineering）
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <p class="text-slate-700 mb-6">
                在有限的上下文窗口内，如何向LLM传递最有效、最相关的信息，是决定其决策质量的关键。优化提示工程正是实现这一目标的核心技术。
              </p>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">动态构建提示</h4>
                  <div class="space-y-3">
                    <div class="bg-blue-50 rounded-lg p-4">
                      <h5 class="font-medium text-blue-800 mb-2">当前步骤指令</h5>
                      <p class="text-sm text-blue-700">清晰、明确的参数和期望结果</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                      <h5 class="font-medium text-green-800 mb-2">历史记录摘要</h5>
                      <p class="text-sm text-green-700">高度压缩的关键信息，如"已成功完成步骤1-5"</p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                      <h5 class="font-medium text-purple-800 mb-2">高层目标</h5>
                      <p class="text-sm text-purple-700">反复强调最终测试目的</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">结构化信息展示</h4>
                  <div class="bg-slate-100 rounded-lg p-4">
                    <pre class="text-sm text-slate-700 overflow-x-auto"><code>| 参数名   | 值               | 来源   | 描述                  |
|----------|------------------|--------|-----------------------|
| `udid`   | `"{device_udid}"`| 动态   | 目标设备唯一标识符     |
| `x`      | `"{element_x}"`  | 动态   | 目标元素X坐标          |
| `y`      | `"{element_y}"`  | 动态   | 目标元素Y坐标          |
| `action` | `tap`            | 静态   | 执行的操作类型         |</code></pre>
                  </div>
                  <p class="text-sm text-slate-600 mt-3">
                    使用Markdown表格清晰展示参数与步骤，降低模型解析信息的认知负荷。
                  </p>
                </div>
              </div>

              <div class="mt-8 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
                <h4 class="font-semibold text-green-800 mb-3 flex items-center">
                  <i class="fas fa-compress-alt mr-2"></i>
                  "滚动摘要"机制
                </h4>
                <p class="text-green-700">
                  定期调用LLM将最近的历史记录压缩成简短摘要，替代原始的多条记录，有效控制上下文长度，为模型提供更高层次的抽象视图。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Multi-Model Collaboration -->
      <section id="multi-model" class="py-16 bg-slate-50">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案二：采用多模型协作架构</h2>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center mb-8">
              当单一模型难以同时胜任高层规划、细节执行和记忆保持等多重任务时，采用多模型协作架构成为一种极具吸引力的解决方案。
              通过专业化分工，不仅可以克服单一模型的能力瓶颈，还能构建出更具鲁棒性和灵活性的系统。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div class="text-center space-y-4">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-sitemap text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-800">规划专家</h3>
                <p class="text-sm text-slate-600">负责高层次的战略规划和决策制定</p>
              </div>
              <div class="text-center space-y-4">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-tools text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-800">执行专家</h3>
                <p class="text-sm text-slate-600">专注于具体工具调用和精确执行</p>
              </div>
              <div class="text-center space-y-4">
                <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-eye text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-800">感知专家</h3>
                <p class="text-sm text-slate-600">提供强大的视觉理解和UI分析能力</p>
              </div>
            </div>
          </div>

          <!-- Factored Agent -->
          <div id="factored-agent" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-project-diagram text-blue-600 mr-3"></i>
              因子化代理（Factored Agent）模式
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <div class="mb-8">
                <blockquote class="border-l-4 border-blue-500 pl-6 italic text-lg text-slate-700">
                  "因子化代理旨在解决LLM-Agent系统中规划与知识记忆之间的冲突。其核心思想源于一个观察：在单一模型中同时训练上下文学习和固定知识记忆会导致模型泛化能力的下降。"
                  <a href="https://blog.csdn.net/qq_36671160/article/details/147669282" class="citation ml-2" target="_blank">[103]</a>
                </blockquote>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Planner Agent -->
                <div class="bg-blue-50 rounded-xl p-6">
                  <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <i class="fas fa-brain text-blue-600 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">规划代理（大模型）</h4>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-medium text-blue-800 mb-2">核心职责</h5>
                      <p class="text-sm text-blue-700">负责高层计划与决策，理解用户请求和测试目标</p>
                    </div>
                    <div>
                      <h5 class="font-medium text-blue-800 mb-2">工作方式</h5>
                      <ul class="text-sm text-blue-700 space-y-1">
                        <li>• 根据实时反馈动态决定下一步行动</li>
                        <li>• 生成高层次的描述性"意图"</li>
                        <li>• 不直接处理具体API调用格式</li>
                      </ul>
                    </div>
                    <div class="bg-blue-100 rounded-lg p-3">
                      <p class="text-xs text-blue-800">
                        <strong>示例输出：</strong>"点击屏幕左上角显示当前地址的文本"
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Tool Agent -->
                <div class="bg-green-50 rounded-xl p-6">
                  <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                      <i class="fas fa-wrench text-green-600 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">工具代理（小模型）</h4>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-medium text-green-800 mb-2">核心职责</h5>
                      <p class="text-sm text-green-700">负责具体工具调用与执行，作为专门的"记忆库"和"执行器"</p>
                    </div>
                    <div>
                      <h5 class="font-medium text-green-800 mb-2">工作方式</h5>
                      <ul class="text-sm text-green-700 space-y-1">
                        <li>• 接收高层次"意图"</li>
                        <li>• 转换为严格的工具调用格式</li>
                        <li>• 确保参数精确性和完整性</li>
                      </ul>
                    </div>
                    <div class="bg-green-100 rounded-lg p-3">
                      <p class="text-xs text-green-800">
                        <strong>示例输出：</strong>JSON格式的find_element和tap工具参数
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AutoGen Framework -->
          <div id="autogen-framework" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-users text-blue-600 mr-3"></i>
              多智能体协作框架（如AutoGen）
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <p class="text-slate-700 mb-6">
                当任务复杂度进一步提升时，引入功能更全面的多智能体协作框架，如微软开发的AutoGen，成为理想的选择。
                <a href="https://zhuanlan.zhihu.com/p/705099162" class="citation" target="_blank">[334]</a>
                <a href="https://zhuanlan.zhihu.com/p/705583812" class="citation" target="_blank">[350]</a>
              </p>

              <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Coordinator -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6">
                  <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i class="fas fa-chess-king text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">"大脑"协调者</h4>
                    <p class="text-sm text-slate-600">使用云端大模型（如GPT-4）</p>
                  </div>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-blue-800 text-sm mb-1">核心功能</h5>
                      <ul class="text-xs text-blue-700 space-y-1">
                        <li>• 接收和理解测试计划</li>
                        <li>• 动态分配子任务</li>
                        <li>• 管理对话记忆</li>
                        <li>• 协调代理间协作</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Executor -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6">
                  <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i class="fas fa-robot text-green-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">本地"执行者"</h4>
                    <p class="text-sm text-slate-600">30B模型专注具体操作</p>
                  </div>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-green-800 text-sm mb-1">专业化分工</h5>
                      <ul class="text-xs text-green-700 space-y-1">
                        <li>• TapExecutor代理</li>
                        <li>• TextValidator代理</li>
                        <li>• 职责单一，提示简洁</li>
                        <li>• 最小化上下文占用</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Visual Model -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6">
                  <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i class="fas fa-eye text-purple-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">视觉模型"眼睛"</h4>
                    <p class="text-sm text-slate-600">GPT-4V识别UI元素</p>
                  </div>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-purple-800 text-sm mb-1">视觉理解</h5>
                      <ul class="text-xs text-purple-700 space-y-1">
                        <li>• 分析屏幕截图</li>
                        <li>• 返回元素精确坐标</li>
                        <li>• 理解布局和状态</li>
                        <li>• 端到端视觉理解</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-8 bg-slate-50 rounded-lg p-6">
                <h4 class="font-semibold text-slate-800 mb-4">实际应用案例</h4>
                <blockquote class="border-l-4 border-slate-400 pl-4 italic text-slate-700">
                  "京东云技术团队探索使用GPT-4V作为'眼睛'，Playwright作为'手'，通过AutoGen和GPT-4进行协同调度，完成自动化UI测试任务。"
                  <a href="https://juejin.cn/post/7316592794109198387" class="citation ml-2" target="_blank">[346]</a>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- State Machine Solution -->
      <section id="state-machine" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案三：引入状态机（State Machine）进行流程控制</h2>

          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              引入状态机是一种更为结构化和工程化的解决方案。状态机的核心思想是将复杂的测试计划分解为一系列离散的、定义明确的状态，
              并通过预定义的规则来管理这些状态之间的流转。这种方法将宏观的流程控制逻辑从模型的即时决策中分离出来，
              由状态机引擎负责确保测试严格按照预定路径执行。
            </p>
          </div>

          <!-- State Machine Core -->
          <div id="state-core" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-flow-chart text-blue-600 mr-3"></i>
              状态机模式的核心思想
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <img src="https://kimi-web-img.moonshot.cn/img/tva1.sinaimg.cn/7e43885360a487841bc856adf659e73ed3a29f45.jpg" alt="抽象状态机流程图" class="w-full rounded-lg mb-4" size="medium" aspect="wide" query="状态机流程图 抽象" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>
                <h4 class="text-lg font-semibold text-slate-800 mb-3">状态分解优势</h4>
                <p class="text-slate-700 text-sm">
                  将复杂的、长序列的测试任务分解为一系列离散的、可管理的"状态"，
                  每个状态代表测试流程中的一个特定阶段或一组相关操作。
                </p>
              </div>

              <div class="space-y-6">
                <div class="bg-blue-50 rounded-xl p-6">
                  <h4 class="font-semibold text-blue-800 mb-3 flex items-center">
                    <i class="fas fa-cog mr-2"></i>
                    减轻认知负担
                  </h4>
                  <p class="text-blue-700 text-sm">
                    LLM不再需要记住整个20步测试计划，只需专注于当前状态的局部任务，
                    极大降低因上下文窗口限制导致的"遗忘"问题。
                  </p>
                </div>

                <div class="bg-green-50 rounded-xl p-6">
                  <h4 class="font-semibold text-green-800 mb-3 flex items-center">
                    <i class="fas fa-shield-alt mr-2"></i>
                    提高可控性
                  </h4>
                  <p class="text-green-700 text-sm">
                    通过预定义状态转换规则，确保测试按既定顺序执行，
                    避免模型因自主决策而偏离主路径的风险。
                  </p>
                </div>

                <div class="bg-purple-50 rounded-xl p-6">
                  <h4 class="font-semibold text-purple-800 mb-3 flex items-center">
                    <i class="fas fa-puzzle-piece mr-2"></i>
                    增强模块化
                  </h4>
                  <p class="text-purple-700 text-sm">
                    每个状态可独立设计、测试和优化，不影响系统其他部分，
                    使代码更易于扩展和调试。
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <h4 class="text-lg font-semibold text-slate-800 mb-6">测试计划状态分解示例</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-play text-blue-600"></i>
                  </div>
                  <h5 class="font-medium text-blue-800 text-sm">INITIALIZE</h5>
                  <p class="text-xs text-blue-600">初始化测试环境</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-search text-green-600"></i>
                  </div>
                  <h5 class="font-medium text-green-800 text-sm">NAVIGATE</h5>
                  <p class="text-xs text-green-600">导航到目标页面</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-check-circle text-yellow-600"></i>
                  </div>
                  <h5 class="font-medium text-yellow-800 text-sm">VALIDATE</h5>
                  <p class="text-xs text-yellow-600">验证页面元素</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-mouse-pointer text-purple-600"></i>
                  </div>
                  <h5 class="font-medium text-purple-800 text-sm">PERFORM</h5>
                  <p class="text-xs text-purple-600">执行操作</p>
                </div>
              </div>

              <div class="bg-slate-50 rounded-lg p-4">
                <h5 class="font-medium text-slate-800 mb-2">状态转移控制</h5>
                <p class="text-sm text-slate-700">
                  状态机通过定义明确的状态转移来确保整体流程的正确性。
                  转移条件基于前一个状态的执行结果，如成功、失败或需要重试。
                </p>
              </div>
            </div>
          </div>

          <!-- Implementation -->
          <div id="state-implementation" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-code text-blue-600 mr-3"></i>
              实现方案
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Custom Implementation -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                  <i class="fas fa-tools text-blue-600 mr-2"></i>
                  使用Python自行实现
                </h4>
                <p class="text-slate-700 mb-4 text-sm">
                  围绕核心循环和状态字典构建，提供最大灵活性和定制能力。
                </p>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-green-800">优点：</strong>
                      <span class="text-green-700 text-sm">灵活轻量，不依赖外部库</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-times text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">缺点：</strong>
                      <span class="text-red-700 text-sm">需自行处理并发、持久化等复杂问题</span>
                    </div>
                  </div>
                </div>
                <div class="bg-slate-100 rounded-lg p-3 mt-4">
                  <p class="text-xs text-slate-700">
                    <strong>核心组件：</strong>State基类、execute方法、状态字典、主控制器循环
                  </p>
                </div>
              </div>

              <!-- LangGraph Framework -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                  <i class="fas fa-sitemap text-purple-600 mr-2"></i>
                  采用LangGraph框架
                </h4>
                <p class="text-slate-700 mb-4 text-sm">
                  专门为构建LLM驱动的、有状态的、多Agent应用而设计的强大框架。
                  <a href="https://gacfox.com/notes/%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B/Python/LangChain/07-LangGraph%E7%8A%B6%E6%80%81%E6%9C%BA%E6%A1%86%E6%9E%B6/07-LangGraph%E7%8A%B6%E6%80%81%E6%9C%BA%E6%A1%86%E6%9E%B6.md" class="citation text-xs" target="_blank">[419]</a>
                </p>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="fas fa-star text-yellow-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-yellow-800">特点：</strong>
                      <span class="text-yellow-700 text-sm">图结构，条件边，内置记忆管理</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-rocket text-blue-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-blue-800">优势：</strong>
                      <span class="text-blue-700 text-sm">快速开发，减少底层复杂性</span>
                    </div>
                  </div>
                </div>
                <div class="bg-purple-50 rounded-lg p-3 mt-4">
                  <p class="text-xs text-purple-700">
                    <strong>核心概念：</strong>节点（Node）、边（Edge）、状态（State）、图（Graph）
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Autonomy Implementation -->
      <section id="autonomy" class="py-16 bg-slate-50">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案四：自主性实现机制</h2>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              在确保测试计划严格执行的同时，赋予Agent适度的自主决策能力，是实现真正智能化测试的关键。
              这种自主性主要体现在异常处理、错误恢复和动态适应等方面。
            </p>
          </div>

          <!-- Error Handling -->
          <div id="error-handling" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-exclamation-triangle text-blue-600 mr-3"></i>
              智能错误处理与恢复
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">基于规则的自动重试</h4>
                <div class="space-y-4">
                  <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                    <h5 class="font-semibold text-green-800 mb-2">元素查找失败</h5>
                    <ul class="text-sm text-green-700 space-y-1">
                      <li>• 等待3-5秒后重试</li>
                      <li>• 最多重试3次</li>
                      <li>• 检查页面是否发生变化</li>
                    </ul>
                  </div>
                  <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h5 class="font-semibold text-blue-800 mb-2">网络延迟处理</h5>
                    <ul class="text-sm text-blue-700 space-y-1">
                      <li>• 动态调整等待时间</li>
                      <li>• 监控页面加载状态</li>
                      <li>• 智能超时机制</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">LLM驱动的复杂决策</h4>
                <div class="space-y-4">
                  <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <h5 class="font-semibold text-purple-800 mb-2">上下文分析</h5>
                    <ul class="text-sm text-purple-700 space-y-1">
                      <li>• 分析当前页面状态</li>
                      <li>• 理解错误发生原因</li>
                      <li>• 评估可能的解决方案</li>
                    </ul>
                  </div>
                  <div class="bg-orange-50 border border-orange-200 rounded-lg p-4">
                    <h5 class="font-semibold text-orange-800 mb-2">动态策略调整</h5>
                    <ul class="text-sm text-orange-700 space-y-1">
                      <li>• 选择替代操作路径</li>
                      <li>• 调整操作参数</li>
                      <li>• 决定是否继续或终止</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Early Termination -->
          <div id="early-termination" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-stop-circle text-blue-600 mr-3"></i>
              智能提前终止机制
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <p class="text-slate-700 mb-6">
                当遇到无法恢复的错误或达到预设的终止条件时，Agent应能够智能地决定是否提前终止测试，
                并生成详细的错误报告和建议。
              </p>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="text-center">
                  <div class="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-times text-red-600 text-2xl"></i>
                  </div>
                  <h4 class="font-semibold text-slate-800 mb-2">致命错误检测</h4>
                  <p class="text-sm text-slate-600">识别无法恢复的系统错误或应用崩溃</p>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-clock text-yellow-600 text-2xl"></i>
                  </div>
                  <h4 class="font-semibold text-slate-800 mb-2">超时保护</h4>
                  <p class="text-sm text-slate-600">防止测试无限期挂起，设置合理的全局超时</p>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-file-alt text-blue-600 text-2xl"></i>
                  </div>
                  <h4 class="font-semibold text-slate-800 mb-2">详细报告</h4>
                  <p class="text-sm text-slate-600">生成包含错误分析和改进建议的完整报告</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- UI Perception Enhancement -->
      <section id="ui-perception" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案五：UI感知能力增强</h2>

          <div class="bg-gradient-to-r from-purple-50 to-pink-50 rounded-2xl p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              强化Agent对移动应用界面的理解和感知能力，是提高测试准确性和鲁棒性的重要手段。
              通过集成OCR技术和多模态模型，Agent能够更好地理解UI元素和页面状态。
            </p>
          </div>

          <!-- OCR Integration -->
          <div id="ocr-integration" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-text-width text-blue-600 mr-3"></i>
              OCR技术集成
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">文本识别与验证</h4>
                <div class="space-y-4">
                  <div class="bg-blue-50 rounded-lg p-4">
                    <h5 class="font-medium text-blue-800 mb-2">精确文本匹配</h5>
                    <p class="text-sm text-blue-700">识别页面中的特定文本内容，验证UI状态</p>
                  </div>
                  <div class="bg-green-50 rounded-lg p-4">
                    <h5 class="font-medium text-green-800 mb-2">模糊匹配算法</h5>
                    <p class="text-sm text-green-700">处理字体渲染差异和OCR识别误差</p>
                  </div>
                  <div class="bg-purple-50 rounded-lg p-4">
                    <h5 class="font-medium text-purple-800 mb-2">多语言支持</h5>
                    <p class="text-sm text-purple-700">支持中英文混合识别，适应国际化应用</p>
                  </div>
                </div>
              </div>

              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">元素定位增强</h4>
                <div class="space-y-4">
                  <div class="bg-orange-50 rounded-lg p-4">
                    <h5 class="font-medium text-orange-800 mb-2">文本坐标映射</h5>
                    <p class="text-sm text-orange-700">将识别的文本与屏幕坐标精确对应</p>
                  </div>
                  <div class="bg-teal-50 rounded-lg p-4">
                    <h5 class="font-medium text-teal-800 mb-2">区域边界检测</h5>
                    <p class="text-sm text-teal-700">识别可点击区域的准确边界</p>
                  </div>
                  <div class="bg-red-50 rounded-lg p-4">
                    <h5 class="font-medium text-red-800 mb-2">动态适应</h5>
                    <p class="text-sm text-red-700">适应不同屏幕尺寸和分辨率</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Multimodal Models -->
          <div id="multimodal-models" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-eye text-blue-600 mr-3"></i>
              多模态模型应用
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <p class="text-slate-700 mb-6">
                利用GPT-4V等多模态大模型的视觉理解能力，实现更智能的UI分析和元素识别。
              </p>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">视觉理解能力</h4>
                  <ul class="space-y-3">
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">理解UI布局和层次结构</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">识别按钮、输入框等UI组件</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">分析页面状态和内容变化</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">生成自然语言的页面描述</span>
                    </li>
                  </ul>
                </div>

                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">实际应用场景</h4>
                  <ul class="space-y-3">
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-blue-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">页面异常检测和分析</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-blue-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">复杂UI元素的智能定位</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-blue-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">测试结果的视觉验证</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-blue-500 mr-2 mt-1"></i>
                      <span class="text-slate-700">自动化测试报告生成</span>
                    </li>
                  </ul>
                </div>
              </div>

              <div class="mt-8 bg-gradient-to-r from-blue-50 to-purple-50 rounded-lg p-6">
                <h4 class="font-semibold text-slate-800 mb-3">技术实现路径</h4>
                <p class="text-slate-700 text-sm">
                  通过API调用GPT-4V等多模态模型，将屏幕截图作为输入，获取结构化的UI分析结果。
                  结合传统的元素定位方法，形成多层次的UI感知体系，提高测试的准确性和可靠性。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Chapter 2 Divider -->
      <div class="chapter-divider">
        <div class="max-w-7xl mx-auto px-4 sm:px-6">
          <h1 class="chapter-title">第二章</h1>
          <p class="chapter-subtitle">技术架构与实现方案</p>
        </div>
      </div>

      <!-- Chapter 2 Content starts here with page2-content class for styling -->
      <div class="page2-content">
        <!-- Overview Section -->
        <section id="overview" class="py-16 bg-white">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>核心问题与解决方案概述</h2>
            </div>

            <div class="callout">
              <h3 class="text-xl font-semibold mb-4">问题核心</h3>
              <p class="mb-4">
                在使用本地30B模型执行长步骤UI测试时，面临的核心挑战是如何在严格遵循测试计划的同时，保持模型的自主决策能力。
                传统方法要么过于僵化失去灵活性，要么过于自由导致偏离计划。
              </p>
              <p>
                我们的解决方案通过<strong>状态机解耦</strong>的方式，将宏观的流程控制与微观的智能决策分离，
                实现了"宏观控制，微观自主"的平衡架构。
              </p>
            </div>

            <div class="architecture-grid">
              <div class="architecture-card">
                <h3 class="text-xl font-semibold mb-3 text-blue-600">
                  <i class="fas fa-sitemap mr-2"></i>
                  状态机控制层
                </h3>
                <p class="text-gray-700 mb-3">
                  负责整体流程的宏观控制，确保测试严格按照预定计划执行，管理状态转换和异常处理。
                </p>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 预定义测试步骤序列</li>
                  <li>• 状态转换规则管理</li>
                  <li>• 全局异常处理机制</li>
                </ul>
              </div>

              <div class="architecture-card">
                <h3 class="text-xl font-semibold mb-3 text-green-600">
                  <i class="fas fa-brain mr-2"></i>
                  LLM决策层
                </h3>
                <p class="text-gray-700 mb-3">
                  在单个状态内进行智能决策，处理具体的UI交互和异常情况，保持必要的自主性。
                </p>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 元素定位与交互</li>
                  <li>• 实时异常处理</li>
                  <li>• 动态参数调整</li>
                </ul>
              </div>

              <div class="architecture-card">
                <h3 class="text-xl font-semibold mb-3 text-purple-600">
                  <i class="fas fa-tools mr-2"></i>
                  工具执行层
                </h3>
                <p class="text-gray-700 mb-3">
                  提供标准化的设备操作接口，封装底层的设备驱动和API调用，确保操作的一致性。
                </p>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 设备驱动管理</li>
                  <li>• API调用封装</li>
                  <li>• 操作结果反馈</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <div class="section-divider"></div>

        <!-- Architecture Section -->
        <section id="architecture" class="py-16 bg-slate-50">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>整体架构设计</h2>
            </div>

            <div class="callout">
              <p>
                我们的架构采用分层设计思想，通过清晰的职责分离实现系统的高内聚、低耦合。
                每一层都有明确的功能边界和接口定义，便于独立开发、测试和维护。
              </p>
            </div>

            <!-- Architecture Diagram Placeholder -->
            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8 mb-8">
              <h3 class="text-xl font-semibold mb-6 text-center">系统架构图</h3>
              <div class="bg-gradient-to-br from-blue-50 to-purple-50 rounded-lg p-8 min-h-96 flex items-center justify-center">
                <div class="text-center space-y-4">
                  <i class="fas fa-layer-group text-6xl text-blue-500"></i>
                  <p class="text-lg font-medium text-gray-700">分层架构设计</p>
                  <p class="text-sm text-gray-600">状态机控制 → LLM决策 → 工具执行</p>
                </div>
              </div>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div class="space-y-6">
                <h3 class="text-xl font-semibold text-gray-800">核心设计原则</h3>

                <div class="highlight-box">
                  <h4 class="font-semibold text-blue-800 mb-2">
                    <i class="fas fa-balance-scale mr-2"></i>
                    平衡性原则
                  </h4>
                  <p class="text-gray-700 text-sm">
                    在严格的流程控制与灵活的智能决策之间找到最佳平衡点，
                    既保证测试计划的完整执行，又允许模型在局部范围内进行自主优化。
                  </p>
                </div>

                <div class="highlight-box">
                  <h4 class="font-semibold text-green-800 mb-2">
                    <i class="fas fa-puzzle-piece mr-2"></i>
                    模块化原则
                  </h4>
                  <p class="text-gray-700 text-sm">
                    每个组件都有清晰的功能边界和标准化接口，
                    支持独立开发、测试和部署，便于系统的扩展和维护。
                  </p>
                </div>

                <div class="highlight-box">
                  <h4 class="font-semibold text-purple-800 mb-2">
                    <i class="fas fa-shield-alt mr-2"></i>
                    鲁棒性原则
                  </h4>
                  <p class="text-gray-700 text-sm">
                    通过多层次的异常处理和容错机制，
                    确保系统在面对各种异常情况时能够优雅降级或自动恢复。
                  </p>
                </div>
              </div>

              <div class="space-y-6">
                <h3 class="text-xl font-semibold text-gray-800">技术特色</h3>

                <div class="bg-white rounded-lg p-6 shadow-md border border-gray-200">
                  <h4 class="font-semibold text-gray-800 mb-3">状态机驱动</h4>
                  <p class="text-gray-700 text-sm mb-3">
                    采用有限状态机模式管理测试流程，每个状态对应一个具体的测试步骤或阶段。
                  </p>
                  <div class="bg-blue-50 rounded p-3">
                    <p class="text-xs text-blue-700">
                      状态转换基于明确的条件和规则，确保流程的可预测性和可控性。
                    </p>
                  </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-md border border-gray-200">
                  <h4 class="font-semibold text-gray-800 mb-3">智能决策</h4>
                  <p class="text-gray-700 text-sm mb-3">
                    在每个状态内，LLM可以根据实时的UI状态和反馈信息进行智能决策。
                  </p>
                  <div class="bg-green-50 rounded p-3">
                    <p class="text-xs text-green-700">
                      支持动态参数调整、异常处理和替代方案选择。
                    </p>
                  </div>
                </div>

                <div class="bg-white rounded-lg p-6 shadow-md border border-gray-200">
                  <h4 class="font-semibold text-gray-800 mb-3">工具抽象</h4>
                  <p class="text-gray-700 text-sm mb-3">
                    通过标准化的工具接口，屏蔽底层设备差异和API复杂性。
                  </p>
                  <div class="bg-purple-50 rounded p-3">
                    <p class="text-xs text-purple-700">
                      支持多种设备类型和操作系统，便于扩展和适配。
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <div class="section-divider"></div>

        <!-- Core Modules Section -->
        <section id="core-modules" class="py-16 bg-white">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>核心模块详细设计</h2>
            </div>

            <div class="callout">
              <p>
                系统的核心功能通过四个主要模块实现：状态机引擎、LLM决策器、工具执行器和记忆管理器。
                每个模块都有明确的职责分工和标准化的接口设计。
              </p>
            </div>

            <!-- State Machine Engine -->
            <div class="mb-12">
              <h3 class="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-cogs text-blue-600 mr-3"></i>
                状态机引擎（State Machine Engine）
              </h3>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h4 class="text-lg font-semibold text-gray-800 mb-4">核心职责</h4>
                  <ul class="space-y-3">
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-gray-700">管理测试流程的整体执行顺序</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-gray-700">控制状态之间的转换条件和规则</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-gray-700">处理全局异常和错误恢复</span>
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-check-circle text-green-500 mr-2 mt-1"></i>
                      <span class="text-gray-700">维护测试执行的上下文信息</span>
                    </li>
                  </ul>
                </div>

                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h4 class="text-lg font-semibold text-gray-800 mb-4">关键特性</h4>
                  <div class="space-y-4">
                    <div class="bg-blue-50 rounded-lg p-4">
                      <h5 class="font-medium text-blue-800 mb-2">状态定义</h5>
                      <p class="text-sm text-blue-700">每个状态包含明确的入口条件、执行逻辑和出口条件</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                      <h5 class="font-medium text-green-800 mb-2">转换控制</h5>
                      <p class="text-sm text-green-700">基于执行结果和预定义规则进行状态转换</p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                      <h5 class="font-medium text-purple-800 mb-2">异常处理</h5>
                      <p class="text-sm text-purple-700">支持重试、跳过、回退等多种异常处理策略</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-6 bg-gray-50 rounded-lg p-6">
                <h4 class="font-semibold text-gray-800 mb-3">状态机配置示例</h4>
                <div class="code-block">
                  <pre><code>{
  "states": {
    "INIT": {
      "description": "初始化测试环境",
      "next_states": ["NAVIGATE", "ERROR"],
      "max_retries": 3,
      "timeout": 30
    },
    "NAVIGATE": {
      "description": "导航到目标页面",
      "next_states": ["VALIDATE", "RETRY_NAVIGATE", "ERROR"],
      "max_retries": 2,
      "timeout": 15
    },
    "VALIDATE": {
      "description": "验证页面元素",
      "next_states": ["PERFORM_ACTION", "ERROR"],
      "max_retries": 1,
      "timeout": 10
    }
  }
}</code></pre>
                </div>
              </div>
            </div>

            <!-- LLM Decision Engine -->
            <div class="mb-12">
              <h3 class="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-brain text-green-600 mr-3"></i>
                LLM决策器（LLM Decision Engine）
              </h3>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h4 class="text-lg font-semibold text-gray-800 mb-4">智能决策能力</h4>
                  <div class="space-y-4">
                    <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                      <h5 class="font-semibold text-blue-800 mb-2">上下文理解</h5>
                      <p class="text-sm text-blue-700">
                        分析当前UI状态、历史操作记录和测试目标，
                        理解当前所处的测试阶段和需要执行的操作。
                      </p>
                    </div>
                    <div class="bg-green-50 border border-green-200 rounded-lg p-4">
                      <h5 class="font-semibold text-green-800 mb-2">参数推理</h5>
                      <p class="text-sm text-green-700">
                        根据UI元素的实际位置和属性，
                        动态计算操作参数，如坐标、文本内容等。
                      </p>
                    </div>
                    <div class="bg-purple-50 border border-purple-200 rounded-lg p-4">
                      <h5 class="font-semibold text-purple-800 mb-2">异常应对</h5>
                      <p class="text-sm text-purple-700">
                        当遇到预期外的情况时，
                        能够分析原因并选择合适的应对策略。
                      </p>
                    </div>
                  </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h4 class="text-lg font-semibold text-gray-800 mb-4">工作流程</h4>
                  <div class="space-y-3">
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-blue-600 font-semibold text-sm">1</span>
                      </div>
                      <span class="text-gray-700">接收状态机传递的任务指令</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-green-600 font-semibold text-sm">2</span>
                      </div>
                      <span class="text-gray-700">分析当前UI状态和上下文信息</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-purple-600 font-semibold text-sm">3</span>
                      </div>
                      <span class="text-gray-700">生成具体的工具调用指令</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-orange-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-orange-600 font-semibold text-sm">4</span>
                      </div>
                      <span class="text-gray-700">监控执行结果并处理异常</span>
                    </div>
                    <div class="flex items-center">
                      <div class="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center mr-3">
                        <span class="text-red-600 font-semibold text-sm">5</span>
                      </div>
                      <span class="text-gray-700">向状态机报告执行状态</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <!-- Memory Manager -->
            <div class="mb-12">
              <h3 class="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-database text-purple-600 mr-3"></i>
                记忆管理器（Memory Manager）
              </h3>

              <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
                <p class="text-gray-700 mb-6">
                  记忆管理器负责维护测试执行过程中的关键信息，
                  为LLM决策器提供必要的历史上下文，同时控制信息量以避免上下文窗口溢出。
                </p>

                <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                  <div class="text-center">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i class="fas fa-history text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">操作历史</h4>
                    <p class="text-sm text-gray-600">记录已执行的操作和结果，支持回溯和分析</p>
                  </div>
                  <div class="text-center">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i class="fas fa-compress-alt text-green-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">智能压缩</h4>
                    <p class="text-sm text-gray-600">动态压缩历史信息，保留关键内容</p>
                  </div>
                  <div class="text-center">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <i class="fas fa-search text-purple-600 text-2xl"></i>
                    </div>
                    <h4 class="font-semibold text-gray-800 mb-2">相关性检索</h4>
                    <p class="text-sm text-gray-600">根据当前任务检索最相关的历史信息</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <div class="section-divider"></div>

        <!-- Tools and API Server Section -->
        <section id="tools-api" class="py-16 bg-slate-50">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>工具执行与API Server模块</h2>
            </div>

            <div class="callout">
              <p>
                工具执行层是系统与实际设备交互的核心组件，通过标准化的API接口和封装好的Python函数，
                提供统一的设备操作能力。本模块基于现有的设备驱动管理工具进行优化和扩展。
              </p>
            </div>

            <!-- Tool Executor -->
            <div class="mb-12">
              <h3 class="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-tools text-orange-600 mr-3"></i>
                工具执行器（Tool Executor）
              </h3>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h4 class="text-lg font-semibold text-gray-800 mb-4">核心功能</h4>
                  <div class="space-y-4">
                    <div class="bg-blue-50 rounded-lg p-4">
                      <h5 class="font-medium text-blue-800 mb-2">设备管理</h5>
                      <p class="text-sm text-blue-700">
                        基于现有的设备驱动管理工具，提供设备连接、状态监控和资源管理功能。
                        支持iOS和Android设备的统一管理。
                      </p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                      <h5 class="font-medium text-green-800 mb-2">操作封装</h5>
                      <p class="text-sm text-green-700">
                        将底层的设备操作API封装为标准化的Python函数，
                        提供统一的接口规范和参数格式。
                      </p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                      <h5 class="font-medium text-purple-800 mb-2">结果反馈</h5>
                      <p class="text-sm text-purple-700">
                        实时监控操作执行状态，提供详细的成功/失败反馈信息，
                        支持异常情况的自动检测和报告。
                      </p>
                    </div>
                  </div>
                </div>

                <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                  <h4 class="text-lg font-semibold text-gray-800 mb-4">标准化工具函数</h4>
                  <div class="space-y-3">
                    <div class="bg-gray-50 rounded p-3">
                      <h5 class="font-medium text-gray-800 text-sm mb-1">设备操作类</h5>
                      <ul class="text-xs text-gray-600 space-y-1">
                        <li>• <code>tap(udid, x, y)</code> - 点击操作</li>
                        <li>• <code>swipe(udid, x1, y1, x2, y2)</code> - 滑动操作</li>
                        <li>• <code>input_text(udid, text)</code> - 文本输入</li>
                        <li>• <code>take_screenshot(udid)</code> - 截图操作</li>
                      </ul>
                    </div>
                    <div class="bg-gray-50 rounded p-3">
                      <h5 class="font-medium text-gray-800 text-sm mb-1">元素查找类</h5>
                      <ul class="text-xs text-gray-600 space-y-1">
                        <li>• <code>find_element_by_text(udid, text)</code> - 文本查找</li>
                        <li>• <code>find_element_by_id(udid, element_id)</code> - ID查找</li>
                        <li>• <code>wait_for_element(udid, selector)</code> - 等待元素</li>
                      </ul>
                    </div>
                    <div class="bg-gray-50 rounded p-3">
                      <h5 class="font-medium text-gray-800 text-sm mb-1">状态检查类</h5>
                      <ul class="text-xs text-gray-600 space-y-1">
                        <li>• <code>get_device_status(udid)</code> - 设备状态</li>
                        <li>• <code>check_app_state(udid, bundle_id)</code> - 应用状态</li>
                        <li>• <code>verify_page_loaded(udid)</code> - 页面加载检查</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-6 bg-white rounded-lg p-6 shadow-md border border-gray-200">
                <h4 class="font-semibold text-gray-800 mb-3">工具函数调用示例</h4>
                <div class="code-block">
                  <pre><code># 基于现有设备管理工具的标准化封装
from tools.device_driver_manage_tools import DeviceManager

class ToolExecutor:
    def __init__(self):
        self.device_manager = DeviceManager()

    def tap(self, udid: str, x: int, y: int) -> dict:
        """标准化点击操作"""
        try:
            result = self.device_manager.tap_coordinate(udid, x, y)
            return {
                "success": True,
                "action": "tap",
                "coordinates": {"x": x, "y": y},
                "timestamp": time.time(),
                "device_udid": udid
            }
        except Exception as e:
            return {
                "success": False,
                "error": str(e),
                "action": "tap",
                "coordinates": {"x": x, "y": y}
            }

    def find_element_by_text(self, udid: str, text: str) -> dict:
        """基于OCR的文本元素查找"""
        try:
            screenshot = self.device_manager.take_screenshot(udid)
            ocr_result = self.device_manager.ocr_analyze(screenshot)

            for element in ocr_result:
                if text in element['text']:
                    return {
                        "success": True,
                        "element": {
                            "text": element['text'],
                            "coordinates": element['bbox'],
                            "confidence": element['confidence']
                        }
                    }

            return {"success": False, "error": f"Text '{text}' not found"}
        except Exception as e:
            return {"success": False, "error": str(e)}</code></pre>
                </div>
              </div>
            </div>

            <!-- API Server -->
            <div class="mb-12">
              <h3 class="text-2xl font-semibold text-gray-800 mb-6 flex items-center">
                <i class="fas fa-server text-blue-600 mr-3"></i>
                API Server设计
              </h3>

              <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
                <p class="text-gray-700 mb-6">
                  API Server提供RESTful接口，支持远程调用和分布式部署。
                  基于FastAPI框架构建，提供高性能的异步处理能力。
                </p>

                <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                  <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">接口设计</h4>
                    <div class="space-y-3">
                      <div class="bg-blue-50 border border-blue-200 rounded p-3">
                        <h5 class="font-medium text-blue-800 text-sm mb-1">设备管理接口</h5>
                        <ul class="text-xs text-blue-700 space-y-1">
                          <li>• <code>GET /devices</code> - 获取设备列表</li>
                          <li>• <code>POST /devices/{udid}/connect</code> - 连接设备</li>
                          <li>• <code>GET /devices/{udid}/status</code> - 设备状态</li>
                        </ul>
                      </div>
                      <div class="bg-green-50 border border-green-200 rounded p-3">
                        <h5 class="font-medium text-green-800 text-sm mb-1">操作执行接口</h5>
                        <ul class="text-xs text-green-700 space-y-1">
                          <li>• <code>POST /devices/{udid}/tap</code> - 点击操作</li>
                          <li>• <code>POST /devices/{udid}/swipe</code> - 滑动操作</li>
                          <li>• <code>POST /devices/{udid}/input</code> - 文本输入</li>
                        </ul>
                      </div>
                      <div class="bg-purple-50 border border-purple-200 rounded p-3">
                        <h5 class="font-medium text-purple-800 text-sm mb-1">元素查找接口</h5>
                        <ul class="text-xs text-purple-700 space-y-1">
                          <li>• <code>POST /devices/{udid}/find-text</code> - 文本查找</li>
                          <li>• <code>POST /devices/{udid}/find-element</code> - 元素查找</li>
                          <li>• <code>GET /devices/{udid}/screenshot</code> - 获取截图</li>
                        </ul>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 class="text-lg font-semibold text-gray-800 mb-4">技术特性</h4>
                    <div class="space-y-4">
                      <div class="bg-yellow-50 rounded-lg p-4">
                        <h5 class="font-medium text-yellow-800 mb-2">异步处理</h5>
                        <p class="text-sm text-yellow-700">
                          支持并发请求处理，提高系统吞吐量，
                          特别适合多设备并行测试场景。
                        </p>
                      </div>
                      <div class="bg-teal-50 rounded-lg p-4">
                        <h5 class="font-medium text-teal-800 mb-2">错误处理</h5>
                        <p class="text-sm text-teal-700">
                          统一的错误响应格式，详细的错误信息和状态码，
                          便于客户端进行异常处理。
                        </p>
                      </div>
                      <div class="bg-indigo-50 rounded-lg p-4">
                        <h5 class="font-medium text-indigo-800 mb-2">文档生成</h5>
                        <p class="text-sm text-indigo-700">
                          自动生成OpenAPI文档，提供交互式API测试界面，
                          便于开发和调试。
                        </p>
                      </div>
                    </div>
                  </div>
                </div>

                <div class="mt-6 bg-gray-50 rounded-lg p-4">
                  <h4 class="font-semibold text-gray-800 mb-3">API Server实现示例</h4>
                  <div class="code-block">
                    <pre><code>from fastapi import FastAPI, HTTPException
from pydantic import BaseModel
from tools.device_driver_manage_tools import DeviceManager

app = FastAPI(title="Mobile Test API Server", version="1.0.0")
device_manager = DeviceManager()

class TapRequest(BaseModel):
    x: int
    y: int

class FindTextRequest(BaseModel):
    text: str
    timeout: int = 10

@app.post("/devices/{udid}/tap")
async def tap_device(udid: str, request: TapRequest):
    """执行设备点击操作"""
    try:
        result = await device_manager.tap_coordinate(udid, request.x, request.y)
        return {"success": True, "result": result}
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@app.post("/devices/{udid}/find-text")
async def find_text_element(udid: str, request: FindTextRequest):
    """查找包含指定文本的元素"""
    try:
        element = await device_manager.find_element_by_text(
            udid, request.text, request.timeout
        )
        return {"success": True, "element": element}
    except Exception as e:
        raise HTTPException(status_code=404, detail=f"Element not found: {str(e)}")

@app.get("/devices")
async def list_devices():
    """获取可用设备列表"""
    devices = await device_manager.get_available_devices()
    return {"devices": devices}</code></pre>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>

        <div class="section-divider"></div>

        <!-- Autonomy and Exception Handling -->
        <section id="autonomy-impl" class="py-16 bg-white">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>自主性与异常处理机制</h2>
            </div>

            <div class="callout">
              <p>
                在状态机的严格控制下，系统仍需要保持适度的自主性来处理各种异常情况。
                通过多层次的异常处理机制，确保系统在面对意外情况时能够智能应对。
              </p>
            </div>

            <div class="architecture-grid">
              <div class="architecture-card">
                <h3 class="text-xl font-semibold mb-3 text-red-600">
                  <i class="fas fa-exclamation-triangle mr-2"></i>
                  异常检测
                </h3>
                <p class="text-gray-700 mb-3">
                  实时监控系统状态，及时发现各种异常情况，包括设备连接异常、应用崩溃、网络超时等。
                </p>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 设备连接状态监控</li>
                  <li>• 应用响应时间检测</li>
                  <li>• UI元素可用性验证</li>
                  <li>• 网络连接稳定性检查</li>
                </ul>
              </div>

              <div class="architecture-card">
                <h3 class="text-xl font-semibold mb-3 text-yellow-600">
                  <i class="fas fa-redo mr-2"></i>
                  智能重试
                </h3>
                <p class="text-gray-700 mb-3">
                  基于异常类型和历史经验，选择合适的重试策略，包括等待重试、参数调整、替代方案等。
                </p>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 指数退避重试算法</li>
                  <li>• 动态超时时间调整</li>
                  <li>• 多种定位策略尝试</li>
                  <li>• 上下文相关的重试逻辑</li>
                </ul>
              </div>

              <div class="architecture-card">
                <h3 class="text-xl font-semibold mb-3 text-green-600">
                  <i class="fas fa-route mr-2"></i>
                  路径恢复
                </h3>
                <p class="text-gray-700 mb-3">
                  当主要执行路径受阻时，寻找替代路径继续测试，或安全地回退到已知稳定状态。
                </p>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 替代操作路径选择</li>
                  <li>• 状态回退机制</li>
                  <li>• 检查点恢复功能</li>
                  <li>• 优雅降级策略</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <div class="section-divider"></div>

        <!-- LLM Integration -->
        <section id="llm-integration" class="py-16 bg-slate-50">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>与LLM的集成与交互</h2>
            </div>

            <div class="callout">
              <p>
                系统与本地30B模型的集成是整个架构的核心。通过优化的提示工程和上下文管理，
                最大化发挥模型的决策能力，同时控制计算资源消耗。
              </p>
            </div>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-12">
              <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">提示模板设计</h3>
                <div class="space-y-4">
                  <div class="bg-blue-50 rounded-lg p-4">
                    <h4 class="font-medium text-blue-800 mb-2">结构化提示</h4>
                    <p class="text-sm text-blue-700">
                      采用固定的提示结构，包括任务描述、当前状态、可用工具、期望输出格式等部分。
                    </p>
                  </div>
                  <div class="bg-green-50 rounded-lg p-4">
                    <h4 class="font-medium text-green-800 mb-2">上下文压缩</h4>
                    <p class="text-sm text-green-700">
                      通过智能摘要和关键信息提取，将长历史记录压缩为简洁的上下文信息。
                    </p>
                  </div>
                  <div class="bg-purple-50 rounded-lg p-4">
                    <h4 class="font-medium text-purple-800 mb-2">动态调整</h4>
                    <p class="text-sm text-purple-700">
                      根据当前任务的复杂度和模型的响应质量，动态调整提示的详细程度。
                    </p>
                  </div>
                </div>
              </div>

              <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-6">
                <h3 class="text-xl font-semibold text-gray-800 mb-4">响应解析与验证</h3>
                <div class="space-y-4">
                  <div class="bg-orange-50 rounded-lg p-4">
                    <h4 class="font-medium text-orange-800 mb-2">格式验证</h4>
                    <p class="text-sm text-orange-700">
                      验证模型输出是否符合预期的JSON格式，包含必要的字段和正确的数据类型。
                    </p>
                  </div>
                  <div class="bg-teal-50 rounded-lg p-4">
                    <h4 class="font-medium text-teal-800 mb-2">语义检查</h4>
                    <p class="text-sm text-teal-700">
                      检查生成的指令是否在当前上下文中合理，参数是否在有效范围内。
                    </p>
                  </div>
                  <div class="bg-red-50 rounded-lg p-4">
                    <h4 class="font-medium text-red-800 mb-2">错误处理</h4>
                    <p class="text-sm text-red-700">
                      当模型输出不符合要求时，提供清晰的错误信息并触发重新生成或降级处理。
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-gray-200 p-8">
              <h3 class="text-xl font-semibold text-gray-800 mb-6">LLM交互流程示例</h3>
              <div class="code-block">
                <pre><code>class LLMIntegrator:
    def __init__(self, model_endpoint: str):
        self.model_endpoint = model_endpoint
        self.context_manager = ContextManager()

    async def make_decision(self, state_info: dict, ui_context: dict) -> dict:
        """基于当前状态和UI上下文做出决策"""

        # 1. 构建结构化提示
        prompt = self._build_prompt(state_info, ui_context)

        # 2. 调用本地30B模型
        response = await self._call_llm(prompt)

        # 3. 解析和验证响应
        parsed_response = self._parse_response(response)

        # 4. 验证决策的合理性
        if not self._validate_decision(parsed_response, ui_context):
            # 触发重试或降级处理
            return await self._handle_invalid_decision(state_info, ui_context)

        return parsed_response

    def _build_prompt(self, state_info: dict, ui_context: dict) -> str:
        """构建结构化提示"""
        template = """
        ## 当前任务
        状态: {current_state}
        目标: {objective}

        ## UI上下文
        {ui_description}

        ## 可用工具
        {available_tools}

        ## 历史摘要
        {history_summary}

        ## 期望输出
        请以JSON格式返回下一步操作:
        {{
            "action": "工具名称",
            "parameters": {{"参数名": "参数值"}},
            "reasoning": "决策理由"
        }}
        """

        return template.format(
            current_state=state_info['state'],
            objective=state_info['objective'],
            ui_description=ui_context['description'],
            available_tools=self._format_tools(state_info['available_tools']),
            history_summary=self.context_manager.get_summary()
        )</code></pre>
              </div>
            </div>
          </div>
        </section>

        <div class="section-divider"></div>

        <!-- Implementation Steps -->
        <section id="implementation" class="py-16 bg-white">
          <div class="max-w-6xl mx-auto px-6">
            <div class="section-header">
              <h2>阶段性测试与实现步骤</h2>
            </div>

            <div class="callout">
              <p>
                为了确保系统的稳定性和可靠性，我们采用分阶段的实现和测试策略，
                从简单的单步操作开始，逐步扩展到复杂的多步骤测试场景。
              </p>
            </div>

            <div class="phase-timeline">
              <div class="phase-item">
                <div class="phase-number">1</div>
                <div class="phase-content">
                  <h3 class="text-lg font-semibold text-gray-800 mb-2">基础工具层实现</h3>
                  <p class="text-gray-700 mb-3">
                    基于现有的设备驱动管理工具，实现标准化的工具函数和API接口。
                  </p>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 封装基础设备操作函数</li>
                    <li>• 实现API Server基础框架</li>
                    <li>• 建立设备连接和状态管理</li>
                    <li>• 完成单元测试和集成测试</li>
                  </ul>
                </div>
              </div>

              <div class="phase-item">
                <div class="phase-number">2</div>
                <div class="phase-content">
                  <h3 class="text-lg font-semibold text-gray-800 mb-2">状态机引擎开发</h3>
                  <p class="text-gray-700 mb-3">
                    实现状态机核心逻辑，支持状态定义、转换控制和异常处理。
                  </p>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 设计状态机配置格式</li>
                    <li>• 实现状态转换引擎</li>
                    <li>• 添加异常处理机制</li>
                    <li>• 测试简单的3-5步流程</li>
                  </ul>
                </div>
              </div>

              <div class="phase-item">
                <div class="phase-number">3</div>
                <div class="phase-content">
                  <h3 class="text-lg font-semibold text-gray-800 mb-2">LLM集成与优化</h3>
                  <p class="text-gray-700 mb-3">
                    集成本地30B模型，优化提示工程和上下文管理策略。
                  </p>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 设计提示模板和格式</li>
                    <li>• 实现上下文压缩算法</li>
                    <li>• 优化模型响应解析</li>
                    <li>• 测试单状态内的决策能力</li>
                  </ul>
                </div>
              </div>

              <div class="phase-item">
                <div class="phase-number">4</div>
                <div class="phase-content">
                  <h3 class="text-lg font-semibold text-gray-800 mb-2">端到端测试验证</h3>
                  <p class="text-gray-700 mb-3">
                    在真实的移动应用上进行完整的20步测试流程验证。
                  </p>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 设计典型的测试用例</li>
                    <li>• 进行长流程稳定性测试</li>
                    <li>• 优化异常处理策略</li>
                    <li>• 性能调优和资源优化</li>
                  </ul>
                </div>
              </div>

              <div class="phase-item">
                <div class="phase-number">5</div>
                <div class="phase-content">
                  <h3 class="text-lg font-semibold text-gray-800 mb-2">生产部署与监控</h3>
                  <p class="text-gray-700 mb-3">
                    部署到生产环境，建立监控和日志系统，持续优化和改进。
                  </p>
                  <ul class="text-sm text-gray-600 space-y-1">
                    <li>• 建立监控和告警系统</li>
                    <li>• 实现详细的日志记录</li>
                    <li>• 建立性能指标体系</li>
                    <li>• 持续优化和功能扩展</li>
                  </ul>
                </div>
              </div>
            </div>

            <div class="highlight-box mt-8">
              <h3 class="text-lg font-semibold text-gray-800 mb-3">
                <i class="fas fa-lightbulb mr-2"></i>
                关键成功因素
              </h3>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h4 class="font-medium text-gray-800 mb-2">技术层面</h4>
                  <ul class="text-sm text-gray-700 space-y-1">
                    <li>• 充分利用现有的设备管理工具</li>
                    <li>• 优化本地30B模型的提示工程</li>
                    <li>• 建立完善的测试和验证体系</li>
                    <li>• 持续监控和性能优化</li>
                  </ul>
                </div>
                <div>
                  <h4 class="font-medium text-gray-800 mb-2">管理层面</h4>
                  <ul class="text-sm text-gray-700 space-y-1">
                    <li>• 分阶段实施，降低风险</li>
                    <li>• 建立清晰的成功标准</li>
                    <li>• 保持团队技能更新</li>
                    <li>• 与业务需求保持紧密对接</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>
      </div>

      <!-- Footer -->
      <footer class="bg-slate-800 text-white py-12">
        <div class="max-w-6xl mx-auto px-6 text-center">
          <h3 class="text-xl font-semibold mb-4">解决方案总结</h3>
          <p class="text-slate-300 mb-6 max-w-3xl mx-auto">
            通过状态机解耦的架构设计，我们成功解决了LLM-Agent在长步骤UI测试中的记忆与自主性平衡问题。
            该方案不仅确保了测试计划的严格执行，还保持了必要的智能决策能力，
            为移动应用自动化测试提供了一个可靠、高效的解决方案。
          </p>
          <div class="flex justify-center space-x-8 text-sm text-slate-400">
            <span>状态机控制</span>
            <span>•</span>
            <span>智能决策</span>
            <span>•</span>
            <span>工具执行</span>
            <span>•</span>
            <span>异常处理</span>
          </div>
        </div>
      </footer>
    </main>

    <script>
      // Mobile menu toggle
      function toggleTOC() {
        const toc = document.querySelector('.toc-fixed');
        const mainContent = document.querySelector('.main-content');
        toc.classList.toggle('active');
        mainContent.classList.toggle('toc-active');
      }

      // Smooth scrolling for anchor links
      document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
          e.preventDefault();
          const target = document.querySelector(this.getAttribute('href'));
          if (target) {
            target.scrollIntoView({
              behavior: 'smooth',
              block: 'start'
            });
          }
        });
      });

      // Add mobile menu button for small screens
      if (window.innerWidth <= 1024) {
        const menuButton = document.createElement('button');
        menuButton.innerHTML = '<i class="fas fa-bars"></i>';
        menuButton.className = 'fixed top-4 left-4 z-50 bg-blue-600 text-white p-3 rounded-lg shadow-lg';
        menuButton.onclick = toggleTOC;
        document.body.appendChild(menuButton);
      }
    </script>
  </body>
</html>
