import asyncio
import threading
import os
from contextlib import asynccontextmanager
from typing import Optional, Dict, Any
from fastapi import FastAPI, HTTPException
from fastapi.responses import FileResponse
from pydantic import BaseModel

# 设置tokenizers环境变量，避免fork警告
os.environ['TOKENIZERS_PARALLELISM'] = 'false'

from agent import LocalAgent
from concurrent_agent_pool import get_concurrent_agent_pool, initialize_concurrent_agent_pool
from ollama_simple_manager import ollama_manager
from tools._concurrent_task_manager import get_concurrent_task_manager
from tools._concurrent_log_manager import get_global_log_manager
from status_manager import StatusManager
from agent_json_planer import AgentJsonPlaner
import uvicorn
import os

@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理"""
    # 启动时初始化Agent
    init_agent()
    yield
    # 这里可以添加关闭时的清理逻辑

app = FastAPI(title="Local Agent API", version="1.0.0", lifespan=lifespan)

# 全局变量
agent_instance = None  # 保留用于向后兼容
concurrent_agent_pool = None
task_manager = None
log_manager = None
status_manager = None
json_planer = None  # JSON计划生成器
concurrent_mode_enabled = False  # 并发模式开关

class TaskRequest(BaseModel):
    task_description: str
    mis_id: str

class PlanRequest(BaseModel):
    natural_language: str
    platform: str = "ios"

class SmartTaskRequest(BaseModel):
    task_description: str  # 兼容原接口，实际作为自然语言输入
    mis_id: str
    platform: Optional[str] = None  # 可选的平台指定
    
class ConfirmRequest(BaseModel):
    message: str
    
class TaskResponse(BaseModel):
    round_id: str  # Java后端期望的字段名
    message: str
    
class ChatResponse(BaseModel):
    round_id: str  # Java后端期望的字段名
    result: str
    status: str
    log_folder: str
    message: str

def init_agent():
    """初始化Agent实例"""
    global agent_instance, concurrent_mode_enabled, concurrent_agent_pool, task_manager, log_manager, status_manager, json_planer
    
    # 初始化全局管理器
    task_manager = get_concurrent_task_manager()
    log_manager = get_global_log_manager()
    status_manager = StatusManager()
    
    # 初始化JSON计划生成器
    try:
        json_planer = AgentJsonPlaner()
        log_manager.info_agent("API服务器: JSON计划生成器初始化成功")
    except Exception as e:
        log_manager.error_agent(f"API服务器: JSON计划生成器初始化失败: {e}")
        json_planer = None
    
    # 尝试初始化并发模式
    try:
        # 检查是否有可用的 Ollama 实例
        available_ports = ollama_manager.get_available_ports()
        if available_ports:
            log_manager.info_agent(f"API服务器: 发现 {len(available_ports)} 个可用的Ollama实例: {available_ports}")
            
            # 预加载模型到所有可用实例
            log_manager.info_agent("API服务器: 开始预加载模型...")
            ollama_manager.preload_all_models()
            
            # 等待模型加载完成
            import time
            time.sleep(5)
            
            # 检查已加载模型的端口
            ready_ports = ollama_manager.get_ready_ports()
            if ready_ports:
                log_manager.info_agent(f"API服务器: 模型预加载完成，可用端口: {ready_ports}")
                
                # 初始化并发Agent池
                if initialize_concurrent_agent_pool():
                    concurrent_agent_pool = get_concurrent_agent_pool()
                    concurrent_mode_enabled = True
                    log_manager.info_agent(f"API服务器: 并发模式初始化成功，{len(ready_ports)} 个Agent实例已创建")
                else:
                    log_manager.warning_agent("API服务器: 并发Agent池初始化失败，回退到单例模式")
            else:
                log_manager.warning_agent("API服务器: 模型预加载失败，回退到单例模式")
        else:
            log_manager.warning_agent("API服务器: 没有可用的Ollama实例，回退到单例模式")
    except Exception as e:
        log_manager.error_agent(f"API服务器: 并发模式初始化失败: {e}")
    
    # 如果并发模式失败，初始化传统单例模式
    if not concurrent_mode_enabled and agent_instance is None:
        try:
            agent_instance = LocalAgent()
            log_manager.info_agent("API服务器: 单例Agent实例初始化成功")
        except Exception as e:
            log_manager.error_agent(f"API服务器: Agent实例初始化失败: {e}")
            raise

def is_test_running():
    """检查是否有测试在运行"""
    try:
        global concurrent_mode_enabled, concurrent_agent_pool, task_manager
        
        if concurrent_mode_enabled and concurrent_agent_pool:
            # 并发模式：检查是否还能接受任务
            can_accept = concurrent_agent_pool.can_accept_task()
            running_tasks = concurrent_agent_pool.get_running_tasks()
            
            if not can_accept:
                log_manager.info_agent(f"API服务器: 已达到最大并发数，当前运行任务: {len(running_tasks)}")
                return True, running_tasks
            else:
                log_manager.info_agent(f"API服务器: 可接受新任务，当前运行任务: {len(running_tasks)}")
                return False, running_tasks
        else:
            # 单例模式：检查是否有任务在运行
            if task_manager:
                running_count = task_manager.get_running_tasks_count()
                if running_count > 0:
                    running_tasks = task_manager.get_running_tasks_info()
                    log_manager.info_agent(f"API服务器: 检测到 {running_count} 个任务正在运行")
                    return True, running_tasks
                else:
                    log_manager.info_agent("API服务器: 当前没有任务在运行")
                    return False, []
            else:
                # 回退到传统逻辑（如果需要的话）
                return False, []
            
    except Exception as e:
        log_manager.error_agent(f"API服务器: 检查测试状态失败: {e}")
        return False, []

def execute_smart_task_background(natural_language: str, task_id: str, mis_id: str):
    """在后台执行智能任务转换和执行"""
    try:
        # 获取轮次信息用于日志标识
        round_id = None
        try:
            current_round = status_manager.get_current_round()
            round_id = f"round_{current_round:06d}"
        except:
            round_id = "unknown"
        
        log_manager.info_agent(f"API服务器: 开始执行智能任务 {task_id} (轮次: {round_id})")
        log_manager.info_agent(f"自然语言输入: {natural_language}")
        log_manager.info_agent(f"MIS ID: {mis_id}")
        
        # 1. 自然语言到结构化计划的转换
        log_manager.info_agent(f"步骤1: 开始自然语言转换")
        plan_result = json_planer.generate_plan(natural_language)
        
        if plan_result["status"] != "success":
            error_msg = f"计划转换失败: {plan_result['error']}"
            log_manager.error_agent(f"API服务器: {error_msg}")
            # 将错误结果存储到全局变量中
            if not hasattr(execute_smart_task_background, 'results'):
                execute_smart_task_background.results = {}
            execute_smart_task_background.results[task_id] = {
                "result": None,
                "status": "failed",
                "error": error_msg,
                "session_info": None
            }
            return
        
        plan = plan_result["plan"]
        log_manager.info_agent(f"步骤1完成: 转换成功 {plan.get('total_steps', 0)} 个步骤")
        
        # 2. 转换为Agent指令
        log_manager.info_agent(f"步骤2: 转换为Agent执行指令")
        detailed_instruction = json_planer.convert_plan_to_detailed_instruction(plan)
        instruction_lines = detailed_instruction.split('\n')
        log_manager.info_agent(f"步骤2完成: 生成指令 {len(instruction_lines)} 行")
        
        # 3. 设置agent为智能任务模式并执行
        log_manager.info_agent(f"步骤3: 开始执行转换后的指令")
        if hasattr(agent_instance, 'is_smart_task'):
            agent_instance.is_smart_task = True
        
        # 为每个新任务清除聊天历史，确保独立执行
        agent_instance.clear_history()
        log_manager.info_agent(f"API服务器: 已清除聊天历史，开始新任务 (轮次: {round_id})")
        
        # 使用agent执行转换后的指令
        result = agent_instance.chat(detailed_instruction, mis_id)
        
        log_manager.info_agent(f"API服务器: 智能任务 {task_id} 执行完成 (轮次: {round_id})")
        log_manager.info_agent(f"任务结果: {result[:200]}...")
        
        # 将结果存储到全局变量中，供后续查询
        if not hasattr(execute_smart_task_background, 'results'):
            execute_smart_task_background.results = {}
        execute_smart_task_background.results[task_id] = {
            "result": result,
            "status": "completed",
            "session_info": agent_instance.get_last_session_info()
        }
        
    except Exception as e:
        # 记录错误信息
        error_msg = str(e)
        log_manager.error_agent(f"API服务器: 智能任务 {task_id} 执行失败: {error_msg}")
        
        # 将错误结果存储到全局变量中
        if not hasattr(execute_smart_task_background, 'results'):
            execute_smart_task_background.results = {}
        execute_smart_task_background.results[task_id] = {
            "result": None,
            "status": "failed",
            "error": error_msg,
            "session_info": agent_instance.get_last_session_info() if hasattr(agent_instance, 'get_last_session_info') else None
        }

def execute_task_background(task_description: str, task_id: str, mis_id: str, is_smart_task: bool = False):
    """在后台执行任务"""
    try:
        # 获取轮次信息用于日志标识
        round_id = None
        try:
            current_round = status_manager.get_current_round()
            round_id = f"round_{current_round:06d}"
        except:
            round_id = "unknown"
        
        log_manager.info_agent(f"API服务器: 开始执行任务 {task_id} (轮次: {round_id})")
        log_manager.info_agent(f"任务描述: {task_description}")
        log_manager.info_agent(f"MIS ID: {mis_id}")
        log_manager.info_agent(f"任务类型: {'智能任务' if is_smart_task else '普通任务'}")
        
        # 设置任务类型（如果agent支持的话）
        if hasattr(agent_instance, 'is_smart_task'):
            agent_instance.is_smart_task = is_smart_task
        
        # 为每个新任务清除聊天历史，确保独立执行
        agent_instance.clear_history()
        log_manager.info_agent(f"API服务器: 已清除聊天历史，开始新任务 (轮次: {round_id})")
        
        # 使用agent的自动会话管理执行任务
        result = agent_instance.chat(task_description, mis_id)
        
        log_manager.info_agent(f"API服务器: 任务 {task_id} 执行完成 (轮次: {round_id})")
        log_manager.info_agent(f"任务结果: {result[:200]}...")
        
        # 将结果存储到全局变量中，供后续查询
        if not hasattr(execute_task_background, 'results'):
            execute_task_background.results = {}
        execute_task_background.results[task_id] = {
            "result": result,
            "status": "completed",
            "session_info": agent_instance.get_last_session_info()
        }
        
    except Exception as e:
        # 记录错误信息
        error_msg = str(e)
        log_manager.error_agent(f"API服务器: 任务 {task_id} 执行失败: {error_msg}")
        
        # 将错误结果存储到全局变量中
        if not hasattr(execute_task_background, 'results'):
            execute_task_background.results = {}
        execute_task_background.results[task_id] = {
            "result": None,
            "status": "failed",
            "error": error_msg,
            "session_info": agent_instance.get_last_session_info()
        }


@app.post("/confirm")
async def confirm_server(request: ConfirmRequest):
    """
    服务器确认端点 - 用于局域网设备识别
    
    发送 "aitest confirm" 消息来确认这是一个Agent服务器
    
    Example:
        curl -X POST "http://************:5630/confirm" \\
             -H "Content-Type: application/json" \\
             -d '{"message": "aitest confirm"}'
    """
    try:
        log_manager.info_agent(f"API服务器: 收到确认请求: {request.message}")
        
        if request.message.strip() == "aitest confirm":
            log_manager.info_agent("API服务器: 确认请求验证成功")
            return {"status": "confirmed", "message": "confirmed"}
        else:
            log_manager.warning_agent(f"API服务器: 确认请求验证失败，收到消息: {request.message}")
            return {"status": "invalid", "message": "invalid confirmation message"}
            
    except Exception as e:
        error_msg = f"确认请求处理失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/chat", response_model=ChatResponse)
async def chat_with_agent(request: TaskRequest):
    """
    与Agent聊天（同步执行）
    
    Example:
        curl -X POST "http://************:5630/chat" \\
             -H "Content-Type: application/json" \\
             -d '{"task_description": "帮我找到可用设备并截图"}'
    """
    try:
        log_manager.info_agent(f"API服务器: 收到聊天请求")
        log_manager.info_agent(f"任务描述: {request.task_description}")
        log_manager.info_agent(f"MIS ID: {request.mis_id}")
        
        # 为每个新任务清除聊天历史，确保独立执行
        agent_instance.clear_history()
        
        # 使用agent的自动会话管理执行任务
        result = agent_instance.chat(request.task_description, request.mis_id)
        
        # 获取会话信息
        session_info = agent_instance.get_last_session_info()
        
        log_manager.info_agent(f"API服务器: 聊天完成")
        
        return ChatResponse(
            round_id=session_info.get("round_id", "unknown"),
            result=result,
            status="completed",
            log_folder=session_info.get("log_dir", "unknown"),
            message="任务执行成功"
        )
        
    except Exception as e:
        error_msg = f"聊天执行失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/submit_task", response_model=TaskResponse)
async def submit_task(request: TaskRequest):
    """
    提交任务（异步执行）
    
    Example:
        curl -X POST "http://************:5630/submit_task" \\
             -H "Content-Type: application/json" \\
             -d '{"task_description": "执行完整的设备测试流程"}'
    """
    try:
        # 检查是否可以接受新任务（基于并行任务数量限制）
        is_running, running_tasks = is_test_running()
        
        if is_running:
            # 如果是并发模式，显示当前运行的任务信息
            if concurrent_mode_enabled:
                task_list = [f"{task.get('task_id', 'unknown')} (端口{task.get('port', 'unknown')})" for task in running_tasks]
                log_manager.warning_agent(f"API服务器: 已达到最大并发数(2)，当前运行任务: {task_list}")
                return TaskResponse(
                    round_id="0",  # 0表示被拒绝
                    message=f"已达到最大并发任务数(2个)，当前运行任务: {len(running_tasks)} 个，请稍后再试"
                )
            else:
                # 单例模式的原有逻辑
                current_task = running_tasks[0] if running_tasks else {}
                current_round_id = current_task.get('round_id', 'unknown')
                task_desc = current_task.get('task_description', '')[:50] + "..." if len(current_task.get('task_description', '')) > 50 else current_task.get('task_description', '')
                
                log_manager.warning_agent(f"API服务器: 拒绝新任务提交，当前有测试正在运行: {current_round_id}")
                
                return TaskResponse(
                    round_id="0",  # 0表示被拒绝
                    message=f"测试正在进行中，当前任务: {current_round_id} ({task_desc})，请稍后再试"
                )
        
        # 正常提交任务流程
        if concurrent_mode_enabled and concurrent_agent_pool:
            # 并发模式：使用新的并发Agent池
            success, message, round_id = concurrent_agent_pool.assign_task(request.task_description, request.mis_id)
            
            if success:
                # 任务分配成功的日志记录到全局日志，因为此时任务还未完全启动
                log_manager.info_agent(f"API服务器: 任务已分配给并发Agent池，轮次: {round_id}")
                return TaskResponse(
                    round_id=round_id,
                    message=f"任务已提交并分配，轮次: {round_id} - {message}"
                )
            else:
                log_manager.error_agent(f"API服务器: 任务分配失败: {message}")
                raise HTTPException(status_code=500, detail=f"任务分配失败: {message}")
        else:
            # 单例模式：使用传统方式
            import uuid
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            task_thread = threading.Thread(
                target=execute_task_background,
                args=(request.task_description, task_id, request.mis_id)
            )
            task_thread.daemon = True
            task_thread.start()
            
            log_manager.info_agent(f"API服务器: 任务 {task_id} 已提交（单例模式）")
            
            return TaskResponse(
                round_id=task_id,
                message=f"任务已提交，任务ID: {task_id}（单例模式）"
            )
        
    except Exception as e:
        error_msg = f"提交任务失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/task_result/{task_id}")
async def get_task_result(task_id: str):
    """
    获取异步任务结果
    
    Example:
        curl "http://************:5630/task_result/task_a1b2c3d4"
    """
    try:
        # 检查任务结果
        if not hasattr(execute_task_background, 'results'):
            raise HTTPException(status_code=404, detail=f"任务 {task_id} 不存在")
        
        if task_id not in execute_task_background.results:
            return {"status": "running", "message": "任务正在执行中"}
        
        task_result = execute_task_background.results[task_id]
        return {
            "task_id": task_id,
            "status": task_result["status"],
            "result": task_result.get("result"),
            "error": task_result.get("error"),
            "session_info": task_result.get("session_info", {})
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取任务结果失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/session_info/{round_id}")
async def get_session_info(round_id: str):
    """
    获取会话信息
    
    Example:
        curl "http://************:5630/session_info/round_240708_120139"
    """
    try:
        session_info = agent_instance.get_session_status(round_id)
        if not session_info:
            raise HTTPException(status_code=404, detail=f"会话 {round_id} 不存在")
        
        return session_info
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取会话信息失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/all_sessions")
async def get_all_sessions():
    """
    获取所有会话
    
    Example:
        curl "http://************:5630/all_sessions"
    """
    try:
        sessions = agent_instance.get_session_status()
        return sessions
        
    except Exception as e:
        error_msg = f"获取所有会话失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/agent_info")
async def get_agent_info():
    """
    获取Agent信息
    
    Example:
        curl "http://************:5630/agent_info"
    """
    try:
        if agent_instance is None:
            raise HTTPException(status_code=500, detail="Agent未初始化")
        
        model_info = agent_instance.get_model_info()
        available_tools = agent_instance.get_available_tools()
        sessions = agent_instance.get_session_status()
        
        return {
            "model_info": model_info,
            "available_tools": available_tools,
            "current_round": sessions.get("current_round", 0),
            "total_sessions": len(sessions.get("all_tasks", {}))
        }
        
    except HTTPException:
        raise
    except Exception as e:
        error_msg = f"获取Agent信息失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/health")
async def health_check():
    """
    健康检查
    
    Example:
        curl "http://************:5630/health"
    """
    return {"status": "healthy", "message": "API服务器运行正常"}

@app.get("/test_status")
async def get_test_status():
    """
    获取当前测试状态
    
    Example:
        curl "http://************:5630/test_status"
    """
    try:
        is_running, running_tasks = is_test_running()
        
        if is_running:
            current_task = running_tasks[0] if running_tasks else {}
            return {
                "is_running": True,
                "current_task": {
                    "round_id": current_task.get('round_id', 'unknown'),
                    "task_description": current_task.get('task_description', '')[:100] + "..." if len(current_task.get('task_description', '')) > 100 else current_task.get('task_description', ''),
                    "start_time": current_task.get('start_time', ''),
                    "status": current_task.get('status', '')
                },
                "running_tasks_count": len(running_tasks),
                "message": f"有 {len(running_tasks)} 个测试正在运行"
            }
        else:
            return {
                "is_running": False,
                "current_task": None,
                "running_tasks_count": 0,
                "message": "当前没有测试在运行，可以提交新任务"
            }
            
    except Exception as e:
        error_msg = f"获取测试状态失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/favicon.ico")
async def favicon():
    """返回favicon图标，避免404错误"""
    # 返回一个简单的响应，避免404
    return {"message": "favicon not available"}

@app.get("/apple-touch-icon-precomposed.png")
async def apple_touch_icon_precomposed():
    """返回Apple触摸图标，避免404错误"""
    return {"message": "apple touch icon not available"}

@app.get("/apple-touch-icon.png")
async def apple_touch_icon():
    """返回Apple触摸图标，避免404错误"""
    return {"message": "apple touch icon not available"}

@app.get("/pool_status")
async def get_pool_status():
    """
    获取Agent池状态
    
    Example:
        curl "http://************:5630/pool_status"
    """
    try:
        if concurrent_mode_enabled and concurrent_agent_pool:
            pool_status = concurrent_agent_pool.get_pool_status()
            running_tasks = concurrent_agent_pool.get_running_tasks()
            
            return {
                "concurrent_mode": True,
                "pool_status": pool_status,
                "running_tasks": running_tasks,
                "message": f"并发Agent池运行正常，{pool_status['idle_agents']}/{pool_status['total_agents']} 个Agent空闲"
            }
        else:
            return {
                "concurrent_mode": False,
                "message": "当前使用单例模式，没有并发Agent池"
            }
            
    except Exception as e:
        error_msg = f"获取Agent池状态失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/ollama_status")
async def get_ollama_status():
    """
    获取Ollama实例状态
    
    Example:
        curl "http://************:5630/ollama_status"
    """
    try:
        if concurrent_mode_enabled:
            instance_status = ollama_manager.get_status()
            available_instances = ollama_manager.get_available_ports()
            
            return {
                "concurrent_mode": True,
                "instance_status": instance_status,
                "available_instances": available_instances,
                "total_instances": len(instance_status),
                "healthy_instances": len(available_instances),
                "message": f"Ollama实例状态正常，{len(available_instances)}/{len(instance_status)} 个实例健康"
            }
        else:
            return {
                "concurrent_mode": False,
                "message": "当前使用单例模式，连接默认Ollama实例 (端口 11434)"
            }
            
    except Exception as e:
        error_msg = f"获取Ollama状态失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/generate_plan")
async def generate_execution_plan(request: PlanRequest):
    """
    生成执行计划
    
    将自然语言测试用例转换为结构化的执行计划
    
    Example:
        curl -X POST "http://************:5630/generate_plan" \\
             -H "Content-Type: application/json" \\
             -d '{"natural_language": "点击左上角的地址，输入北京", "platform": "ios"}'
    """
    try:
        if not json_planer:
            raise HTTPException(status_code=500, detail="JSON计划生成器未初始化")
        
        log_manager.info_agent(f"API服务器: 收到计划生成请求")
        log_manager.info_agent(f"自然语言: {request.natural_language}")
        log_manager.info_agent(f"目标平台: {request.platform}")
        
        # 生成计划
        result = json_planer.generate_plan(request.natural_language)
        
        if result["status"] == "success":
            plan = result["plan"]
            
            # 生成两种指令格式
            simple_instruction = json_planer.convert_plan_to_agent_instruction(plan)
            detailed_instruction = json_planer.convert_plan_to_detailed_instruction(plan)
            
            log_manager.info_agent(f"API服务器: 计划生成成功，共 {plan.get('total_steps', 0)} 个步骤")
            
            return {
                "status": "success",
                "plan": plan,
                "instructions": {
                    "simple": simple_instruction,
                    "detailed": detailed_instruction
                },
                "processing_time": result.get("processing_time", 0),
                "message": f"成功生成包含 {plan.get('total_steps', 0)} 个步骤的执行计划"
            }
        else:
            log_manager.error_agent(f"API服务器: 计划生成失败 - {result['error']}")
            return {
                "status": "error",
                "error": result["error"],
                "raw_response": result.get("raw_response", ""),
                "processing_time": result.get("processing_time", 0),
                "message": "计划生成失败"
            }
            
    except Exception as e:
        error_msg = f"生成执行计划失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.post("/submit_smart_task", response_model=TaskResponse)
async def submit_smart_task(request: TaskRequest):
    """
    智能任务提交（与submit_task接口完全兼容）
    
    自动将自然语言转换为执行计划，然后提交给Agent执行
    
    Example:
        curl -X POST "http://************:5630/submit_smart_task" \\
             -H "Content-Type: application/json" \\
             -d '{"task_description": "点击左上角的地址，输入北京", "mis_id": "test_001"}'
    """
    try:
        if not json_planer:
            raise HTTPException(status_code=500, detail="JSON计划生成器未初始化")
        
        # 创建转换日志文件
        import time
        timestamp = time.strftime('%Y%m%d_%H%M%S')
        conversion_log_file = f"/Users/<USER>/Desktop/work/langchain_ollama/log/smart_task_conversion/conversion_{timestamp}_{request.mis_id}.log"
        
        def log_conversion(message: str):
            """记录转换日志"""
            with open(conversion_log_file, "a", encoding="utf-8") as f:
                f.write(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {message}\n")
            log_manager.info_agent(message)
        
        log_conversion(f"🔄 智能任务转换开始")
        log_conversion(f"📝 原始自然语言: {request.task_description}")
        log_conversion(f"🎯 MIS ID: {request.mis_id}")
        
        # 检查是否可以接受新任务（基于并行任务数量限制） - 与原接口完全一致
        is_running, running_tasks = is_test_running()
        
        if is_running:
            # 如果是并发模式，显示当前运行的任务信息
            if concurrent_mode_enabled:
                task_list = [f"{task.get('task_id', 'unknown')} (端口{task.get('port', 'unknown')})" for task in running_tasks]
                log_conversion(f"⚠️ 任务被拒绝: 已达到最大并发数(2)，当前运行任务: {task_list}")
                return TaskResponse(
                    round_id="0",  # 0表示被拒绝
                    message=f"已达到最大并发任务数(2个)，当前运行任务: {len(running_tasks)} 个，请稍后再试"
                )
            else:
                # 单例模式的原有逻辑
                current_task = running_tasks[0] if running_tasks else {}
                current_round_id = current_task.get('round_id', 'unknown')
                task_desc = current_task.get('task_description', '')[:50] + "..." if len(current_task.get('task_description', '')) > 50 else current_task.get('task_description', '')
                
                log_conversion(f"⚠️ 任务被拒绝: 当前有测试正在运行: {current_round_id}")
                
                return TaskResponse(
                    round_id="0",  # 0表示被拒绝
                    message=f"测试正在进行中，当前任务: {current_round_id} ({task_desc})，请稍后再试"
                )
        
        # 异步处理智能任务转换和执行
        log_conversion(f"🚀 智能任务已接收，开始异步处理")
        
        # 正常提交任务流程 - 参考submit_task的处理方式
        if concurrent_mode_enabled and concurrent_agent_pool:
            # 并发模式：直接分配任务，转换在后台进行
            success, message, round_id = concurrent_agent_pool.assign_smart_task(request.task_description, request.mis_id)
            
            if success:
                # 任务分配成功的日志记录到全局日志
                log_conversion(f"✅ 智能任务已分配给并发Agent池，轮次: {round_id}")
                log_conversion(f"🔄 转换和执行将在后台进行")
                
                return TaskResponse(
                    round_id=round_id,
                    message=f"智能任务已提交并分配，轮次: {round_id} - {message}"
                )
            else:
                log_conversion(f"❌ 任务分配失败: {message}")
                raise HTTPException(status_code=500, detail=f"任务分配失败: {message}")
        else:
            # 单例模式：使用传统方式
            import uuid
            task_id = f"task_{uuid.uuid4().hex[:8]}"
            
            task_thread = threading.Thread(
                target=execute_smart_task_background,
                args=(request.task_description, task_id, request.mis_id)
            )
            task_thread.daemon = True
            task_thread.start()
            
            log_conversion(f"✅ 智能任务已提交: {task_id}")
            log_conversion(f"🔄 转换和执行将在后台进行")
            
            return TaskResponse(
                round_id=task_id,
                message=f"智能任务已提交，任务ID: {task_id}"
            )
            
    except Exception as e:
        error_msg = f"智能任务提交失败: {str(e)}"
        log_manager.error_agent(f"API服务器: {error_msg}")
        raise HTTPException(status_code=500, detail=error_msg)

@app.get("/")
async def root():
    """
    根路径 - 获取API服务器基本信息
    
    Example:
        curl "http://************:5630/"
    """
    return {
        "message": "Local Agent API Server",
        "version": "2.1.0",
        "description": "支持并发任务处理的Agent API服务器",
        "concurrent_mode": concurrent_mode_enabled,
        "endpoints": {
            "confirm": "POST /confirm - 服务器确认接口（发送'aitest confirm'获取'confirmed'响应）",
            "chat": "POST /chat - 同步聊天接口",
            "submit_task": "POST /submit_task - 异步任务提交（支持并发）",
            "generate_plan": "POST /generate_plan - 自然语言转执行计划",
            "submit_smart_task": "POST /submit_smart_task - 智能任务提交（自动转换+执行）",
            "task_result": "GET /task_result/{task_id} - 获取异步任务结果",
            "session_info": "GET /session_info/{round_id} - 获取会话信息",
            "all_sessions": "GET /all_sessions - 获取所有会话",
            "agent_info": "GET /agent_info - 获取Agent信息",
            "test_status": "GET /test_status - 获取当前测试状态",
            "pool_status": "GET /pool_status - 获取Agent池状态",
            "ollama_status": "GET /ollama_status - 获取Ollama实例状态",
            "health": "GET /health - 健康检查"
        }
    }

if __name__ == "__main__":
    # 先初始化log_manager
    log_manager = get_global_log_manager()
    log_manager.info_agent("API服务器: 启动中...")
    uvicorn.run(app, host="0.0.0.0", port=5630)