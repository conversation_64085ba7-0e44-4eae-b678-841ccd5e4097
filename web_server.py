#!/usr/bin/env python3
"""
Web Server for Combined Report
提供合并报告的HTTP服务器，运行在7670端口
"""

import os
import sys
from pathlib import Path
from http.server import HTTPServer, SimpleHTTPRequestHandler
import webbrowser
import threading
import time

class ReportHTTPRequestHandler(SimpleHTTPRequestHandler):
    """自定义HTTP请求处理器"""

    def __init__(self, *args, **kwargs):
        # 设置服务器根目录为当前目录
        super().__init__(*args, directory=os.getcwd(), **kwargs)

    def do_GET(self):
        """处理GET请求"""
        # 如果访问根路径，重定向到合并报告
        if self.path == '/' or self.path == '':
            self.path = '/merged_report.html'

        # 调用父类方法处理请求
        super().do_GET()

    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")

def check_report_file():
    """检查报告文件是否存在"""
    report_file = Path("merged_report.html")
    if not report_file.exists():
        print("❌ 错误: merged_report.html 文件不存在!")
        print("请确保已经生成了合并报告文件。")
        return False
    return True

def open_browser_delayed(url, delay=2):
    """延迟打开浏览器"""
    def open_browser():
        time.sleep(delay)
        try:
            webbrowser.open(url)
            print(f"🌐 已在默认浏览器中打开: {url}")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {e}")
            print(f"请手动访问: {url}")
    
    thread = threading.Thread(target=open_browser)
    thread.daemon = True
    thread.start()

def start_server(port=7670, auto_open=True):
    """启动HTTP服务器"""

    # 检查报告文件
    if not check_report_file():
        return False

    try:
        # 创建服务器
        server_address = ('', port)
        httpd = HTTPServer(server_address, ReportHTTPRequestHandler)

        print("=" * 60)
        print("🚀 LLM-Agent UI测试报告服务器")
        print("=" * 60)
        print(f"📍 服务器地址: http://localhost:{port}")
        print(f"📄 报告文件: merged_report.html")
        print(f"📂 服务目录: {os.getcwd()}")
        print("=" * 60)
        print("💡 提示:")
        print("  - 访问 http://localhost:7670 查看合并报告")
        print("  - 按 Ctrl+C 停止服务器")
        print("=" * 60)

        # 延迟打开浏览器
        if auto_open:
            open_browser_delayed(f"http://localhost:{port}")

        print(f"🟢 服务器已启动，监听端口 {port}...")
        print()

        # 启动服务器
        httpd.serve_forever()

    except KeyboardInterrupt:
        print("\n🛑 收到停止信号，正在关闭服务器...")
        if 'httpd' in locals():
            httpd.shutdown()
        print("✅ 服务器已停止")
        return True

    except OSError as e:
        print(f"❌ 服务器启动失败: {e}")
        if "Address already in use" in str(e):
            print(f"端口 {port} 已被占用，请尝试其他端口")
        return False

    except Exception as e:
        print(f"❌ 意外错误: {e}")
        return False

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(
        description="启动LLM-Agent UI测试报告服务器",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python web_server.py                    # 使用默认端口7670启动
  python web_server.py --port 8080       # 使用端口8080启动
  python web_server.py --no-browser      # 启动但不自动打开浏览器
        """
    )
    
    parser.add_argument(
        '--port', '-p',
        type=int,
        default=7670,
        help='服务器端口号 (默认: 7670)'
    )
    
    parser.add_argument(
        '--no-browser',
        action='store_true',
        help='不自动打开浏览器'
    )
    
    args = parser.parse_args()
    
    # 验证端口号
    if not (1 <= args.port <= 65535):
        print("❌ 错误: 端口号必须在 1-65535 范围内")
        sys.exit(1)
    
    # 启动服务器
    success = start_server(port=args.port, auto_open=not args.no_browser)
    
    if not success:
        sys.exit(1)

if __name__ == "__main__":
    main()
