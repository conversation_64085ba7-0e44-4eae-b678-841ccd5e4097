基于状态机的移动端App测试Agent设计方案
1. 核心问题与解决方案概述
1.1 当前Agent面临的挑战
1.1.1 LLM上下文窗口限制导致的计划遗忘
在当前基于大型语言模型（LLM）的移动端App测试Agent项目中，一个核心挑战是LLM的上下文窗口限制，这直接导致了Agent在执行长序列测试任务时出现“计划遗忘”的现象。尽管用户所使用的本地模型拥有高达40k的上下文窗口，理论上足以覆盖包含20多个步骤的测试计划，但在实际运行中，Agent往往在正确执行了前几个步骤后，便开始偏离预设的测试流程。例如，在一个包含19个步骤的美团App地址选择功能测试中，Agent可能在执行完第5步后，就忘记了后续需要进行的操作，如校验页面文案或点击特定元素。这种现象的根本原因在于，LLM在处理长文本时，其注意力机制可能会逐渐“遗忘”或“稀释”掉序列早期的信息，尤其是在需要持续不断地将新的执行结果和观察反馈融入上下文时。随着测试步骤的推进，上下文被不断填充，早期的关键指令（如测试目标、整体流程）被后续的工具调用结果和状态信息所稀释，最终导致Agent失去对全局计划的把握。这种“计划遗忘”不仅影响了测试的自动化程度和可靠性，也使得Agent难以胜任复杂的、多步骤的端到端测试场景，限制了其在实际应用中的价值。
1.1.2 缺乏自主异常处理能力
当前测试Agent的另一个显著缺陷是其缺乏有效的自主异常处理能力。在理想的测试场景中，每一个操作都能按预期成功执行，但在真实的App测试环境中，各种意外情况时有发生，例如网络延迟导致页面加载缓慢、UI元素因动态渲染而暂时无法找到、或者点击操作后页面未发生预期的跳转。现有的Agent在面对这些异常情况时，往往表现出较低的鲁棒性。例如，当find_element工具未能找到目标元素时，Agent可能会直接报错并终止测试，而不是尝试进行自主修复，如等待一段时间后再次尝试查找。同样，如果tap操作执行后，页面状态没有按预期变化，Agent也缺乏有效的机制来判断是操作失败、页面卡顿还是进入了错误的页面，从而无法采取有效的补救措施。用户期望Agent具备一定的“自主性”，即能够根据工具返回的具体结论（如“未找到元素”、“截图失败”等）进行简单的决策，例如自动重试。对于更复杂的错误，如点击后页面未跳转，则希望能将决策权交给LLM，由其分析当前状态并决定下一步行动。这种自主决策能力的缺失，使得Agent在面对非理想情况时表现不佳，需要大量的人工干预，违背了自动化测试的初衷。
1.1.3 机械执行与自主性的平衡难题
在测试Agent的设计中，如何在“机械执行”与“自主性”之间找到一个恰当的平衡点，是一个复杂且关键的设计难题。一方面，为了保证测试的准确性和可复现性，Agent需要严格按照用户提供的结构化测试计划执行每一步操作。这种“机械执行”模式确保了测试流程的确定性，使得测试结果具有高度的可预测性。然而，正如前文所述，这种模式在面对异常情况时显得过于僵化，缺乏应对变化的灵活性。另一方面，如果赋予Agent过高的自主性，让其完全脱离预设的测试计划，根据实时情况自由决策，那么测试过程可能会变得不可控，甚至偏离测试的核心目标，导致无法验证预期的功能点。用户所期望的“自主性”是一种有限的、受控的智能，即在遵循总体测试计划的前提下，Agent能够自主处理一些简单的、可预见的异常情况，如元素查找失败后的重试，或者在连续失败后能够记录错误并优雅地终止测试。这种平衡的实现需要一套精密的机制，既能保证Agent对核心测试计划的忠实执行，又能为其提供在特定规则下进行自主决策的能力。如何设计和实现这样一套机制，使得Agent既能“听话”又能“思考”，是本项目在技术实现上的一大挑战。
1.2 状态机模式的核心价值
1.2.1 以App界面状态为核心管理测试流程
状态机模式为解决上述挑战提供了一个结构化的框架，其核心思想是将复杂的、长序列的测试任务分解为一系列离散的、可管理的“状态”（States）。在本项目中，状态的定义以美团App的界面状态为核心“主键” 。这意味着，当Agent通过调用tap、swipe或restartapp等工具操作，导致App界面发生显著变化（例如，从首页跳转到地址选择页）时，就视为进入了一个新的状态。这种以界面状态为核心的划分方式，天然地将测试流程切分成了多个逻辑单元，每个单元对应App的一个特定页面或功能模块。例如，一个完整的地址选择测试流程可以被划分为“首页”、“地址选择页”、“地址搜索页”等多个状态。在每个状态下，Agent需要执行的操作（如find_element、ocr_validate_text）都是针对当前页面的，具有明确的上下文。这种设计极大地降低了流程管理的复杂性，使得Agent不再需要在一个巨大的、连续的上下文中追踪所有操作，而是可以专注于当前状态的任务，从而有效缓解了LLM的上下文窗口压力，避免了“计划遗忘”的问题。
1.2.2 通过“黑板”记忆机制优化上下文
为了进一步优化LLM的上下文管理，状态机模式通常与“黑板”（Blackboard）记忆机制结合使用。黑板作为一个中央知识库，用于存储和共享跨状态的关键信息，如设备UDID、当前页面描述、关键元素的坐标等。当Agent进入一个新状态时，它会首先更新黑板上的信息，例如记录新的页面截图、页面布局信息或从上一个状态传递过来的关键数据。同时，Agent会清除与旧状态相关的、不再需要的局部上下文，只将与当前状态相关的信息（如当前页面的描述、待执行的动作列表）传递给LLM。这种机制确保了LLM每次接收到的上下文都是精简且高度相关的，避免了无关信息的干扰。例如，当Agent在“地址选择页”时，LLM的上下文只需要包含该页面的截图、布局和待校验的文案，而不需要关心之前“首页”的详细信息。这种“记忆”与“遗忘”的有机结合，使得Agent能够在保持全局信息连贯性的同时，为LLM提供一个清晰的、聚焦的局部视图，从而提升决策的准确性和效率。
1.2.3 实现可配置的自主决策与重试逻辑
状态机模式为实现可配置的自主决策与重试逻辑提供了理想的载体。在每个状态内部，可以定义一系列“动作”（Actions）和“转换”（Transitions）。动作即需要执行的工具调用（如find_element、tap），而转换则定义了根据动作的执行结果如何切换到下一个状态。更重要的是，可以在状态机中嵌入自主决策规则。例如，可以配置一个通用规则：如果find_element动作返回“未找到元素”，则自动触发一个“等待3秒并重试”的子流程。如果重试多次仍然失败，则可以触发一个转换到“错误处理”状态的流程，或者将当前状态和错误信息提交给LLM，请求其进行决策。这种将决策逻辑与状态流程相结合的方式，使得自主性不再是空中楼阁，而是可以被精确地定义、配置和扩展。用户可以根据不同的测试场景和错误类型，灵活地定义各种重试和恢复策略，从而在严格遵循测试计划的大框架下，赋予Agent应对意外情况的“智能”，实现了机械执行与自主性的完美平衡。
2. 整体架构设计
2.1 架构概览：分层与模块化
为了构建一个既强大又灵活的移动端App测试Agent，我们采用分层与模块化的架构设计。整个系统被划分为三个核心层次：核心引擎层、工具执行层和决策与规划层。这种分层结构旨在实现关注点分离，使得各个模块可以独立开发、测试和扩展，从而提高整个系统的可维护性和可扩展性。
2.1.1 核心引擎层：状态机与记忆管理
核心引擎层是整个Agent的“大脑”，负责驱动测试流程的执行。该层主要由状态机（State Machine） 和黑板记忆（Blackboard） 两个核心模块构成。状态机模块负责解析预定义的测试计划（以状态机配置的形式），并根据当前状态和工具执行结果来驱动状态的转换。它维护着测试的生命周期，确保Agent按照预设的逻辑一步步执行。黑板记忆模块则作为Agent的中央知识库，存储着所有跨状态需要共享的关键信息，如设备信息、当前页面状态、元素坐标、历史执行日志等。状态机与黑板紧密协作：每当状态发生转换时，状态机会更新黑板上的信息，并为下一个状态准备必要的上下文。这种设计将流程控制与数据管理解耦，使得Agent的决策过程更加清晰和高效。
2.1.2 工具执行层：API Server与工具封装
工具执行层是Agent与外部世界（即被测的移动设备）交互的桥梁。该层通过一个API Server来统一管理和调度所有的测试工具。API Server是一个轻量级的HTTP服务器，它暴露了一系列RESTful API端点，每个端点对应一个具体的测试操作，如/find_element、/tap、/screenshot等。当核心引擎层的Agent需要执行某个操作时，它会向API Server发送一个HTTP请求。API Server接收到请求后，会调用相应的工具函数（这些函数是用户自创的，用于操作iOS/Android设备），并将执行结果以结构化的格式（如JSON）返回给Agent。这种设计的好处在于，它将具体的工具实现与Agent的核心逻辑完全解耦。Agent无需关心工具是如何与设备通信的（是通过Appium、UIAutomator还是其他框架），只需要通过统一的HTTP接口调用即可。这不仅简化了Agent的设计，也使得工具的添加、修改和替换变得更加容易，增强了系统的灵活性。
2.1.3 决策与规划层：LLM与规则引擎
决策与规划层是Agent“智能”的体现，主要由大型语言模型（LLM） 和一个规则引擎组成。规则引擎负责处理那些预定义的、可自动化的简单决策，例如“元素查找失败则重试两次”。这些规则是可配置的，可以根据不同的测试场景进行调整。当遇到规则引擎无法处理的复杂或未知情况时（例如，点击后页面跳转到了意料之外的页面），决策权将移交给LLM。LLM会接收到当前的状态信息、黑板记忆中的关键数据以及异常描述，然后基于其强大的推理能力，生成一个决策建议，例如“返回上一页并重新尝试”或“记录错误并终止测试”。这种分层决策机制（规则引擎处理简单情况，LLM处理复杂情况）在保证效率的同时，也充分利用了LLM的智能，实现了自主性与可控性的平衡。
2.2 关键组件交互流程
2.2.1 从测试计划到状态机配置的转换
整个测试流程始于一个结构化的测试计划，通常以JSON格式提供。这个测试计划详细描述了测试的目标、步骤、每个步骤的动作、参数以及期望结果。然而，这个原始的测试计划并不能直接被状态机执行。因此，需要一个转换模块，将这个线性的步骤列表转换为一个以App界面状态为核心的状态机配置文件。这个转换过程会分析测试步骤，识别出关键的页面跳转点（例如，点击“地址”元素后，预期会进入“地址选择页”），并以此为基础构建状态节点。每个状态节点会包含在该页面上需要执行的所有动作（如find_element、ocr_validate_text），以及根据这些动作的执行结果定义的状态转换规则。例如，在“地址选择页”状态下，会包含校验文案、查找搜索框、点击搜索框等一系列动作。最终生成的状态机配置文件，将成为驱动Agent执行的核心蓝图。
2.2.2 状态驱动的工具调用与结果反馈
一旦状态机配置准备就绪，Agent的核心引擎便开始工作。它从初始状态（例如“App首页”）开始，读取该状态下需要执行的动作列表。然后，它会依次向API Server发送请求，调用相应的工具。例如，如果当前动作是“查找左上角地址元素”，Agent会向API Server的/find_element端点发送一个包含元素描述（“左上角地址”）的HTTP请求。API Server接收到请求后，会调用底层的设备操作工具来执行查找，并将结果（例如，元素的坐标或“未找到”的错误信息）返回给Agent。这个返回结果是整个交互流程的关键，它不仅包含了操作是否成功的信息，还可能包含执行过程中产生的关键数据（如元素坐标），这些数据将被用于后续的动作。
2.2.3 基于工具返回的状态转换与记忆更新
Agent接收到API Server返回的结果后，会将其与动作定义中的“期望结果”进行比对。如果结果匹配（例如，find_element成功返回了元素坐标），Agent会根据状态机配置中定义的转换规则，决定下一个要进入的状态（例如，从“App首页”转换到“地址选择页”）。在状态转换的同时，黑板记忆模块也会进行相应的更新。新的页面信息、关键元素的坐标等会被写入黑板，而与旧状态相关的、不再需要的局部上下文信息则会被清除。这种“状态转换-记忆更新”的机制，确保了Agent的上下文始终是精简和相关的，有效地解决了LLM的“计划遗忘”问题。如果工具执行失败，状态机则会触发异常处理流程，根据预设的规则或LLM的决策来决定是重试、跳过还是终止测试，从而实现了Agent的自主异常处理能力。
3. 核心模块详细设计：状态机与记忆管理
3.1 状态机（State Machine）模块
状态机模块是Agent的指挥中心，负责解析测试计划、驱动流程执行和管理状态转换。它的设计目标是提供一个清晰、可配置且可扩展的框架，以应对复杂的测试场景。
3.1.1 状态（State）的定义与实现
******* 状态的识别：以App页面为核心
在本设计中，状态的识别严格遵循以App界面状态为核心“主键” 的原则。这意味着，一个“状态”的边界由一个或多个连续的、发生在同一个App页面（或功能模块）内的操作所界定。状态的切换由明确的页面跳转事件触发。例如，在美团App的测试场景中，我们可以定义以下几个核心状态：
MainPage: 代表App的首页，这是测试的起点。
AddressSelectionPage: 代表点击首页地址后进入的地址选择页面。
SearchPage: 代表在地址选择页点击搜索框后进入的地址搜索页面。
HomePage: 代表完成地址选择并返回后的首页（此时地址已更新）。
这种以页面为核心的状态划分方式，使得每个状态内部的上下文高度相关和聚焦。Agent在每个状态下只需要关心当前页面的元素和操作，极大地简化了决策的复杂性。状态的进入和退出由明确的动作结果来定义，例如，从MainPage到AddressSelectionPage的转换，是由tap操作成功执行并检测到页面变化所触发的。
******* 状态的数据结构：包含动作与转换规则
每个状态在实现上被设计为一个包含两个核心部分的数据结构：动作列表（Actions） 和转换规则（Transitions） 。
动作列表（Actions） : 这是一个有序的列表，定义了在该状态下需要依次执行的所有操作。列表中的每个动作都是一个字典，包含了执行该动作所需的所有信息：
tool: 要调用的工具名称，如"find_element", "tap", "ocr_validate_text"等。
parameters: 一个字典，包含了传递给工具的所有参数，例如{"element": "左上角地址"}。
expected_result: 一个字符串，定义了该动作成功执行后预期的返回结果，如"element_found"或"text_validated"。这个字段是状态转换的关键触发条件。
转换规则（Transitions） : 这是一个字典，定义了状态如何根据动作的执行结果进行切换。字典的键是expected_result的可能值，字典的值是目标状态的名称。例如，{"element_found": "AddressSelectionPage"}表示，如果find_element动作返回了"element_found"，那么Agent就应该从当前状态转换到"AddressSelectionPage"状态。
这种数据结构的设计，使得状态的逻辑完全可以通过一个配置文件（如JSON）来定义，从而实现了流程与代码的分离，提高了系统的灵活性和可维护性。
3.1.2 状态转换（Transition）逻辑
******* 转换的触发条件：工具执行结果
状态转换的触发条件是严格且明确的，即前一个动作的执行结果。Agent在执行完一个动作后，会将其返回结果与动作定义中的expected_result进行精确匹配。只有当两者完全一致时，转换才会被触发。这种基于结果的确定性转换机制，保证了测试流程的可预测性和可控性。例如，在AddressSelectionPage状态下，有一个动作是ocr_validate_text，其expected_result是"text_validated"。只有当OCR工具成功识别并校验了目标文本后，返回"text_validated"，状态机才会根据转换规则决定是否停留在当前状态或进入下一个状态。如果工具返回任何其他结果（如"text_not_found"），则转换不会被触发，Agent会进入异常处理流程。
******* 转换的目标状态：由配置规则决定
一旦转换被触发，目标状态由状态机配置中的transitions字典来决定。这个字典为每个可能的expected_result都指定了一个明确的目标状态。这种设计允许在同一个状态下，根据不同的成功结果导向不同的后续流程。例如，在AddressSelectionPage状态下，可能会有多个动作，每个动作成功后都可能导致不同的状态。ocr_validate_text成功后可能仍然停留在AddressSelectionPage以执行后续操作，而tap（点击搜索框）成功后则会转换到SearchPage。这种灵活的转换机制，使得状态机能够精确地模拟用户在App中的真实操作流程，从简单的线性流程到复杂的分支流程都能有效支持。
3.1.3 状态机配置（State Machine Config）
******* 从测试计划JSON生成状态机配置
状态机配置是整个系统的核心蓝图，它定义了测试的完整流程。这个配置通常由一个专门的转换模块，从用户提供的、更易于理解的结构化测试计划（JSON格式）中生成。转换过程会解析测试计划中的步骤列表，识别出关键的页面跳转点，并将线性的步骤序列组织成一个以状态为节点的图状结构。例如，一个包含19个步骤的测试计划，可能会被转换成包含4-5个核心状态的状态机配置。这个转换过程是自动化的，它将用户的意图（“做什么”）翻译成Agent可以执行的指令（“怎么做”），是连接人类测试工程师和自动化Agent的关键桥梁。
3.1.3.2 配置文件的JSON Schema定义
为了确保状态机配置的正确性和一致性，需要定义一个严格的JSON Schema。这个Schema详细规定了配置文件中每个字段的类型、结构和约束。例如，它会定义states字段必须是一个对象，其键是状态名称，值是包含actions和transitions的对象。actions必须是一个数组，数组中的每个元素必须是包含tool、parameters和expected_result的对象。通过使用JSON Schema，可以在配置加载时进行校验，及早发现格式错误或逻辑错误，避免因配置问题导致的运行时异常。这不仅提高了系统的健壮性，也为配置的编写和维护提供了清晰的指导。
3.2 黑板记忆（Blackboard）模块
黑板记忆模块是Agent的中央知识库，它解决了在状态转换过程中如何有效管理和传递信息的问题，是实现上下文感知和自主决策的基础。
3.2.1 黑板记忆的设计理念
3.2.1.1 作为Agent的中央知识库
黑板（Blackboard）模式源于人工智能领域，其核心思想是创建一个共享的数据空间，让不同的知识源（在这里是Agent的不同模块，如状态机、工具执行器、LLM决策器）可以读取和写入信息。在我们的设计中，黑板记忆模块扮演着Agent的中央知识库角色。所有在测试过程中产生的、需要跨状态使用的关键信息，都会被存储在黑板上。这包括但不限于：设备的唯一标识符（UDID）、当前页面的截图和布局信息、关键UI元素的坐标、从OCR识别出的文本内容、以及历史执行日志等。任何模块需要这些信息时，都可以直接从黑板上获取，而无需通过复杂的参数传递。这种集中式的数据管理，极大地简化了模块间的通信，降低了系统的耦合度。
3.2.1.2 实现上下文信息的精简与持久化
黑板记忆的另一个关键作用是实现上下文信息的精简与持久化。如前所述，LLM的上下文窗口是有限的。如果将所有历史信息都一股脑地塞给LLM，很快就会导致上下文溢出，并且会引入大量无关噪声，干扰LLM的决策。黑板记忆通过其更新和维护策略，解决了这个问题。当Agent进入一个新状态时，它会将旧状态中不再需要的信息从LLM的即时上下文中清除，只保留与当前状态高度相关的信息。同时，所有重要的历史信息（如成功找到的元素坐标）会被持久化地存储在黑板上。这样，LLM的上下文始终保持精简和相关，而Agent又能在需要时（例如，LLM决策需要参考历史信息）从黑板上获取完整的背景知识。这种“即时上下文”与“持久化记忆”的分离，是提升Agent性能和智能的关键。
3.2.2 黑板记忆的数据结构设计
黑板记忆的数据结构设计需要兼顾灵活性和结构性。一个典型的实现是使用一个字典（Dictionary）或类似的数据结构来存储键值对。以下是一些核心的数据字段设计：
******* 设备信息（Device Info）
udid: 存储当前连接设备的唯一标识符。这个信息在测试会话开始时由find_device工具获取，并贯穿整个测试过程，是所有设备操作的基础。
platform: 存储当前测试的平台，如"ios"或"android"。
******* 当前页面信息（Page Info）
current_page_name: 存储当前状态的名称，如"AddressSelectionPage"。
current_page_screenshot: 存储当前页面的截图（可以是图片数据的路径或Base64编码）。
current_page_layout: 存储当前页面的UI布局信息（如从get_layout工具获取的XML或JSON数据）。
******* 关键元素信息（Element Info）
elements: 一个字典，用于存储已找到的关键元素的信息。键可以是元素的描述（如"左上角地址"），值是另一个包含元素详细信息的字典，如{"x": 100, "y": 200, "width": 50, "height": 30}。
******* 执行日志（Execution Log）
execution_history: 一个列表，按时间顺序记录所有已执行的动作及其结果。每个条目可以是一个字典，包含timestamp, state, action, result等字段。这为LLM提供了回溯和分析的依据。
3.2.3 黑板记忆的更新与维护策略
******* 状态切换时的记忆更新
黑板记忆的更新与状态机的生命周期紧密相连。每当状态机发生状态转换时，都会触发一次黑板记忆的更新。具体来说，在离开旧状态之前，Agent会将该状态下产生的关键信息（如成功找到的元素坐标、OCR识别的文本）写入黑板。在进入新状态之后，Agent会立即调用相关工具（如screenshot、get_layout）来获取新页面的信息，并更新黑板上的current_page_*相关字段。这个过程确保了黑板上的信息始终与Agent当前所处的实际状态保持同步。
3.2.3.2 局部上下文的清除与保留
为了实现LLM上下文的精简，在状态切换时，Agent需要执行一个“上下文切换”操作。这包括清除LLM的即时工作记忆中与旧状态相关的详细信息（如旧页面的布局），同时从黑板上加载新状态所需的核心信息（如新页面的描述和待执行的动作列表）。然而，并非所有旧信息都会被丢弃。那些对后续步骤可能仍然有用的信息（如设备的UDID、已找到的通用元素坐标）会被保留在黑板上，以备不时之需。这种策略性的清除与保留，是黑板记忆模块实现其核心价值——平衡上下文相关性与历史信息持久性——的关键所在。
4. 工具执行与API Server模块
目前项目中已经有比较成熟的 tools 模块，具体在 tools 中。
5. 自主性与异常处理机制
5.1 自主决策规则引擎
5.1.1 规则的定义与配置
自主决策规则引擎是实现Agent“自主性”的核心组件之一，它负责处理那些可以预见和标准化的简单异常情况。规则引擎的设计基于一套可配置的“如果-那么”（IF-THEN）规则，这些规则定义了在特定条件下Agent应该采取的自动恢复动作。规则的配置文件可以采用JSON或YAML格式，以便于阅读和修改。一个典型的规则配置可能包含以下字段：
trigger: 触发规则的条件，通常与工具返回的result字段匹配，例如"element_not_found"。
action: 触发后执行的动作，如"retry"、"wait"、"skip"或"call_llm"。
parameters: 执行动作所需的参数，例如{"wait_seconds": 3, "max_retries": 2}。
scope: 规则的作用范围，可以是全局的，也可以是针对特定状态或特定工具的。
通过将决策逻辑外部化为配置文件，我们可以灵活地调整Agent的行为，而无需修改核心代码，从而实现了自主行为的可扩展性。
5.1.2 简单错误的自动重试机制
******* 元素查找失败的重试
元素查找失败是测试中最常见的瞬时性错误之一，通常由页面加载延迟或动画效果引起。针对这种情况，我们可以定义一条重试规则。当find_element工具返回"element_not_found"时，规则引擎会触发重试机制。具体流程如下：
首次失败: find_element返回"element_not_found"。
规则匹配: 规则引擎匹配到trigger为"element_not_found"的规则，该规则的action为"retry"。
执行等待: 根据规则中的parameters，Agent会等待指定的秒数（例如3秒）。
重新执行: 等待结束后，Agent会再次调用find_element工具。
重试计数: 每次重试都会增加一个计数器。如果重试次数超过了max_retries（例如2次），规则引擎将不再重试，而是将错误上报给更高级别的决策模块（如LLM）。
这种自动重试机制可以显著提高测试的鲁棒性，减少因临时性问题导致的测试失败。
5.1.2.2 页面加载超时的重试
页面加载超时是另一种常见的错误。在执行tap等可能导致页面跳转的操作后，如果新页面长时间未能加载完成，Agent可能会因为找不到新页面的元素而失败。为了处理这种情况，可以在tap操作后引入一个隐式等待和检查机制。规则可以定义为：在tap操作后，如果后续的元素查找操作连续失败，则触发页面加载超时的重试逻辑。这可能包括：
检查当前页面: 调用一个get_current_page工具，判断页面是否已经发生变化。
如果页面未变: 认为tap操作可能未生效，可以尝试重新执行tap。
如果页面已变但元素仍未找到: 可能是页面内容加载缓慢，可以增加等待时间后再次尝试查找元素。
这种机制确保了Agent在面对不确定的页面加载时间时，能够更加智能地等待和重试。
5.1.3 复杂错误的LLM决策机制
5.1.3.1 点击后页面未跳转的处理
当Agent执行tap操作后，如果页面没有发生预期的跳转，这是一个比元素查找失败更复杂的错误。这种情况可能由多种原因导致，例如：
点击的元素不是一个可点击的链接或按钮。
点击操作被其他UI元素（如弹窗）拦截。
App本身存在Bug，导致点击无响应。
对于这种复杂错误，简单的重试机制可能无效。此时，规则引擎应该将决策权移交给LLM。Agent会构建一个详细的Prompt，包含当前页面的截图、布局、被点击的元素信息以及操作历史，然后询问LLM：“我点击了‘搜索’按钮，但页面没有跳转，可能是什么原因？下一步应该怎么做？” LLM可以基于其强大的推理能力，分析可能的原因，并给出建议，例如：
“检查页面上是否有弹窗，如果有，先关闭它。”
“尝试点击页面上的其他‘搜索’按钮。”
“截图并记录错误，然后终止测试。”
5.1.3.2 连续失败后的测试终止与清理
当一个测试步骤连续多次失败，并且所有自动重试和LLM决策都无法解决问题时，Agent应该能够优雅地终止当前测试用例，并执行必要的清理操作。这可以防止Agent陷入无限循环，并确保测试环境的稳定。终止流程可以包括：
记录错误: 将失败的步骤、错误信息、相关截图和日志记录到测试报告中。
执行清理: 调用end_test工具，关闭App会话，释放设备资源。
状态转换: 将状态机转换到TestAbortedState，并通知上层测试管理系统。
这种在无法恢复的错误面前的终止机制，是构建一个负责任、可维护的自动化测试系统的关键。
5.2 异常处理流程
5.2.1 工具执行异常的捕获
异常处理的第一步是在最底层——工具执行层——捕获所有可能的异常。API Server中的每个路由处理函数都必须使用try...except块来包裹对底层工具函数的调用。这可以捕获由设备驱动、网络问题或工具内部逻辑错误引发的各种异常，例如DeviceNotFoundError、ElementNotFoundError、TimeoutError等。捕获异常后，API Server不应直接崩溃，而是应该将异常信息转换成一个结构化的错误响应，返回给调用方（即Agent的核心引擎）。
5.2.2 异常信息的记录与上报
当异常发生时，仅仅返回一个错误码是不够的。为了便于调试和问题追溯，必须对异常信息进行详细的记录和上报。这包括：
记录到黑板记忆: 将异常的类型、错误消息、发生时间以及当时的上下文信息（如当前状态、操作参数）记录到黑板记忆的execution_log中。
生成调试信息: 在发生异常时，自动截取当前设备屏幕的截图，并获取页面布局信息，将这些文件保存到本地，并将路径记录到日志中。
上报给决策层: 将结构化的异常信息传递给上层的规则引擎或LLM，作为它们进行决策的依据。
5.2.3 异常恢复与状态回滚
在某些情况下，异常发生后可能需要进行恢复操作，以使Agent回到一个已知的、稳定的状态。例如，如果一个操作意外地跳转到了一个错误的页面，Agent可能需要执行“返回”操作，回到上一个页面，然后重新尝试。这种恢复逻辑可以由LLM来决策，也可以预定义在状态机配置中。状态回滚是一种更高级的恢复机制，它要求Agent能够撤销之前的一系列操作，回到一个更早的稳定状态。实现状态回滚需要Agent能够记录足够详细的历史操作信息，并具备执行逆向操作的能力（例如，如果之前执行了input_text，回滚时可能需要清空输入框）。虽然实现起来较为复杂，但它能极大地提高Agent在复杂异常情况下的生存能力。
6. 与LLM的集成与交互
6.1 LLM在系统中的角色
6.1.1 作为决策引擎处理复杂错误
在本架构中，LLM的核心角色是高级决策引擎，专门用于处理那些无法通过预设规则解决的复杂或未知的异常情况。当状态机在执行过程中遇到一个非预期的结果，并且规则引擎没有匹配的规则或规则执行失败时，LLM将被调用。例如，当tap操作后页面没有跳转，或者App进入了一个完全未知的界面时，Agent会将当前的上下文信息（包括状态、黑板记忆、页面截图、历史操作等）构建成一个Prompt，提交给LLM。LLM利用其强大的自然语言理解和推理能力，分析当前状况，判断可能的原因，并给出一个高层次的决策建议，如“尝试点击页面上的返回按钮”、“跳过当前步骤”或“终止测试并记录错误”。这种将复杂决策外包给LLM的模式，使得Agent能够展现出真正的“智能”，而不仅仅是一个僵化的脚本执行器。
6.1.2 作为规划器生成测试计划（可选）
除了作为决策引擎，LLM还可以扮演测试计划规划器的角色。虽然当前项目的主要目标是执行预定义的结构化测试计划，但在未来，可以扩展LLM的功能，使其能够根据一个高层次的测试需求（例如，“测试一下美团App的地址选择功能”），自动生成详细的、结构化的测试计划JSON。LLM可以分析需求，将其分解为一系列逻辑步骤，并为每个步骤指定合适的工具、参数和预期结果。这将进一步降低编写自动化测试用例的门槛，使得不具备编程背景的测试人员也能通过自然语言与Agent交互，创建复杂的测试场景。
6.2 构建传递给LLM的Prompt
6.2.1 Prompt的组成部分：当前状态、黑板记忆、异常信息
为了让LLM能够做出准确的决策，传递给它的Prompt必须包含充分且相关的上下文信息。一个精心构建的Prompt通常由以下几个部分组成：
系统指令（System Instruction） : 定义LLM的角色和任务，例如：“你是一个智能的移动端App测试助手。你的任务是根据提供的上下文信息，分析测试过程中遇到的问题，并给出下一步的最佳行动建议。”
当前状态（Current State） : 明确指出Agent当前所处的状态，例如"当前状态：AddressSelectionPage"。
黑板记忆摘要（Blackboard Summary） : 从黑板记忆中提取最关键的信息，包括：
设备信息（udid, platform）。
当前页面的简要描述（page_title, ocr_text摘要）。
上一个成功执行的动作及其结果。
历史执行日志的简要摘要（例如，最近3-5步的操作）。
异常信息（Error Information） : 详细描述当前遇到的问题，包括失败的工具名称、传入的参数、工具返回的错误信息（result和message）以及相关的截图。
决策请求（Decision Request） : 明确提出需要LLM解决的问题，例如：“find_element工具未能找到‘搜索框’元素。请分析可能的原因，并告诉我下一步应该怎么做。可选的行动包括：1. 等待3秒后重试。2. 尝试点击页面上的其他区域。3. 跳过此步骤。4. 终止测试。”
6.2.2 保持Prompt的精简与相关性
尽管需要提供足够的信息，但Prompt的长度必须受到严格控制，以避免超出LLM的上下文窗口限制，并减少无关信息的干扰。因此，在构建Prompt时，必须遵循精简与相关性的原则：
信息筛选: 只包含与当前决策最相关的信息。例如，在分析元素查找失败的问题时，无需提供与当前页面无关的历史操作细节。
信息摘要: 对于长篇的文本（如OCR结果），可以进行摘要或只提取包含关键词的句子。
结构化呈现: 使用清晰的标题、列表和JSON格式来组织信息，使LLM能够更容易地解析和理解。
动态调整: 根据不同的错误类型，动态地调整Prompt的内容。例如，对于视觉相关的问题（如元素定位），可以强调截图信息；对于逻辑相关的问题，可以强调历史操作日志。
6.3 处理LLM的返回与决策
6.3.1 解析LLM的决策指令
LLM的返回通常是一段自然语言文本，其中包含了它的分析和决策建议。为了将LLM的建议转化为Agent可以执行的操作，必须对返回的文本进行解析。这可以通过以下几种方式实现：
关键词匹配: 在LLM的返回文本中查找预定义的关键词，如"retry"、"skip"、"abort"等。
结构化输出: 在Prompt中明确要求LLM以特定的JSON格式返回决策，例如：{"action": "retry", "reason": "页面可能加载延迟", "parameters": {"wait_seconds": 5}}。这种方式更可靠，易于解析。
函数调用（Function Calling） : 如果使用的LLM支持函数调用功能，可以定义一系列决策函数（如retry_action(), skip_step()），让LLM直接选择并调用合适的函数。
6.3.2 将决策指令转化为状态机操作
解析出LLM的决策指令后，最后一步是将其转化为对状态机的具体操作。这通常意味着更新状态机的状态或触发某个特定的转换。例如：
如果LLM的决策是"retry"，状态机可能会重置当前状态的某个计数器，并重新执行当前动作。
如果LLM的决策是"skip"，状态机可能会忽略当前步骤的失败，并转换到下一个状态。
如果LLM的决策是"abort"，状态机将转换到TestAbortedState，并执行相应的清理操作。
通过这一系列从Prompt构建到决策执行的流程，Agent能够有效地利用LLM的智能，处理复杂的异常情况，从而实现更高水平的自主性和鲁棒性。
7. 阶段性测试与实现步骤
为了确保项目的顺利进行并及早发现问题，建议将整个开发过程划分为四个阶段，每个阶段都有明确的目标和可交付的成果。
7.1 第一阶段：基础模块搭建与测试
7.1.1 实现黑板记忆模块并测试
目标: 创建一个功能完整的Blackboard类，能够存储、更新和查询设备信息、页面信息、元素信息和执行日志。
任务:
定义Blackboard类的数据结构和API（如set_device_info(), update_page_info(), get_element_info()等）。
编写单元测试，验证每个API的功能是否正常。
模拟状态切换，测试黑板记忆的更新和清除策略是否正确。
交付物: 通过单元测试的blackboard.py模块。
7.1.2 实现API Server与一个工具并测试
目标: 搭建一个基于Flask的API Server，并成功封装一个工具（例如find_element）。
任务:
创建Flask应用，并定义/find_element端点。
封装底层的find_element工具函数，确保其能被API端点调用。
实现统一的请求/响应JSON格式和异常处理逻辑。
使用Postman或curl等工具手动测试API端点，验证其功能。
交付物: 可运行的api_server.py和一个通过手动测试的API端点。
7.1.3 实现简单的状态机并测试
目标: 创建一个基础的状态机框架，能够加载配置并驱动一个简单的流程。
任务:
定义状态机的核心类，能够解析一个简单的状态机配置（JSON）。
实现状态的进入、执行和退出逻辑。
实现基于工具返回结果的简单状态转换。
编写一个包含2-3个状态的简单测试用例（例如：启动App -> 点击按钮 -> 退出），并验证状态机能否正确执行。
交付物: 可运行的state_machine.py模块和一个通过测试的简单状态机配置。
7.2 第二阶段：核心流程集成与测试
7.2.1 集成状态机、黑板与API Server
目标: 将第一阶段实现的三个核心模块连接起来，形成一个可以协同工作的整体。
任务:
修改状态机，使其在执行动作时，通过调用API Server的端点来完成。
修改状态机，使其在状态转换时，能够正确地更新黑板记忆。
确保模块间的数据流（如API返回结果被状态机接收，状态机更新黑板）是通畅的。
交付物: 一个能够驱动API Server工具并管理黑板记忆的集成状态机。
7.2.2 实现从测试计划到状态机配置的转换
目标: 开发一个转换器，能够将用户提供的JSON测试计划自动转换为状态机配置文件。
任务:
编写一个plan_converter.py脚本。
实现解析测试计划、识别状态、构建转换图的逻辑。
使用用户提供的美团测试计划作为输入，生成对应的状态机配置。
验证生成的配置文件是否符合JSON Schema。
交付物: plan_converter.py脚本和生成的状态机配置文件。
7.2.3 测试完整的单步执行流程
目标: 验证整个系统能够完整地执行一个单步操作，并正确处理其结果。
任务:
使用转换器生成的状态机配置。
启动Agent，观察它是否能从初始状态开始，成功执行第一个动作（如find_device）。
验证API Server是否被正确调用，并返回了预期结果。
验证状态机是否根据返回结果正确地更新了黑板记忆，并准备转换到下一个状态。
交付物: 成功执行单步操作的日志和报告。
7.3 第三阶段：自主性与异常处理实现
7.3.1 实现简单的重试规则
目标: 为Agent添加基于规则的自主重试能力。
任务:
实现规则引擎模块，能够加载和解析规则配置文件。
定义针对element_not_found等常见错误的重试规则。
修改状态机，在工具执行失败后，首先调用规则引擎进行决策。
编写测试用例，模拟元素查找失败，验证Agent是否能自动重试。
交付物: 包含规则引擎和重试功能的Agent。
7.3.2 集成LLM进行复杂错误决策
目标: 将LLM集成到决策流程中，处理规则引擎无法解决的复杂错误。
任务:
实现llm_client.py模块，负责与本地LLM服务（如LM Studio）进行通信。
实现prompt_builder.py模块，负责根据当前上下文构建传递给LLM的Prompt。
修改规则引擎，当规则无法匹配或重试失败时，调用LLM进行决策。
编写测试用例，模拟点击后页面未跳转等复杂错误，验证LLM是否能被正确调用并给出决策。
交付物: 能够调用LLM进行复杂决策的Agent。
7.3.3 测试完整的测试用例执行与异常处理
目标: 使用完整的美团测试用例，验证Agent在真实场景下的执行能力和异常处理能力。
任务:
使用完整的美团测试计划JSON。
启动Agent，观察其是否能从头到尾完整地执行整个测试流程。
在测试过程中，人为制造一些异常（如网络延迟、遮挡元素），观察Agent是否能正确处理（重试或请求LLM决策）。
分析最终的测试报告，检查所有步骤的执行结果和异常处理记录。
交付物: 完整的测试执行报告和日志。
7.4 第四阶段：优化与扩展
7.4.1 优化状态识别与转换逻辑
目标: 提高状态识别的准确性和状态转换的鲁棒性。
任务:
改进analysis_now_page工具，结合多种特征（如页面标题、关键元素、布局哈希）来更准确地识别页面。
优化状态转换逻辑，处理更复杂的分支和循环流程。
对状态机配置进行性能分析，优化状态图的复杂度。
7.4.2 扩展自主性规则库
目标: 根据实际测试中遇到的问题，不断丰富自主性规则库。
任务:
收集和分析测试过程中出现的各种异常。
为新的异常类型定义相应的处理规则。
实现更复杂的规则，如条件规则（IF-THEN-ELSE）或基于概率的决策。
7.4.3 性能与稳定性测试
目标: 对Agent进行全面的性能和稳定性测试，确保其能够在长时间、高并发的场景下稳定运行。
任务:
进行压力测试，模拟连续执行大量测试用例。
进行稳定性测试，长时间运行Agent，观察是否存在内存泄漏或资源耗尽的问题。
对Agent的响应时间进行基准测试，分析性能瓶颈。
交付物: 性能测试报告和稳定性评估。