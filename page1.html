<!DOCTYPE html><html lang="zh-CN"><head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>解决LLM-Agent在长步骤UI测试中的记忆与自主性平衡问题</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css"/>
    <script src="https://cdn.jsdelivr.net/npm/mermaid@10/dist/mermaid.min.js"></script>
    <style>
        * {
            box-sizing: border-box;
        }
        img {
            max-width: 100%;
            height: auto;
        }
        body { font-family: 'Inter', sans-serif; }
        .toc-fixed { position: fixed; top: 0; left: 0; width: 280px; height: 100vh; overflow-y: auto; z-index: 40; background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%); border-right: 1px solid #cbd5e1; }
        .main-content { margin-left: 280px; min-height: 100vh; }
        .hero-gradient { background: linear-gradient(135deg, #1e40af 0%, #3b82f6 50%, #60a5fa 100%); }
        .text-shadow { text-shadow: 0 2px 4px rgba(0,0,0,0.3); }
        .glass-effect { backdrop-filter: blur(10px); background: rgba(255,255,255,0.9); }
        .citation { color: #3b82f6; text-decoration: none; font-weight: 500; }
        .citation:hover { text-decoration: underline; }
        .section-divider { background: linear-gradient(90deg, #3b82f6, #60a5fa, #3b82f6); height: 2px; }
        
        /* Mermaid chart styling */
        .mermaid-container {
            display: flex;
            justify-content: center;
            min-height: 300px;
            max-height: 800px;
            background: #ffffff;
            border: 2px solid #e5e7eb;
            border-radius: 12px;
            padding: 30px;
            margin: 30px 0;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.08);
            position: relative;
            overflow: hidden;
        }

        .mermaid-container .mermaid {
            width: 100%;
            max-width: 100%;
            height: 100%;
            cursor: grab;
            transition: transform 0.3s ease;
            transform-origin: center center;
            display: flex;
            justify-content: center;
            align-items: center;
            touch-action: none; /* 防止触摸设备上的默认行为 */
            -webkit-user-select: none; /* 防止文本选择 */
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        .mermaid-container .mermaid svg {
            max-width: 100%;
            height: 100%;
            display: block;
            margin: 0 auto;
        }

        .mermaid-container .mermaid:active {
            cursor: grabbing;
        }

        .mermaid-container.zoomed .mermaid {
            height: 100%;
            width: 100%;
            cursor: grab;
        }

        .mermaid-controls {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 10px;
            z-index: 20;
            background: rgba(255, 255, 255, 0.95);
            padding: 8px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        }

        .mermaid-control-btn {
            background: #ffffff;
            border: 1px solid #d1d5db;
            border-radius: 6px;
            padding: 10px;
            cursor: pointer;
            transition: all 0.2s ease;
            color: #374151;
            font-size: 14px;
            min-width: 36px;
            height: 36px;
            text-align: center;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .mermaid-control-btn:hover {
            background: #f8fafc;
            border-color: #3b82f6;
            color: #3b82f6;
            transform: translateY(-1px);
        }

        .mermaid-control-btn:active {
            transform: scale(0.95);
        }

        /* Enhanced mermaid theme for better contrast and unified styling */
        .mermaid .node rect,
        .mermaid .node circle,
        .mermaid .node ellipse,
        .mermaid .node polygon {
            stroke-width: 2px;
            filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
        }
        
        /* Primary nodes - blue theme */
        .mermaid .node.default rect,
        .mermaid .node rect {
            fill: #dbeafe !important;
            stroke: #2563eb !important;
        }
        
        .mermaid .node.default .label,
        .mermaid .node .label {
            color: #1e40af !important;
            font-weight: 600 !important;
            font-size: 14px !important;
        }
        
        /* Success/end nodes - green theme */
        .mermaid .node[id*="end"] rect,
        .mermaid .node[id*="END"] rect {
            fill: #dcfce7 !important;
            stroke: #16a34a !important;
        }
        
        .mermaid .node[id*="end"] .label,
        .mermaid .node[id*="END"] .label {
            color: #15803d !important;
            font-weight: 600 !important;
        }
        
        /* Failure/error nodes - red theme */
        .mermaid .node[id*="fail"] rect,
        .mermaid .node[id*="FAIL"] rect {
            fill: #fef2f2 !important;
            stroke: #dc2626 !important;
        }
        
        .mermaid .node[id*="fail"] .label,
        .mermaid .node[id*="FAIL"] .label {
            color: #b91c1c !important;
            font-weight: 600 !important;
        }
        
        /* Start nodes - blue theme */
        .mermaid .node[id*="start"] rect,
        .mermaid .node[id*="START"] rect {
            fill: #dbeafe !important;
            stroke: #1e40af !important;
            stroke-width: 3px !important;
        }
        
        .mermaid .node[id*="start"] .label,
        .mermaid .node[id*="START"] .label {
            color: #1e40af !important;
            font-weight: 700 !important;
        }
        
        /* Edge styling */
        .mermaid .edgePath .path {
            stroke: #64748b !important;
            stroke-width: 2px !important;
            filter: drop-shadow(0 1px 1px rgba(0, 0, 0, 0.1));
        }
        
        .mermaid .edgeLabel {
            background-color: #ffffff !important;
            border: 1px solid #e2e8f0 !important;
            border-radius: 6px !important;
            padding: 2px 6px !important;
            font-size: 12px !important;
            font-weight: 500 !important;
            color: #475569 !important;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) !important;
        }
        
        /* Decision nodes - yellow theme */
        .mermaid .node[id*="decision"] polygon,
        .mermaid .node[id*="DECISION"] polygon {
            fill: #fef3c7 !important;
            stroke: #d97706 !important;
            stroke-width: 2px !important;
        }
        
        .mermaid .node[id*="decision"] .label,
        .mermaid .node[id*="DECISION"] .label {
            color: #92400e !important;
            font-weight: 600 !important;
        }
        
        /* Process nodes - purple theme */
        .mermaid .node[id*="process"] rect,
        .mermaid .node[id*="PROCESS"] rect {
            fill: #f3e8ff !important;
            stroke: #9333ea !important;
        }
        
        .mermaid .node[id*="process"] .label,
        .mermaid .node[id*="PROCESS"] .label {
            color: #7c3aed !important;
            font-weight: 600 !important;
        }
        
        /* Ensure text contrast for all node types */
        .mermaid text {
            font-family: 'Inter', sans-serif !important;
            font-size: 13px !important;
            font-weight: 500 !important;
        }

@media (max-width: 1024px) {
    .toc-fixed { 
        width: 0;
        transform: translateX(-100%);
        transition: transform 0.3s ease;
    }
    .toc-fixed.active {
        transform: translateX(0);
        width: 280px;
    }
    .main-content { 
        margin-left: 0;
        transition: margin-left 0.3s ease;
    }
    .main-content.toc-active {
        margin-left: 280px;
    }
    
    .mermaid-control-btn:not(.reset-zoom) {
        display: none;
    }
    .mermaid-controls {
        top: auto;
        bottom: 15px;
        right: 15px;
    }
}

/* Responsive images */
@media (max-width: 768px) {
    .hero-gradient {
        padding: 1rem;
    }
    .hero-gradient h1 {
        font-size: 2.25rem;
        line-height: 1.2;
    }
    .hero-gradient p {
        font-size: 1rem;
    }
    .bento-grid {
        grid-template-columns: 1fr;
    }
    section {
        padding-left: 1rem;
        padding-right: 1rem;
    }
}
    </style>
  </head>

  <body class="bg-slate-50 text-slate-800 overflow-x-hidden">
    <!-- Table of Contents -->
    <nav class="toc-fixed p-6">
      <div class="mb-8">
        <h2 class="text-lg font-bold text-slate-800 mb-4">目录导航</h2>
        <div class="space-y-2">
          <a href="#problem-diagnosis" class="block text-sm text-slate-600 hover:text-blue-600 transition-colors">1. 核心问题诊断</a>
          <div class="ml-3 space-y-1">
            <a href="#context-window" class="block text-xs text-slate-500 hover:text-blue-500">1.1 上下文窗口限制</a>
            <a href="#model-limitations" class="block text-xs text-slate-500 hover:text-blue-500">1.2 模型能力局限</a>
            <a href="#memory-strategy" class="block text-xs text-slate-500 hover:text-blue-500">1.3 记忆策略不足</a>
          </div>

          <a href="#memory-optimization" class="block text-sm text-slate-600 hover:text-blue-600 transition-colors">2. 记忆管理优化</a>
          <div class="ml-3 space-y-1">
            <a href="#long-term-memory" class="block text-xs text-slate-500 hover:text-blue-500">2.1 长期记忆系统</a>
            <a href="#prompt-engineering" class="block text-xs text-slate-500 hover:text-blue-500">2.2 提示工程优化</a>
          </div>

          <a href="#multi-model" class="block text-sm text-slate-600 hover:text-blue-600 transition-colors">3. 多模型协作架构</a>
          <div class="ml-3 space-y-1">
            <a href="#factored-agent" class="block text-xs text-slate-500 hover:text-blue-500">3.1 因子化代理模式</a>
            <a href="#autogen-framework" class="block text-xs text-slate-500 hover:text-blue-500">3.2 AutoGen框架</a>
          </div>

          <a href="#state-machine" class="block text-sm text-slate-600 hover:text-blue-600 transition-colors">4. 状态机流程控制</a>
          <div class="ml-3 space-y-1">
            <a href="#state-core" class="block text-xs text-slate-500 hover:text-blue-500">4.1 核心思想</a>
            <a href="#state-implementation" class="block text-xs text-slate-500 hover:text-blue-500">4.2 实现方案</a>
          </div>

          <a href="#autonomy" class="block text-sm text-slate-600 hover:text-blue-600 transition-colors">5. 自主性实现机制</a>
          <div class="ml-3 space-y-1">
            <a href="#error-handling" class="block text-xs text-slate-500 hover:text-blue-500">5.1 错误处理</a>
            <a href="#early-termination" class="block text-xs text-slate-500 hover:text-blue-500">5.2 提前终止</a>
          </div>

          <a href="#ui-perception" class="block text-sm text-slate-600 hover:text-blue-600 transition-colors">6. UI感知能力增强</a>
          <div class="ml-3 space-y-1">
            <a href="#ocr-integration" class="block text-xs text-slate-500 hover:text-blue-500">6.1 OCR集成</a>
            <a href="#multimodal-models" class="block text-xs text-slate-500 hover:text-blue-500">6.2 多模态模型</a>
          </div>
        </div>
      </div>
    </nav>

    <!-- Main Content -->
    <main class="main-content">
      <!-- Hero Section -->
      <section class="hero-gradient text-white relative overflow-hidden">
        <div class="absolute inset-0 bg-black bg-opacity-20"></div>
        <div class="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 py-16 lg:py-24">
          <!-- Bento Grid Layout -->
          <div class="bento-grid grid grid-cols-1 lg:grid-cols-3 gap-8 items-center">
            <!-- Title Card -->
            <div class="lg:col-span-2 space-y-6">
              <h1 class="text-4xl sm:text-5xl lg:text-6xl font-bold leading-tight text-shadow">
                解决LLM-Agent在长步骤UI测试中的
                <span class="italic text-blue-200">记忆与自主性</span>
                平衡问题
              </h1>
              <p class="text-lg sm:text-xl lg:text-2xl text-blue-100 font-light leading-relaxed max-w-full sm:max-w-2xl">
                通过状态机解耦流程控制与模型决策，实现严格遵循测试计划的同时保持智能自主性
              </p>
            </div>

            <!-- Visual Element -->
            <div class="relative">
              <div class="glass-effect rounded-2xl p-4 sm:p-8 border border-white border-opacity-20">
                <div class="text-center space-y-4">
                  <div class="text-3xl sm:text-4xl font-bold text-slate-800">30B</div>
                  <div class="text-sm sm:text-base text-slate-600">本地模型规模</div>
                  <div class="h-px bg-slate-300"></div>
                  <div class="text-xl sm:text-2xl font-bold text-slate-800">20+</div>
                  <div class="text-sm sm:text-base text-slate-600">测试步骤</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Key Highlights -->
      <section class="bg-white py-12">
        <div class="max-w-7xl mx-auto px-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center space-y-3">
              <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-brain text-2xl text-blue-600"></i>
              </div>
              <h3 class="text-lg font-semibold text-slate-800">宏观控制</h3>
              <p class="text-sm text-slate-600">状态机管理整体流程，确保计划严格执行</p>
            </div>
            <div class="text-center space-y-3">
              <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-robot text-2xl text-green-600"></i>
              </div>
              <h3 class="text-lg font-semibold text-slate-800">微观自主</h3>
              <p class="text-sm text-slate-600">LLM在单个步骤内智能决策与自适应调整</p>
            </div>
            <div class="text-center space-y-3">
              <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                <i class="fas fa-eye text-2xl text-purple-600"></i>
              </div>
              <h3 class="text-lg font-semibold text-slate-800">视觉感知</h3>
              <p class="text-sm text-slate-600">OCR与多模态模型增强UI理解能力</p>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Core Problem Analysis -->
      <section id="problem-diagnosis" class="py-16 bg-slate-50">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">核心问题诊断：模型为何会&#34;忘记&#34;测试计划</h2>

          <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center mb-16">
            <div class="space-y-6">
              <p class="text-lg text-slate-700 leading-relaxed">
                在构建由大型语言模型（LLM）驱动的自主代理（Agent）以执行复杂的UI自动化测试时，一个核心的挑战在于如何平衡严格遵循预设计划与保持自主决策能力。
              </p>
              <p class="text-lg text-slate-700 leading-relaxed">
                用户提供的场景中，一个本地的30B模型在执行一个包含20个步骤的测试计划时，往往在完成前几个步骤后便开始偏离预定轨道。这种现象并非偶然，而是由LLM在架构和实现上的固有特性所决定的。
              </p>
            </div>
            <div class="bg-white rounded-2xl p-8 shadow-lg border border-slate-200">
              <img src="https://kimi-web-img.moonshot.cn/img/moonlight-paper-snapshot.s3.ap-northeast-2.amazonaws.com/28ea435fbb30e58efd5cbbff4488dc7d90774daf.png" alt="LLM记忆限制的抽象概念图" class="w-full rounded-lg" size="medium" aspect="wide" query="大型语言模型记忆限制" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>
            </div>
          </div>

          <!-- Context Window Limitations -->
          <div id="context-window" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <i class="fas fa-window-maximize text-blue-600 mr-3"></i>
              上下文窗口限制：长序列任务的天然瓶颈
            </h3>
            <div class="bg-white rounded-xl p-8 shadow-lg border border-slate-200">
              <p class="text-slate-700 mb-6">
                大型语言模型（LLM）的核心工作机制是基于对输入文本（即&#34;上下文&#34;）的理解和生成。然而，任何模型能够处理的上下文长度都是有限的，这个长度被称为&#34;上下文窗口&#34;（Context Window）。
              </p>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
                <div class="space-y-4">
                  <h4 class="font-semibold text-slate-800">问题表现</h4>
                  <ul class="space-y-2 text-slate-700">
                    <li class="flex items-start">
                      <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-1"></i>
                      当任务序列过长时，信息量很容易超出本地30B模型的上下文窗口限制
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-1"></i>
                      模型为了处理新信息，不得不丢弃早期部分上下文
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-exclamation-triangle text-yellow-500 mr-2 mt-1"></i>
                      导致忘记测试计划的初始目标和已完成步骤
                    </li>
                  </ul>
                </div>
                <div class="space-y-4">
                  <h4 class="font-semibold text-slate-800">影响后果</h4>
                  <ul class="space-y-2 text-slate-700">
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-red-500 mr-2 mt-1"></i>
                      无法做出与全局计划一致的决策
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-red-500 mr-2 mt-1"></i>
                      只能基于最近几步进行模糊推测
                    </li>
                    <li class="flex items-start">
                      <i class="fas fa-arrow-right text-red-500 mr-2 mt-1"></i>
                      最终导致测试流程中断或偏离
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>

          <!-- Model Limitations -->
          <div id="model-limitations" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <i class="fas fa-cogs text-blue-600 mr-3"></i>
              本地30B模型能力局限：规划与记忆保持的挑战
            </h3>
            <div class="bg-white rounded-xl p-8 shadow-lg border border-slate-200">
              <blockquote class="border-l-4 border-blue-500 pl-6 italic text-lg text-slate-700 mb-6">
                &#34;尽管30B参数的模型在许多自然语言处理任务上表现出色，但在处理需要长期规划和精确记忆保持的复杂任务时，其能力仍然存在局限性。&#34;
              </blockquote>
              <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <div class="bg-slate-50 rounded-lg p-6">
                  <h4 class="font-semibold text-slate-800 mb-3">推理深度限制</h4>
                  <p class="text-sm text-slate-600">与更大规模模型相比，30B模型在推理深度和指令遵循精确性上较弱</p>
                </div>
                <div class="bg-slate-50 rounded-lg p-6">
                  <h4 class="font-semibold text-slate-800 mb-3">长程依赖捕捉</h4>
                  <p class="text-sm text-slate-600">对长程依赖关系的捕捉能力不足，影响对&#34;大局&#34;的把握</p>
                </div>
                <div class="bg-slate-50 rounded-lg p-6">
                  <h4 class="font-semibold text-slate-800 mb-3">动态适应困难</h4>
                  <p class="text-sm text-slate-600">缺乏稳定、持久的外部记忆系统，难以从经验中学习</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Memory Strategy Issues -->
          <div id="memory-strategy" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-6 flex items-center">
              <i class="fas fa-database text-blue-600 mr-3"></i>
              现有记忆策略的不足：简单存储无法替代动态推理
            </h3>
            <div class="bg-white rounded-xl p-8 shadow-lg border border-slate-200">
              <p class="text-slate-700 mb-6">
                为了解决上下文窗口的限制，开发者通常会尝试引入外部记忆系统。然而，简单的记忆策略往往难以满足复杂UI测试的需求。
              </p>

              <div class="bg-red-50 border border-red-200 rounded-lg p-6 mb-6">
                <h4 class="font-semibold text-red-800 mb-3">常见问题模式</h4>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="fas fa-times-circle text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">上下文消耗：</strong>
                      <span class="text-red-700">将所有历史信息都塞入提示，迅速消耗宝贵上下文窗口</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-times-circle text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">效率低下：</strong>
                      <span class="text-red-700">模型需从冗长历史记录中筛选相关信息，效率低且易出错</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-times-circle text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">自主性剥夺：</strong>
                      <span class="text-red-700">模型变成简单脚本执行器，失去根据实时反馈决策的能力</span>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-blue-50 border border-blue-200 rounded-lg p-6">
                <h4 class="font-semibold text-blue-800 mb-3">根本矛盾</h4>
                <p class="text-blue-700">
                  用户期望的自主性，如&#34;元素查找失败时等待并重试&#34;，在这种僵化的记忆策略下很难实现，因为模型的决策空间被严格限制在预设的步骤之内。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Memory Optimization -->
      <section id="memory-optimization" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案一：优化记忆管理策略</h2>

          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              为了克服本地30B模型在长步骤UI测试中因上下文窗口限制而导致的&#34;遗忘&#34;问题，优化记忆管理策略是首要且关键的步骤。
              需要转向更智能、更高效的记忆系统，在保证模型能够获取足够历史上下文的同时，为模型的自主推理和决策保留充足的&#34;思考空间&#34;。
            </p>
          </div>

          <!-- Long-term Memory Systems -->
          <div id="long-term-memory" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-archive text-blue-600 mr-3"></i>
              引入长期记忆系统
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-3 gap-8 mb-8">
              <!-- JSON Storage -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-file-code text-yellow-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">JSON文件存储</h4>
                </div>
                <p class="text-slate-600 mb-4 text-sm">
                  直接且易于实现的持久化存储方案，结构化记录每一步操作和状态信息。
                </p>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">实现简单，无需额外数据库</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-times text-red-500 mr-2"></i>
                    <span class="text-sm text-slate-700">检索效率低，缺乏智能过滤</span>
                  </div>
                </div>
              </div>

              <!-- Vector Store -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-vector-square text-blue-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">向量数据库</h4>
                </div>
                <p class="text-slate-600 mb-4 text-sm">
                  通过嵌入模型将文本转换为向量，支持基于语义的智能检索。
                </p>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">语义理解，智能检索</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">高效相似度搜索</span>
                  </div>
                </div>
              </div>

              <!-- A-Mem -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-brain text-purple-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">A-Mem高级系统</h4>
                </div>
                <p class="text-slate-600 mb-4 text-sm">
                  灵感来源于Zettelkasten，实现动态自我组织的记忆网络。
                </p>
                <div class="space-y-2">
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">动态记忆连接</span>
                  </div>
                  <div class="flex items-center">
                    <i class="fas fa-check text-green-500 mr-2"></i>
                    <span class="text-sm text-slate-700">上下文长度缩减85%</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-slate-50 rounded-xl p-6">
              <h4 class="font-semibold text-slate-800 mb-4">A-Mem核心机制</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                  <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-pencil-alt text-blue-600 text-xl"></i>
                  </div>
                  <h5 class="font-medium text-slate-800">笔记构建</h5>
                  <p class="text-sm text-slate-600">将观察和思考转化为原子化笔记</p>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-link text-green-600 text-xl"></i>
                  </div>
                  <h5 class="font-medium text-slate-800">链接生成</h5>
                  <p class="text-sm text-slate-600">分析笔记间的潜在联系并生成链接</p>
                </div>
                <div class="text-center">
                  <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                    <i class="fas fa-sync-alt text-purple-600 text-xl"></i>
                  </div>
                  <h5 class="font-medium text-slate-800">记忆演化</h5>
                  <p class="text-sm text-slate-600">不断精炼和深化记忆网络</p>
                </div>
              </div>
            </div>
          </div>

          <!-- Prompt Engineering -->
          <div id="prompt-engineering" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-terminal text-blue-600 mr-3"></i>
              优化提示工程（Prompt Engineering）
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <p class="text-slate-700 mb-6">
                在有限的上下文窗口内，如何向LLM传递最有效、最相关的信息，是决定其决策质量的关键。优化提示工程正是实现这一目标的核心技术。
              </p>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">动态构建提示</h4>
                  <div class="space-y-3">
                    <div class="bg-blue-50 rounded-lg p-4">
                      <h5 class="font-medium text-blue-800 mb-2">当前步骤指令</h5>
                      <p class="text-sm text-blue-700">清晰、明确的参数和期望结果</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                      <h5 class="font-medium text-green-800 mb-2">历史记录摘要</h5>
                      <p class="text-sm text-green-700">高度压缩的关键信息，如&#34;已成功完成步骤1-5&#34;</p>
                    </div>
                    <div class="bg-purple-50 rounded-lg p-4">
                      <h5 class="font-medium text-purple-800 mb-2">高层目标</h5>
                      <p class="text-sm text-purple-700">反复强调最终测试目的</p>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">结构化信息展示</h4>
                  <div class="bg-slate-100 rounded-lg p-4">
                    <pre class="text-sm text-slate-700 overflow-x-auto"><code>| 参数名   | 值               | 来源   | 描述                  |
|----------|------------------|--------|-----------------------|
| `udid`   | `&#34;{device_udid}&#34;`| 动态   | 目标设备唯一标识符     |
| `x`      | `&#34;{element_x}&#34;`  | 动态   | 目标元素X坐标          |
| `y`      | `&#34;{element_y}&#34;`  | 动态   | 目标元素Y坐标          |
| `action` | `tap`            | 静态   | 执行的操作类型         |</code></pre>
                  </div>
                  <p class="text-sm text-slate-600 mt-3">
                    使用Markdown表格清晰展示参数与步骤，降低模型解析信息的认知负荷。
                  </p>
                </div>
              </div>

              <div class="mt-8 bg-gradient-to-r from-green-50 to-emerald-50 rounded-lg p-6">
                <h4 class="font-semibold text-green-800 mb-3 flex items-center">
                  <i class="fas fa-compress-alt mr-2"></i>
                  &#34;滚动摘要&#34;机制
                </h4>
                <p class="text-green-700">
                  定期调用LLM将最近的历史记录压缩成简短摘要，替代原始的多条记录，有效控制上下文长度，为模型提供更高层次的抽象视图。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Multi-Model Collaboration -->
      <section id="multi-model" class="py-16 bg-slate-50">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案二：采用多模型协作架构</h2>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center mb-8">
              当单一模型难以同时胜任高层规划、细节执行和记忆保持等多重任务时，采用多模型协作架构成为一种极具吸引力的解决方案。
              通过专业化分工，不仅可以克服单一模型的能力瓶颈，还能构建出更具鲁棒性和灵活性的系统。
            </p>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div class="text-center space-y-4">
                <div class="w-20 h-20 bg-blue-100 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-sitemap text-blue-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-800">规划专家</h3>
                <p class="text-sm text-slate-600">负责高层次的战略规划和决策制定</p>
              </div>
              <div class="text-center space-y-4">
                <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-tools text-green-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-800">执行专家</h3>
                <p class="text-sm text-slate-600">专注于具体工具调用和精确执行</p>
              </div>
              <div class="text-center space-y-4">
                <div class="w-20 h-20 bg-purple-100 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-eye text-purple-600 text-2xl"></i>
                </div>
                <h3 class="text-lg font-semibold text-slate-800">感知专家</h3>
                <p class="text-sm text-slate-600">提供强大的视觉理解和UI分析能力</p>
              </div>
            </div>
          </div>

          <!-- Factored Agent -->
          <div id="factored-agent" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-project-diagram text-blue-600 mr-3"></i>
              因子化代理（Factored Agent）模式
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <div class="mb-8">
                <blockquote class="border-l-4 border-blue-500 pl-6 italic text-lg text-slate-700">
                  &#34;因子化代理旨在解决LLM-Agent系统中规划与知识记忆之间的冲突。其核心思想源于一个观察：在单一模型中同时训练上下文学习和固定知识记忆会导致模型泛化能力的下降。&#34;
                  <a href="https://blog.csdn.net/qq_36671160/article/details/147669282" class="citation ml-2" target="_blank">[103]</a>
                </blockquote>
              </div>

              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <!-- Planner Agent -->
                <div class="bg-blue-50 rounded-xl p-6">
                  <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                      <i class="fas fa-brain text-blue-600 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">规划代理（大模型）</h4>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-medium text-blue-800 mb-2">核心职责</h5>
                      <p class="text-sm text-blue-700">负责高层计划与决策，理解用户请求和测试目标</p>
                    </div>
                    <div>
                      <h5 class="font-medium text-blue-800 mb-2">工作方式</h5>
                      <ul class="text-sm text-blue-700 space-y-1">
                        <li>• 根据实时反馈动态决定下一步行动</li>
                        <li>• 生成高层次的描述性&#34;意图&#34;</li>
                        <li>• 不直接处理具体API调用格式</li>
                      </ul>
                    </div>
                    <div class="bg-blue-100 rounded-lg p-3">
                      <p class="text-xs text-blue-800">
                        <strong>示例输出：</strong>&#34;点击屏幕左上角显示当前地址的文本&#34;
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Tool Agent -->
                <div class="bg-green-50 rounded-xl p-6">
                  <div class="flex items-center mb-4">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                      <i class="fas fa-wrench text-green-600 text-xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">工具代理（小模型）</h4>
                  </div>
                  <div class="space-y-4">
                    <div>
                      <h5 class="font-medium text-green-800 mb-2">核心职责</h5>
                      <p class="text-sm text-green-700">负责具体工具调用与执行，作为专门的&#34;记忆库&#34;和&#34;执行器&#34;</p>
                    </div>
                    <div>
                      <h5 class="font-medium text-green-800 mb-2">工作方式</h5>
                      <ul class="text-sm text-green-700 space-y-1">
                        <li>• 接收高层次&#34;意图&#34;</li>
                        <li>• 转换为严格的工具调用格式</li>
                        <li>• 确保参数精确性和完整性</li>
                      </ul>
                    </div>
                    <div class="bg-green-100 rounded-lg p-3">
                      <p class="text-xs text-green-800">
                        <strong>示例输出：</strong>JSON格式的find_element和tap工具参数
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- AutoGen Framework -->
          <div id="autogen-framework" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-users text-blue-600 mr-3"></i>
              多智能体协作框架（如AutoGen）
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <p class="text-slate-700 mb-6">
                当任务复杂度进一步提升时，引入功能更全面的多智能体协作框架，如微软开发的AutoGen，成为理想的选择。
                <a href="https://zhuanlan.zhihu.com/p/705099162" class="citation" target="_blank">[334]</a>
                <a href="https://zhuanlan.zhihu.com/p/705583812" class="citation" target="_blank">[350]</a>
              </p>

              <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <!-- Coordinator -->
                <div class="bg-gradient-to-br from-blue-50 to-indigo-50 rounded-xl p-6">
                  <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i class="fas fa-chess-king text-blue-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">&#34;大脑&#34;协调者</h4>
                    <p class="text-sm text-slate-600">使用云端大模型（如GPT-4）</p>
                  </div>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-blue-800 text-sm mb-1">核心功能</h5>
                      <ul class="text-xs text-blue-700 space-y-1">
                        <li>• 接收和理解测试计划</li>
                        <li>• 动态分配子任务</li>
                        <li>• 管理对话记忆</li>
                        <li>• 协调代理间协作</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Executor -->
                <div class="bg-gradient-to-br from-green-50 to-emerald-50 rounded-xl p-6">
                  <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i class="fas fa-robot text-green-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">本地&#34;执行者&#34;</h4>
                    <p class="text-sm text-slate-600">30B模型专注具体操作</p>
                  </div>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-green-800 text-sm mb-1">专业化分工</h5>
                      <ul class="text-xs text-green-700 space-y-1">
                        <li>• TapExecutor代理</li>
                        <li>• TextValidator代理</li>
                        <li>• 职责单一，提示简洁</li>
                        <li>• 最小化上下文占用</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <!-- Visual Model -->
                <div class="bg-gradient-to-br from-purple-50 to-violet-50 rounded-xl p-6">
                  <div class="text-center mb-4">
                    <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                      <i class="fas fa-eye text-purple-600 text-2xl"></i>
                    </div>
                    <h4 class="text-lg font-semibold text-slate-800">视觉模型&#34;眼睛&#34;</h4>
                    <p class="text-sm text-slate-600">GPT-4V识别UI元素</p>
                  </div>
                  <div class="space-y-3">
                    <div>
                      <h5 class="font-medium text-purple-800 text-sm mb-1">视觉理解</h5>
                      <ul class="text-xs text-purple-700 space-y-1">
                        <li>• 分析屏幕截图</li>
                        <li>• 返回元素精确坐标</li>
                        <li>• 理解布局和状态</li>
                        <li>• 端到端视觉理解</li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-8 bg-slate-50 rounded-lg p-6">
                <h4 class="font-semibold text-slate-800 mb-4">实际应用案例</h4>
                <blockquote class="border-l-4 border-slate-400 pl-4 italic text-slate-700">
                  &#34;京东云技术团队探索使用GPT-4V作为&#39;眼睛&#39;，Playwright作为&#39;手&#39;，通过AutoGen和GPT-4进行协同调度，完成自动化UI测试任务。&#34;
                  <a href="https://juejin.cn/post/7316592794109198387" class="citation ml-2" target="_blank">[346]</a>
                </blockquote>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- State Machine Solution -->
      <section id="state-machine" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">解决方案三：引入状态机（State Machine）进行流程控制</h2>

          <div class="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-2xl p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              引入状态机是一种更为结构化和工程化的解决方案。状态机的核心思想是将复杂的测试计划分解为一系列离散的、定义明确的状态，
              并通过预定义的规则来管理这些状态之间的流转。这种方法将宏观的流程控制逻辑从模型的即时决策中分离出来，
              由状态机引擎负责确保测试严格按照预定路径执行。
            </p>
          </div>

          <!-- State Machine Core -->
          <div id="state-core" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-flow-chart text-blue-600 mr-3"></i>
              状态机模式的核心思想
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <img src="https://kimi-web-img.moonshot.cn/img/tva1.sinaimg.cn/7e43885360a487841bc856adf659e73ed3a29f45.jpg" alt="抽象状态机流程图" class="w-full rounded-lg mb-4" size="medium" aspect="wide" query="状态机流程图 抽象" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>
                <h4 class="text-lg font-semibold text-slate-800 mb-3">状态分解优势</h4>
                <p class="text-slate-700 text-sm">
                  将复杂的、长序列的测试任务分解为一系列离散的、可管理的&#34;状态&#34;，
                  每个状态代表测试流程中的一个特定阶段或一组相关操作。
                </p>
              </div>

              <div class="space-y-6">
                <div class="bg-blue-50 rounded-xl p-6">
                  <h4 class="font-semibold text-blue-800 mb-3 flex items-center">
                    <i class="fas fa-cog mr-2"></i>
                    减轻认知负担
                  </h4>
                  <p class="text-blue-700 text-sm">
                    LLM不再需要记住整个20步测试计划，只需专注于当前状态的局部任务，
                    极大降低因上下文窗口限制导致的&#34;遗忘&#34;问题。
                  </p>
                </div>

                <div class="bg-green-50 rounded-xl p-6">
                  <h4 class="font-semibold text-green-800 mb-3 flex items-center">
                    <i class="fas fa-shield-alt mr-2"></i>
                    提高可控性
                  </h4>
                  <p class="text-green-700 text-sm">
                    通过预定义状态转换规则，确保测试按既定顺序执行，
                    避免模型因自主决策而偏离主路径的风险。
                  </p>
                </div>

                <div class="bg-purple-50 rounded-xl p-6">
                  <h4 class="font-semibent text-purple-800 mb-3 flex items-center">
                    <i class="fas fa-puzzle-piece mr-2"></i>
                    增强模块化
                  </h4>
                  <p class="text-purple-700 text-sm">
                    每个状态可独立设计、测试和优化，不影响系统其他部分，
                    使代码更易于扩展和调试。
                  </p>
                </div>
              </div>
            </div>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <h4 class="text-lg font-semibold text-slate-800 mb-6">测试计划状态分解示例</h4>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="bg-blue-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-play text-blue-600"></i>
                  </div>
                  <h5 class="font-medium text-blue-800 text-sm">INITIALIZE</h5>
                  <p class="text-xs text-blue-600">初始化测试环境</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-search text-green-600"></i>
                  </div>
                  <h5 class="font-medium text-green-800 text-sm">NAVIGATE</h5>
                  <p class="text-xs text-green-600">导航到目标页面</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-check-circle text-yellow-600"></i>
                  </div>
                  <h5 class="font-medium text-yellow-800 text-sm">VALIDATE</h5>
                  <p class="text-xs text-yellow-600">验证页面元素</p>
                </div>
                <div class="bg-purple-50 rounded-lg p-4 text-center">
                  <div class="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-mouse-pointer text-purple-600"></i>
                  </div>
                  <h5 class="font-medium text-purple-800 text-sm">PERFORM</h5>
                  <p class="text-xs text-purple-600">执行操作</p>
                </div>
              </div>

              <div class="bg-slate-50 rounded-lg p-4">
                <h5 class="font-medium text-slate-800 mb-2">状态转移控制</h5>
                <p class="text-sm text-slate-700">
                  状态机通过定义明确的状态转移来确保整体流程的正确性。
                  转移条件基于前一个状态的执行结果，如成功、失败或需要重试。
                </p>
              </div>
            </div>
          </div>

          <!-- Implementation -->
          <div id="state-implementation" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-code text-blue-600 mr-3"></i>
              实现方案
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Custom Implementation -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                  <i class="fas fa-tools text-blue-600 mr-2"></i>
                  使用Python自行实现
                </h4>
                <p class="text-slate-700 mb-4 text-sm">
                  围绕核心循环和状态字典构建，提供最大灵活性和定制能力。
                </p>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="fas fa-check text-green-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-green-800">优点：</strong>
                      <span class="text-green-700 text-sm">灵活轻量，不依赖外部库</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-times text-red-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-red-800">缺点：</strong>
                      <span class="text-red-700 text-sm">需自行处理并发、持久化等复杂问题</span>
                    </div>
                  </div>
                </div>
                <div class="bg-slate-100 rounded-lg p-3 mt-4">
                  <p class="text-xs text-slate-700">
                    <strong>核心组件：</strong>State基类、execute方法、状态字典、主控制器循环
                  </p>
                </div>
              </div>

              <!-- LangGraph Framework -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4 flex items-center">
                  <i class="fas fa-sitemap text-purple-600 mr-2"></i>
                  采用LangGraph框架
                </h4>
                <p class="text-slate-700 mb-4 text-sm">
                  专门为构建LLM驱动的、有状态的、多Agent应用而设计的强大框架。
                  <a href="https://gacfox.com/notes/%E8%BD%AF%E4%BB%B6%E5%B7%A5%E7%A8%8B/Python/LangChain/07-LangGraph%E7%8A%B6%E6%80%81%E6%9C%BA%E6%A1%86%E6%9E%B6/07-LangGraph%E7%8A%B6%E6%80%81%E6%9C%BA%E6%A1%86%E6%9E%B6.md" class="citation text-xs" target="_blank">[419]</a>
                </p>
                <div class="space-y-3">
                  <div class="flex items-start">
                    <i class="fas fa-star text-yellow-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-yellow-800">特点：</strong>
                      <span class="text-yellow-700 text-sm">图结构，条件边，内置记忆管理</span>
                    </div>
                  </div>
                  <div class="flex items-start">
                    <i class="fas fa-rocket text-blue-500 mr-2 mt-1"></i>
                    <div>
                      <strong class="text-blue-800">优势：</strong>
                      <span class="text-blue-700 text-sm">快速开发，减少底层复杂性</span>
                    </div>
                  </div>
                </div>
                <div class="bg-purple-50 rounded-lg p-3 mt-4">
                  <p class="text-xs text-purple-700">
                    <strong>核心概念：</strong>State对象、Node节点、Edge边、条件转换
                  </p>
                </div>
              </div>
            </div>

            <!-- LangGraph Implementation Example -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8 mt-8">
              <h4 class="text-lg font-semibold text-slate-800 mb-6">LangGraph实现示例</h4>

              <div class="bg-slate-900 rounded-lg p-6 mb-6 overflow-x-auto">
                <pre class="text-green-400 text-sm"><code>from langgraph.graph import StateGraph, START, END
import your_llm_wrapper as llm
import your_tools as tools

# 定义状态结构
class TestState(TypedDict):
    messages: List[HumanMessage | AIMessage]
    current_step: int
    test_plan: dict
    device_udid: str
    last_result: str

# 构建状态图
graph_builder = StateGraph(TestState)

# 添加节点
graph_builder.add_node(&#34;plan&#34;, plan_step)
graph_builder.add_node(&#34;execute&#34;, execute_step)
graph_builder.add_node(&#34;validate&#34;, validate_step)

# 添加边和条件逻辑
graph_builder.add_edge(START, &#34;plan&#34;)
graph_builder.add_edge(&#34;plan&#34;, &#34;execute&#34;)
graph_builder.add_edge(&#34;execute&#34;, &#34;validate&#34;)

# 定义条件边：根据验证结果决定下一步
def decide_next_step(state: TestState):
    if state[&#34;last_result&#34;] == &#34;validation_passed&#34;:
        if state[&#34;current_step&#34;] &lt; state[&#34;test_plan&#34;][&#34;total_steps&#34;]:
            return &#34;plan&#34; # 继续下一步
        else:
            return END # 测试完成
    else:
        return &#34;plan&#34; # 重试当前步骤或处理错误

graph_builder.add_conditional_edges(&#34;validate&#34;, decide_next_step)

# 编译并运行图
graph = graph_builder.compile()</code></pre>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="bg-blue-50 rounded-lg p-4">
                  <h5 class="font-medium text-blue-800 text-sm mb-2">plan_step</h5>
                  <p class="text-xs text-blue-700">规划下一步操作，调用LLM进行决策</p>
                </div>
                <div class="bg-green-50 rounded-lg p-4">
                  <h5 class="font-medium text-green-800 text-sm mb-2">execute_step</h5>
                  <p class="text-xs text-green-700">执行具体操作，调用相应工具</p>
                </div>
                <div class="bg-yellow-50 rounded-lg p-4">
                  <h5 class="font-medium text-yellow-800 text-sm mb-2">validate_step</h5>
                  <p class="text-xs text-yellow-700">验证操作结果，决定后续流程</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- Autonomy Mechanisms -->
      <section id="autonomy" class="py-16 bg-slate-50">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">实现Agent自主性的具体机制</h2>

          <div class="bg-white rounded-2xl shadow-lg border border-slate-200 p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              在通过状态机模式确保了测试流程的宏观可控性之后，下一步的关键是实现Agent在微观执行层面的自主性。
              这种自主性主要体现在对意外情况的处理能力上，通过设计具体的机制，可以有效地赋予Agent错误处理、重试和决策能力。
            </p>
          </div>

          <!-- Error Handling -->
          <div id="error-handling" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-exclamation-triangle text-orange-600 mr-3"></i>
              错误处理与重试机制
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">元素查找失败处理</h4>
                <img src="https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175491623825184ad5aad61b27dbb859831171057baeaa8042a.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&amp;X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250811%2Fcn-beijing%2Ftos%2Frequest&amp;X-Tos-Date=20250811T124401Z&amp;X-Tos-Expires=86400&amp;X-Tos-Signature=7ceedb9662ff30a6f7ea7606a95a6cc98a103f49743cbdf58d672cff8e040a71&amp;X-Tos-SignedHeaders=host&amp;x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF85" alt="元素查找失败重试机制流程图" class="w-full rounded-lg mb-4" size="medium" aspect="wide" color="blue" style="linedrawing" query="元素查找失败处理流程图" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>

                <div class="space-y-4">
                  <div class="bg-orange-50 rounded-lg p-4">
                    <h5 class="font-medium text-orange-800 text-sm mb-2">自主等待策略</h5>
                    <p class="text-xs text-orange-700">模拟人类测试工程师行为，在元素查找失败时进行等待和重试</p>
                  </div>
                  <div class="bg-blue-50 rounded-lg p-4">
                    <h5 class="font-medium text-blue-800 text-sm mb-2">重试循环设计</h5>
                    <ul class="text-xs text-blue-700 space-y-1">
                      <li>• 最大重试次数：3次</li>
                      <li>• 每次等待时间：2秒</li>
                      <li>• 内部循环，对上层透明</li>
                    </ul>
                  </div>
                </div>
              </div>

              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">错误日志记录</h4>

                <div class="space-y-4">
                  <div class="bg-red-50 rounded-lg p-4">
                    <h5 class="font-medium text-red-800 text-sm mb-2">记录内容</h5>
                    <ul class="text-xs text-red-700 space-y-1">
                      <li>• 失败步骤ID和执行操作</li>
                      <li>• 使用的参数和当前截图</li>
                      <li>• 页面UI结构和LLM推理过程</li>
                      <li>• 最终错误信息</li>
                    </ul>
                  </div>
                  <div class="bg-green-50 rounded-lg p-4">
                    <h5 class="font-medium text-green-800 text-sm mb-2">存储格式</h5>
                    <p class="text-xs text-green-700">结构化JSON格式，便于后续问题排查和调试</p>
                  </div>
                  <div class="bg-purple-50 rounded-lg p-4">
                    <h5 class="font-medium text-purple-800 text-sm mb-2">实现位置</h5>
                    <p class="text-xs text-purple-700">在错误处理节点中集成日志记录逻辑</p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Early Termination -->
          <div id="early-termination" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-stop-circle text-red-600 mr-3"></i>
              提前终止测试机制
            </h3>

            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">连续失败决策逻辑</h4>
                  <div class="bg-slate-100 rounded-lg p-6 mb-4">
                    <div class="mermaid-container">
                      <div class="mermaid-controls">
                        <button class="mermaid-control-btn zoom-in" title="放大">
                          <i class="fas fa-search-plus"></i>
                        </button>
                        <button class="mermaid-control-btn zoom-out" title="缩小">
                          <i class="fas fa-search-minus"></i>
                        </button>
                        <button class="mermaid-control-btn reset-zoom" title="重置">
                          <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button class="mermaid-control-btn fullscreen" title="全屏查看">
                          <i class="fas fa-expand"></i>
                        </button>
                      </div>
                      <div class="mermaid">
                        graph TD
                        A[&#34;开始测试&#34;] --&gt; B[&#34;执行步骤&#34;]
                        B --&gt; C{&#34;检查结果&#34;}
                        C --&gt;|&#34;成功&#34;| D[&#34;继续下一步&#34;]
                        C --&gt;|&#34;失败&#34;| E[&#34;增加失败计数&#34;]
                        E --&gt; F{&#34;计数 &gt;= 3?&#34;}
                        F --&gt;|&#34;否&#34;| B
                        F --&gt;|&#34;是&#34;| G[&#34;触发终止流程&#34;]
                        G --&gt; H[&#34;执行清理操作&#34;]
                        H --&gt; I[&#34;结束测试&#34;]

                        D --&gt; J{&#34;是否完成?&#34;}
                        J --&gt;|&#34;否&#34;| B
                        J --&gt;|&#34;是&#34;| H
                      </div>
                    </div>
                  </div>
                  <div class="space-y-3">
                    <div class="flex items-start">
                      <i class="fas fa-calculator text-blue-500 mr-2 mt-1"></i>
                      <div>
                        <strong class="text-blue-800 text-sm">失败计数器：</strong>
                        <span class="text-blue-700 text-sm">跟踪连续失败次数</span>
                      </div>
                    </div>
                    <div class="flex items-start">
                      <i class="fas fa-threshold text-orange-500 mr-2 mt-1"></i>
                      <div>
                        <strong class="text-orange-800 text-sm">阈值设置：</strong>
                        <span class="text-orange-700 text-sm">连续3次失败触发终止</span>
                      </div>
                    </div>
                  </div>
                </div>

                <div>
                  <h4 class="text-lg font-semibold text-slate-800 mb-4">收尾操作保障</h4>
                  <div class="space-y-4">
                    <div class="bg-red-50 rounded-lg p-4">
                      <h5 class="font-medium text-red-800 text-sm mb-2">CLEANUP_AND_EXIT状态</h5>
                      <p class="text-xs text-red-700">确保所有必要的收尾操作都能被执行</p>
                    </div>
                    <div class="bg-green-50 rounded-lg p-4">
                      <h5 class="font-medium text-green-800 text-sm mb-2">资源释放</h5>
                      <ul class="text-xs text-green-700 space-y-1">
                        <li>• 调用end_test工具</li>
                        <li>• 关闭测试会话</li>
                        <li>• 释放设备资源</li>
                      </ul>
                    </div>
                    <div class="bg-blue-50 rounded-lg p-4">
                      <h5 class="font-medium text-blue-800 text-sm mb-2">设计原则</h5>
                      <p class="text-xs text-blue-700">所有导致测试结束的路径最终都指向清理状态</p>
                    </div>
                  </div>
                </div>
              </div>

              <div class="bg-gradient-to-r from-yellow-50 to-orange-50 rounded-lg p-6">
                <h4 class="font-semibold text-yellow-800 mb-3 flex items-center">
                  <i class="fas fa-lightbulb mr-2"></i>
                  关键设计理念
                </h4>
                <p class="text-yellow-700 text-sm">
                  这种&#34;外紧内松&#34;的架构设计，既能保证测试流程不偏离轨道，又能充分发挥模型的自主性。
                  状态机负责宏观流程控制，LLM在单个状态内进行微观决策，实现计划性与自主性的完美平衡。
                </p>
              </div>
            </div>
          </div>
        </div>
      </section>

      <div class="section-divider"></div>

      <!-- UI Perception -->
      <section id="ui-perception" class="py-16 bg-white">
        <div class="max-w-6xl mx-auto px-6">
          <h2 class="text-4xl font-bold text-slate-800 mb-12 text-center">增强Agent的UI感知能力</h2>

          <div class="bg-gradient-to-r from-purple-50 to-violet-50 rounded-2xl p-8 mb-12">
            <p class="text-lg text-slate-700 leading-relaxed text-center">
              为了提升本地Agent在移动端UI自动化测试中的自主性和鲁棒性，核心在于增强其对当前界面状态的感知能力。
              当Agent能够准确&#34;看懂&#34;屏幕上的内容时，它便能更好地理解测试进度、验证预期结果，并在遇到异常时做出更智能的决策。
            </p>
          </div>

          <!-- OCR Integration -->
          <div id="ocr-integration" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-font text-blue-600 mr-3"></i>
              集成OCR工具
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <!-- Tesseract -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-file-alt text-blue-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">Tesseract OCR</h4>
                </div>
                <img src="https://kimi-web-img.moonshot.cn/img/static-blog.onlyoffice.com/464b9b1c84d2f8920cad325c8945df6f34cbc27e.png" alt="Tesseract OCR处理移动应用界面的效果" class="w-full rounded-lg mb-4" size="medium" aspect="wide" style="photo" query="Tesseract OCR 移动应用界面" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>

                <div class="space-y-4">
                  <div class="bg-blue-50 rounded-lg p-4">
                    <h5 class="font-medium text-blue-800 text-sm mb-2">特点</h5>
                    <ul class="text-xs text-blue-700 space-y-1">
                      <li>• 开源OCR引擎，Google维护</li>
                      <li>• 支持多种语言（包括中文）</li>
                      <li>• 历史悠久，社区支持广泛</li>
                      <li>• 通过pytesseract库集成</li>
                    </ul>
                  </div>
                  <div class="bg-orange-50 rounded-lg p-4">
                    <h5 class="font-medium text-orange-800 text-sm mb-2">预处理技术</h5>
                    <ul class="text-xs text-orange-700 space-y-1">
                      <li>• 灰度化：去除颜色干扰</li>
                      <li>• 二值化：增强对比度</li>
                      <li>• 去噪：平滑文字边缘</li>
                      <li>• 图像缩放：提高精度</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- PaddleOCR -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <div class="flex items-center mb-4">
                  <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center mr-4">
                    <i class="fas fa-robot text-green-600 text-xl"></i>
                  </div>
                  <h4 class="text-lg font-semibold text-slate-800">PaddleOCR</h4>
                </div>

                <div class="space-y-4">
                  <div class="bg-green-50 rounded-lg p-4">
                    <h5 class="font-medium text-green-800 text-sm mb-2">核心优势</h5>
                    <ul class="text-xs text-green-700 space-y-1">
                      <li>• 基于深度学习，端到端模型</li>
                      <li>• 集成了文本检测和识别</li>
                      <li>• 处理复杂场景能力更强</li>
                      <li>• 提供多种预训练模型</li>
                    </ul>
                  </div>
                  <div class="bg-purple-50 rounded-lg p-4">
                    <h5 class="font-medium text-purple-800 text-sm mb-2">API示例</h5>
                    <div class="bg-slate-100 rounded p-2">
                      <code class="text-xs text-slate-700">
                                        ocr = PaddleOCR(lang=&#34;ch&#34;)<br/>
                                        result = ocr.ocr(img_path)
                                    </code>
                    </div>
                  </div>
                  <div class="bg-yellow-50 rounded-lg p-4">
                    <h5 class="font-medium text-yellow-800 text-sm mb-2">输出格式</h5>
                    <p class="text-xs text-yellow-700">
                      返回文本框坐标、识别文本和置信度分数，结构化为列表形式
                      <a href="https://juejin.cn/post/7387690498004402226" class="citation text-xs" target="_blank">[311]</a>
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Multimodal Models -->
          <div id="multimodal-models" class="mb-16">
            <h3 class="text-2xl font-bold text-slate-800 mb-8 flex items-center">
              <i class="fas fa-eye text-purple-600 mr-3"></i>
              结合多模态模型
            </h3>

            <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
              <!-- Document Understanding -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">文档理解模型</h4>
                <div class="space-y-4">
                  <div class="bg-blue-50 rounded-lg p-4">
                    <h5 class="font-medium text-blue-800 text-sm mb-2">Donut &amp; Nougat</h5>
                    <p class="text-xs text-blue-700 mb-2">
                      OCR-free文档理解模型，直接将文档图像转换为结构化文本
                      <a href="https://blog.csdn.net/shiwanghualuo/article/details/136596013" class="citation text-xs" target="_blank">[316]</a>
                    </p>
                    <ul class="text-xs text-blue-700 space-y-1">
                      <li>• 端到端Transformer架构</li>
                      <li>• 支持文档分类和信息提取</li>
                      <li>• Nougat专门处理学术文档</li>
                    </ul>
                  </div>
                  <div class="bg-green-50 rounded-lg p-4">
                    <h5 class="font-medium text-green-800 text-sm mb-2">应用场景</h5>
                    <ul class="text-xs text-green-700 space-y-1">
                      <li>• 用户协议与隐私政策</li>
                      <li>• 帮助中心与FAQ文档</li>
                      <li>• 报表与数据展示解析</li>
                    </ul>
                  </div>
                </div>
              </div>

              <!-- Vision Language Models -->
              <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-6">
                <h4 class="text-lg font-semibold text-slate-800 mb-4">视觉语言模型</h4>
                <img src="https://kimi-web-img.moonshot.cn/img/aisholar.s3.ap-northeast-1.amazonaws.com/2a7539032ba295381070c917614bd2e27d79bd49.png" alt="视觉语言模型分析UI界面的示意图" class="w-full rounded-lg mb-4" size="medium" aspect="wide" query="视觉语言模型 UI界面分析" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>

                <div class="space-y-4">
                  <div class="bg-purple-50 rounded-lg p-4">
                    <h5 class="font-medium text-purple-800 text-sm mb-2">VLM能力</h5>
                    <ul class="text-xs text-purple-700 space-y-1">
                      <li>• 同时理解图像和文本</li>
                      <li>• 回答关于UI布局的复杂问题</li>
                      <li>• 定位元素并返回精确坐标</li>
                      <li>• 理解元素功能和交互流程</li>
                    </ul>
                  </div>
                  <div class="bg-yellow-50 rounded-lg p-4">
                    <h5 class="font-medium text-yellow-800 text-sm mb-2">典型问题</h5>
                    <ul class="text-xs text-yellow-700 space-y-1">
                      <li>&#34;外卖按钮的坐标是什么？&#34;</li>
                      <li>&#34;当前页面是否成功跳转？&#34;</li>
                      <li>&#34;购物车图标显示是否正确？&#34;</li>
                    </ul>
                  </div>
                </div>
              </div>
            </div>

            <!-- Multi-agent Architecture -->
            <div class="bg-white rounded-xl shadow-lg border border-slate-200 p-8">
              <h4 class="text-lg font-semibold text-slate-800 mb-6">多模态测试智能体架构</h4>
              <p class="text-slate-700 mb-6">
                一个典型的多模态测试智能体架构（如AUITestAgent）充分利用了VLM的能力，通过多个智能体的协作实现端到端的UI测试自动化。
                <a href="https://blog.csdn.net/Testerhomee/article/details/140608798" class="citation" target="_blank">[312]</a>
              </p>

              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-blue-50 rounded-xl p-6 text-center">
                  <div class="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-eye text-blue-600 text-2xl"></i>
                  </div>
                  <h5 class="font-semibold text-blue-800 mb-3">观察者</h5>
                  <p class="text-xs text-blue-700">
                    接收UI截图和层级结构，利用VLM和OCR全面识别页面上的所有可交互元素，并推断其功能
                  </p>
                </div>
                <div class="bg-green-50 rounded-xl p-6 text-center">
                  <div class="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-mouse-pointer text-green-600 text-2xl"></i>
                  </div>
                  <h5 class="font-semibold text-green-800 mb-3">选择者</h5>
                  <p class="text-xs text-green-700">
                    根据自然语言描述的测试步骤，从元素列表中选择最合适的目标，并生成具体操作指令
                  </p>
                </div>
                <div class="bg-purple-50 rounded-xl p-6 text-center">
                  <div class="w-16 h-16 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <i class="fas fa-play text-purple-600 text-2xl"></i>
                  </div>
                  <h5 class="font-semibold text-purple-800 mb-3">执行者</h5>
                  <p class="text-xs text-purple-700">
                    在真实设备上执行由选择者生成的操作指令，完成自动化测试流程
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Conclusion -->
      <section class="py-16 bg-gradient-to-r from-blue-600 to-indigo-600 text-white">
        <div class="max-w-4xl mx-auto px-6 text-center">
          <h2 class="text-3xl font-bold mb-6">核心解决方案总结</h2>
          <p class="text-xl leading-relaxed mb-8">
            通过将宏观的流程控制与微观的模型决策进行解耦，引入外部的、非LLM的控制器（如状态机）来严格管理测试步骤的顺序和状态转换，
            同时在每个步骤内部通过优化提示工程和长期记忆系统，为模型提供精准的上下文，实现严格遵循测试计划与保持自主决策能力的完美平衡。
          </p>
          <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div class="text-center">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-sitemap text-2xl"></i>
              </div>
              <h3 class="font-semibold mb-2">状态机控制</h3>
              <p class="text-sm opacity-90">确保流程严格按照预定路径执行</p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-brain text-2xl"></i>
              </div>
              <h3 class="font-semibold mb-2">智能决策</h3>
              <p class="text-sm opacity-90">在单个步骤内进行自主错误处理和重试</p>
            </div>
            <div class="text-center">
              <div class="w-16 h-16 bg-white bg-opacity-20 rounded-full flex items-center justify-center mx-auto mb-3">
                <i class="fas fa-eye text-2xl"></i>
              </div>
              <h3 class="font-semibold mb-2">视觉感知</h3>
              <p class="text-sm opacity-90">通过OCR和多模态模型增强UI理解</p>
            </div>
          </div>
        </div>
      </section>
    </main>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', function (e) {
                e.preventDefault();
                const target = document.querySelector(this.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Highlight active section in TOC
        const sections = document.querySelectorAll('section[id]');
        const navLinks = document.querySelectorAll('.toc-fixed a[href^="#"]');

        function highlightActiveSection() {
            let current = '';
            sections.forEach(section => {
                const sectionTop = section.offsetTop;
                if (pageYOffset >= sectionTop - 200) {
                    current = section.getAttribute('id');
                }
            });

            navLinks.forEach(link => {
                link.classList.remove('text-blue-600', 'font-semibold');
                link.classList.add('text-slate-600');
                if (link.getAttribute('href') === '#' + current) {
                    link.classList.remove('text-slate-600');
                    link.classList.add('text-blue-600', 'font-semibold');
                }
            });
        }

        window.addEventListener('scroll', highlightActiveSection);
        highlightActiveSection(); // Initial call

        // Initialize Mermaid
        mermaid.initialize({ 
            startOnLoad: true,
            theme: 'default',
            themeVariables: {
                primaryColor: '#3b82f6',
                primaryTextColor: '#1e40af',
                primaryBorderColor: '#2563eb',
                lineColor: '#64748b',
                sectionBkgColor: '#dbeafe',
                altSectionBkgColor: '#dcfce7',
                gridColor: '#e2e8f0',
                secondaryColor: '#10b981',
                tertiaryColor: '#f59e0b',
                background: '#ffffff',
                mainBkg: '#dbeafe',
                secondBkg: '#dcfce7',
                tertiaryBkg: '#fef3c7',
                nodeBkg: '#dbeafe',
                nodeBorder: '#2563eb',
                clusterBkg: '#f8fafc',
                clusterBorder: '#cbd5e1',
                defaultLinkColor: '#64748b',
                titleColor: '#1e40af',
                edgeLabelBackground: '#ffffff',
                nodeTextColor: '#1e40af'
            },
            flowchart: {
                useMaxWidth: false,
                htmlLabels: true,
                curve: 'basis'
            },
            sequence: {
                useMaxWidth: false,
                wrap: true
            },
            gantt: {
                useMaxWidth: false
            }
        });

        // Initialize Mermaid Controls for zoom and pan
        function initializeMermaidControls() {
            const containers = document.querySelectorAll('.mermaid-container');

            containers.forEach(container => {
            const mermaidElement = container.querySelector('.mermaid');
            let scale = 1;
            let isDragging = false;
            let startX, startY, translateX = 0, translateY = 0;

            // 触摸相关状态
            let isTouch = false;
            let touchStartTime = 0;
            let initialDistance = 0;
            let initialScale = 1;
            let isPinching = false;

            // Zoom controls
            const zoomInBtn = container.querySelector('.zoom-in');
            const zoomOutBtn = container.querySelector('.zoom-out');
            const resetBtn = container.querySelector('.reset-zoom');
            const fullscreenBtn = container.querySelector('.fullscreen');

            function updateTransform() {
                mermaidElement.style.transform = `translate(${translateX}px, ${translateY}px) scale(${scale})`;

                if (scale > 1) {
                container.classList.add('zoomed');
                } else {
                container.classList.remove('zoomed');
                }

                mermaidElement.style.cursor = isDragging ? 'grabbing' : 'grab';
            }

            if (zoomInBtn) {
                zoomInBtn.addEventListener('click', () => {
                scale = Math.min(scale * 1.25, 4);
                updateTransform();
                });
            }

            if (zoomOutBtn) {
                zoomOutBtn.addEventListener('click', () => {
                scale = Math.max(scale / 1.25, 0.3);
                if (scale <= 1) {
                    translateX = 0;
                    translateY = 0;
                }
                updateTransform();
                });
            }

            if (resetBtn) {
                resetBtn.addEventListener('click', () => {
                scale = 1;
                translateX = 0;
                translateY = 0;
                updateTransform();
                });
            }

            if (fullscreenBtn) {
                fullscreenBtn.addEventListener('click', () => {
                if (container.requestFullscreen) {
                    container.requestFullscreen();
                } else if (container.webkitRequestFullscreen) {
                    container.webkitRequestFullscreen();
                } else if (container.msRequestFullscreen) {
                    container.msRequestFullscreen();
                }
                });
            }

            // Mouse Events
            mermaidElement.addEventListener('mousedown', (e) => {
                if (isTouch) return; // 如果是触摸设备，忽略鼠标事件

                isDragging = true;
                startX = e.clientX - translateX;
                startY = e.clientY - translateY;
                mermaidElement.style.cursor = 'grabbing';
                updateTransform();
                e.preventDefault();
            });

            document.addEventListener('mousemove', (e) => {
                if (isDragging && !isTouch) {
                translateX = e.clientX - startX;
                translateY = e.clientY - startY;
                updateTransform();
                }
            });

            document.addEventListener('mouseup', () => {
                if (isDragging && !isTouch) {
                isDragging = false;
                mermaidElement.style.cursor = 'grab';
                updateTransform();
                }
            });

            document.addEventListener('mouseleave', () => {
                if (isDragging && !isTouch) {
                isDragging = false;
                mermaidElement.style.cursor = 'grab';
                updateTransform();
                }
            });

            // 获取两点之间的距离
            function getTouchDistance(touch1, touch2) {
                return Math.hypot(
                touch2.clientX - touch1.clientX,
                touch2.clientY - touch1.clientY
                );
            }

            // Touch Events - 触摸事件处理
            mermaidElement.addEventListener('touchstart', (e) => {
                isTouch = true;
                touchStartTime = Date.now();

                if (e.touches.length === 1) {
                // 单指拖动
                isPinching = false;
                isDragging = true;

                const touch = e.touches[0];
                startX = touch.clientX - translateX;
                startY = touch.clientY - translateY;

                } else if (e.touches.length === 2) {
                // 双指缩放
                isPinching = true;
                isDragging = false;

                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                initialDistance = getTouchDistance(touch1, touch2);
                initialScale = scale;
                }

                e.preventDefault();
            }, { passive: false });

            mermaidElement.addEventListener('touchmove', (e) => {
                if (e.touches.length === 1 && isDragging && !isPinching) {
                // 单指拖动
                const touch = e.touches[0];
                translateX = touch.clientX - startX;
                translateY = touch.clientY - startY;
                updateTransform();

                } else if (e.touches.length === 2 && isPinching) {
                // 双指缩放
                const touch1 = e.touches[0];
                const touch2 = e.touches[1];
                const currentDistance = getTouchDistance(touch1, touch2);

                if (initialDistance > 0) {
                    const newScale = Math.min(Math.max(
                    initialScale * (currentDistance / initialDistance),
                    0.3
                    ), 4);
                    scale = newScale;
                    updateTransform();
                }
                }

                e.preventDefault();
            }, { passive: false });

            mermaidElement.addEventListener('touchend', (e) => {
                // 重置状态
                if (e.touches.length === 0) {
                isDragging = false;
                isPinching = false;
                initialDistance = 0;

                // 延迟重置isTouch，避免鼠标事件立即触发
                setTimeout(() => {
                    isTouch = false;
                }, 100);
                } else if (e.touches.length === 1 && isPinching) {
                // 从双指变为单指，切换为拖动模式
                isPinching = false;
                isDragging = true;

                const touch = e.touches[0];
                startX = touch.clientX - translateX;
                startY = touch.clientY - translateY;
                }

                updateTransform();
            });

            mermaidElement.addEventListener('touchcancel', (e) => {
                isDragging = false;
                isPinching = false;
                initialDistance = 0;

                setTimeout(() => {
                isTouch = false;
                }, 100);

                updateTransform();
            });

            // Enhanced wheel zoom with better center point handling
            container.addEventListener('wheel', (e) => {
                e.preventDefault();
                const rect = container.getBoundingClientRect();
                const centerX = rect.width / 2;
                const centerY = rect.height / 2;

                const delta = e.deltaY > 0 ? 0.9 : 1.1;
                const newScale = Math.min(Math.max(scale * delta, 0.3), 4);

                // Adjust translation to zoom towards center
                if (newScale !== scale) {
                const scaleDiff = newScale / scale;
                translateX = translateX * scaleDiff;
                translateY = translateY * scaleDiff;
                scale = newScale;

                if (scale <= 1) {
                    translateX = 0;
                    translateY = 0;
                }

                updateTransform();
                }
            });

            // Initialize display
            updateTransform();
            });
        }

        // Initialize the mermaid controls when the page loads
        document.addEventListener('DOMContentLoaded', function() {
            initializeMermaidControls();
        });
    </script>
  

</body></html>