<!DOCTYPE html>
<html lang="zh-CN"><head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>基于状态机的移动端App测试Agent设计方案</title>
    <!-- 原 CDN 及样式保留 ↓ -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;600;700&amp;family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <!-- 原 <style> 区域全部保留 ↓ -->
    <style>
        /* 优化的颜色方案 */
        :root {
            --primary: #2d6a4f;
            --primary-light: #40916c;
            --secondary: #74c69d;
            --accent: #95d5b2;
            --text-primary: #1a202c;
            --text-secondary: #4a5568;
            --text-muted: #718096;
            --bg-primary: #ffffff;
            --bg-secondary: #f7fafc;
            --bg-accent: #edf2f7;
            --border-color: #e2e8f0;
            --shadow-color: rgba(0, 0, 0, 0.1);
        }

        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.7;
            color: var(--text-primary);
            background-color: var(--bg-primary);
        }

        .serif {
            font-family: 'Noto Serif SC', serif;
        }

        .toc {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, var(--primary), var(--primary-light));
            z-index: 1000;
            overflow-y: auto;
            padding: 2rem 1.5rem;
            color: white;
        }

        .toc::-webkit-scrollbar {
            width: 4px;
        }

        .toc::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .toc::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
        }

        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }

        .toc a {
            display: block;
            padding: 0.5rem 0;
            color: rgba(255,255,255,0.9);
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
            font-weight: 400;
        }

        .toc a:hover {
            color: white;
            padding-left: 0.5rem;
            background-color: rgba(255,255,255,0.1);
            border-radius: 4px;
        }

        .toc .toc-level-2 {
            padding-left: 1rem;
            font-size: 0.9em;
            color: rgba(255,255,255,0.8);
        }

        .toc .toc-level-3 {
            padding-left: 2rem;
            font-size: 0.85em;
            color: rgba(255,255,255,0.7);
        }

        .hero-section {
            background: linear-gradient(135deg, var(--bg-secondary) 0%, #f1f8ff 100%);
            min-height: 60vh;
            position: relative;
            overflow: hidden;
        }

        .hero-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
            height: 100%;
        }

        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .hero-visual {
            position: relative;
            height: 400px;
            background: linear-gradient(45deg, rgba(45, 106, 79, 0.1), rgba(116, 198, 157, 0.1));
            border-radius: 16px;
            overflow: hidden;
        }

        .state-diagram {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
        }

        .section-header {
            border-left: 4px solid var(--primary);
            padding-left: 1.5rem;
            margin: 3rem 0 2rem 0;
        }

        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--primary);
        }

        .callout {
            background: linear-gradient(135deg, rgba(45, 106, 79, 0.05), rgba(116, 198, 157, 0.05));
            border-left: 4px solid var(--accent);
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
        }

        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }

        .architecture-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }

        .architecture-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }

        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin: 1.5rem 0;
        }

        .phase-timeline {
            position: relative;
            margin: 3rem 0;
        }

        .phase-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
        }

        .phase-item {
            display: flex;
            margin-bottom: 2rem;
            position: relative;
        }

        .phase-number {
            width: 2.5rem;
            height: 2.5rem;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1.5rem;
            flex-shrink: 0;
        }

        .phase-content {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
        }

        .highlight-box {
            background: linear-gradient(135deg, rgba(45, 106, 79, 0.1), rgba(116, 198, 157, 0.05));
            border: 1px solid var(--accent);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }

        @media (max-width: 1024px) {
            .toc {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .toc.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .hero-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
        }

        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .hero-visual {
                height: 300px;
            }
            
            .section-header h2 {
                font-size: 1.8rem;
            }
        }
    </style>
</head>

<body>
    <!-- 目录导航（与原文件相同，内容略） -->
    <nav class="toc">
        <div class="mb-8">
            <h3 class="text-xl font-bold text-white mb-4">目录导航</h3>
        </div>
        <a href="#overview">1. 核心问题与解决方案概述</a>
        <a href="#overview" class="toc-level-2">1.1 当前Agent面临的挑战</a>
        <a href="#overview" class="toc-level-2">1.2 状态机模式的核心价值</a>

        <a href="#architecture">2. 整体架构设计</a>
        <a href="#architecture" class="toc-level-2">2.1 架构概览：分层与模块化</a>
        <a href="#architecture" class="toc-level-2">2.2 关键组件交互流程</a>

        <a href="#core-modules">3. 核心模块详细设计</a>
        <a href="#core-modules" class="toc-level-2">3.1 状态机模块</a>
        <a href="#core-modules" class="toc-level-2">3.2 黑板记忆模块</a>

        <a href="#tools-api">4. 工具执行与API Server</a>
        <a href="#tools-api" class="toc-level-2">4.1 API Server的设计与实现</a>
        <a href="#tools-api" class="toc-level-2">4.2 工具函数的封装与调用</a>

        <a href="#autonomy">5. 自主性与异常处理机制</a>
        <a href="#autonomy" class="toc-level-2">5.1 自主决策规则引擎</a>
        <a href="#autonomy" class="toc-level-2">5.2 异常处理流程</a>

        <a href="#llm-integration">6. 与LLM的集成与交互</a>
        <a href="#llm-integration" class="toc-level-2">6.1 LLM在系统中的角色</a>
        <a href="#llm-integration" class="toc-level-2">6.2 构建传递给LLM的Prompt</a>
        <a href="#llm-integration" class="toc-level-2">6.3 处理LLM的返回与决策</a>

        <a href="#implementation">7. 阶段性测试与实现步骤</a>
        <a href="#implementation" class="toc-level-2">7.1 第一阶段：基础模块搭建</a>
        <a href="#implementation" class="toc-level-2">7.2 第二阶段：核心流程集成</a>
        <a href="#implementation" class="toc-level-2">7.3 第三阶段：自主性实现</a>
        <a href="#implementation" class="toc-level-2">7.4 第四阶段：优化与扩展</a>
    </nav>

    <!-- 其余正文内容（与原文件完全相同，此处省略） -->
    <!-- 如需完整文件，请直接将原 HTML 的 <body> 内容粘贴在下面 -->
    <div class="main-content">
        <!-- Hero Section -->
        <section class="hero-section">
            <div class="container mx-auto px-8 py-12 h-full">
                <div class="hero-grid">
                    <div class="hero-content">
                        <h1 class="serif">基于状态机的移动端App测试Agent设计方案</h1>
                        <p class="text-xl mb-6">解决长序列任务中的计划遗忘与自主决策挑战</p>
                        <div class="flex items-center space-x-6 text-sm">
                            <span><i class="fas fa-calendar mr-2"></i>2025年1月</span>
                            <span><i class="fas fa-code mr-2"></i>技术架构</span>
                            <span><i class="fas fa-mobile-alt mr-2"></i>移动端测试</span>
                        </div>
                    </div>
                    <div class="hero-visual">
                        <div class="state-diagram">
                            <div class="w-full h-full flex items-center justify-center bg-gradient-to-br from-green-600 to-green-800 rounded-lg">
                                <div class="text-center text-white">
                                    <i class="fas fa-project-diagram text-6xl mb-4 opacity-80"></i>
                                    <p class="text-lg font-semibold">状态机架构图</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>

        <!-- 其余正文内容与原文件完全一致，省略 -->
    </div>

    <!-- 原 JavaScript 保持不变 -->
    <script>
        /* 与原文相同的平滑滚动/移动端菜单脚本 */
        document.querySelectorAll('.toc a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    </script>

</body></html>