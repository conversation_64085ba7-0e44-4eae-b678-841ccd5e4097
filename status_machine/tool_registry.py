#!/usr/bin/env python3
"""
工具注册中心 - 为PlanConverter提供可扩展的工具元数据管理
"""

import json
import inspect
from typing import Dict, Any, List, Optional, Callable, Type
from dataclasses import dataclass, asdict
from enum import Enum


class ToolCategory(Enum):
    """工具分类"""
    DEVICE_MANAGEMENT = "device_management"  # 设备管理
    UI_INTERACTION = "ui_interaction"        # UI交互
    VALIDATION = "validation"                # 验证校验
    NAVIGATION = "navigation"                # 页面导航
    DATA_INPUT = "data_input"                # 数据输入
    SYSTEM_CONTROL = "system_control"        # 系统控制
    LOGGING = "logging"                      # 日志记录


class StateTransitionType(Enum):
    """状态转换类型"""
    NO_TRANSITION = "no_transition"          # 不引起状态转换
    PAGE_NAVIGATION = "page_navigation"      # 页面跳转
    MODAL_POPUP = "modal_popup"             # 弹窗出现
    APP_RESTART = "app_restart"             # 应用重启
    CONDITIONAL = "conditional"              # 条件性转换


@dataclass
class ToolMetadata:
    """工具元数据"""
    name: str
    category: ToolCategory
    description: str
    parameters: Dict[str, Any]  # 参数定义
    return_format: str          # 返回值格式说明
    transition_type: StateTransitionType
    transition_conditions: Dict[str, Any]  # 状态转换条件
    retry_config: Optional[Dict[str, Any]] = None
    dependencies: List[str] = None  # 依赖的其他工具
    conflict_tools: List[str] = None  # 冲突的工具
    
    def __post_init__(self):
        if self.dependencies is None:
            self.dependencies = []
        if self.conflict_tools is None:
            self.conflict_tools = []


class ToolRegistry:
    """工具注册中心 - 管理所有工具的元数据"""
    
    def __init__(self):
        self._tools: Dict[str, ToolMetadata] = {}
        self._categories: Dict[ToolCategory, List[str]] = {}
        self._register_default_tools()
    
    def register_tool(self, metadata: ToolMetadata) -> None:
        """注册工具"""
        self._tools[metadata.name] = metadata
        
        # 更新分类索引
        if metadata.category not in self._categories:
            self._categories[metadata.category] = []
        if metadata.name not in self._categories[metadata.category]:
            self._categories[metadata.category].append(metadata.name)
    
    def get_tool_metadata(self, tool_name: str) -> Optional[ToolMetadata]:
        """获取工具元数据"""
        return self._tools.get(tool_name)
    
    def get_tools_by_category(self, category: ToolCategory) -> List[str]:
        """根据分类获取工具列表"""
        return self._categories.get(category, [])
    
    def get_all_tools(self) -> Dict[str, ToolMetadata]:
        """获取所有工具"""
        return self._tools.copy()
    
    def is_navigation_tool(self, tool_name: str) -> bool:
        """判断是否为导航工具（会引起状态转换）"""
        metadata = self.get_tool_metadata(tool_name)
        if not metadata:
            return False
        return metadata.transition_type in [
            StateTransitionType.PAGE_NAVIGATION,
            StateTransitionType.APP_RESTART,
            StateTransitionType.CONDITIONAL
        ]
    
    def get_transition_conditions(self, tool_name: str) -> Dict[str, Any]:
        """获取工具的状态转换条件"""
        metadata = self.get_tool_metadata(tool_name)
        return metadata.transition_conditions if metadata else {}
    
    def validate_tool_sequence(self, tool_sequence: List[str]) -> List[str]:
        """验证工具序列的合法性，返回警告信息"""
        warnings = []
        
        for i, tool_name in enumerate(tool_sequence):
            metadata = self.get_tool_metadata(tool_name)
            if not metadata:
                warnings.append(f"未知工具: {tool_name}")
                continue
            
            # 检查依赖
            for dep in metadata.dependencies:
                if dep not in tool_sequence[:i]:
                    warnings.append(f"工具 {tool_name} 依赖 {dep}，但 {dep} 未在之前执行")
            
            # 检查冲突
            for conflict in metadata.conflict_tools:
                if conflict in tool_sequence:
                    warnings.append(f"工具 {tool_name} 与 {conflict} 存在冲突")
        
        return warnings
    
    def suggest_missing_tools(self, tool_sequence: List[str]) -> List[str]:
        """基于工具序列推荐可能缺失的工具"""
        suggestions = []
        
        # 检查是否缺少设备初始化
        device_tools = self.get_tools_by_category(ToolCategory.DEVICE_MANAGEMENT)
        if not any(tool in tool_sequence for tool in device_tools):
            suggestions.append("find_available_device")
            suggestions.append("start_device_test")
        
        # 检查是否缺少测试结束
        if "end_device_test" not in tool_sequence:
            suggestions.append("end_device_test")
        
        # 检查UI交互后是否缺少验证
        ui_tools = self.get_tools_by_category(ToolCategory.UI_INTERACTION)
        validation_tools = self.get_tools_by_category(ToolCategory.VALIDATION)
        
        has_ui_interaction = any(tool in tool_sequence for tool in ui_tools)
        has_validation = any(tool in tool_sequence for tool in validation_tools)
        
        if has_ui_interaction and not has_validation:
            suggestions.extend(["take_screenshot", "ocr_text_validation"])
        
        return suggestions
    
    def _register_default_tools(self):
        """注册默认工具元数据"""
        
        # 设备管理工具
        self.register_tool(ToolMetadata(
            name="find_available_device",
            category=ToolCategory.DEVICE_MANAGEMENT,
            description="查找可用设备",
            parameters={"platform": {"type": "string", "required": True, "default": "ios"}},
            return_format="{'status': 'success/error', 'udid': 'device_id', 'platform': 'ios/android'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        self.register_tool(ToolMetadata(
            name="start_device_test",
            category=ToolCategory.DEVICE_MANAGEMENT,
            description="启动设备测试环境",
            parameters={"udid": {"type": "string", "required": True, "source": "find_available_device.udid"}},
            return_format="{'status': 'success/error', 'message': 'description'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={},
            dependencies=["find_available_device"]
        ))
        
        self.register_tool(ToolMetadata(
            name="end_device_test",
            category=ToolCategory.DEVICE_MANAGEMENT,
            description="结束设备测试环境",
            parameters={"udid": {"type": "string", "required": True}},
            return_format="{'status': 'success/error', 'message': 'description'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        # UI交互工具
        self.register_tool(ToolMetadata(
            name="tap_device",
            category=ToolCategory.UI_INTERACTION,
            description="点击设备屏幕",
            parameters={
                "udid": {"type": "string", "required": True},
                "x": {"type": "int", "required": True, "source": "find_element_on_page.x"},
                "y": {"type": "int", "required": True, "source": "find_element_on_page.y"}
            },
            return_format="{'status': 'success/failed', 'message': 'description'}",
            transition_type=StateTransitionType.PAGE_NAVIGATION,
            transition_conditions={
                "success_triggers_navigation": True,
                "navigation_elements": ["按钮", "链接", "菜单项"]
            },
            dependencies=["find_element_on_page"]
        ))
        
        self.register_tool(ToolMetadata(
            name="slide_device",
            category=ToolCategory.UI_INTERACTION,
            description="滑动设备屏幕",
            parameters={
                "udid": {"type": "string", "required": True},
                "from_x": {"type": "float", "required": True},
                "from_y": {"type": "float", "required": True},
                "to_x": {"type": "float", "required": True},
                "to_y": {"type": "float", "required": True},
                "duration": {"type": "float", "required": False, "default": 0.5}
            },
            return_format="{'status': 'success/failed', 'message': 'description'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        # 验证工具
        self.register_tool(ToolMetadata(
            name="ocr_text_validation",
            category=ToolCategory.VALIDATION,
            description="OCR文本校验",
            parameters={
                "udid": {"type": "string", "required": True},
                "target_text": {"type": "string", "required": True}
            },
            return_format="{'status': 'success', 'found': true/false, 'matches': [...]}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={},
            retry_config={"max_retries": 2, "wait_seconds": 3}
        ))
        
        self.register_tool(ToolMetadata(
            name="take_screenshot",
            category=ToolCategory.VALIDATION,
            description="截取设备屏幕",
            parameters={"udid": {"type": "string", "required": True}},
            return_format="{'status': 'success', 'image_url': 'url', 'local_path': 'path'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        # 页面分析工具
        self.register_tool(ToolMetadata(
            name="find_element_on_page",
            category=ToolCategory.NAVIGATION,
            description="查找页面元素",
            parameters={
                "udid": {"type": "string", "required": True},
                "element": {"type": "string", "required": True},
                "scene_desc": {"type": "string", "required": False}
            },
            return_format="{'status': 'success', 'found': true/false, 'text_result': '...'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={},
            retry_config={"max_retries": 2, "wait_seconds": 3}
        ))
        
        self.register_tool(ToolMetadata(
            name="analyze_meituan_page",
            category=ToolCategory.NAVIGATION,
            description="分析美团app页面",
            parameters={
                "udid": {"type": "string", "required": True},
                "action_description": {"type": "string", "required": False},
                "model": {"type": "string", "required": False, "default": "qwen2.5vl:7b"}
            },
            return_format="{'status': 'success', 'text_result': '...', 'image_url': '...'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        # 数据输入工具
        self.register_tool(ToolMetadata(
            name="input_text_smart",
            category=ToolCategory.DATA_INPUT,
            description="智能文本输入",
            parameters={
                "udid": {"type": "string", "required": True},
                "text": {"type": "string", "required": True},
                "element_index": {"type": "int", "required": False}
            },
            return_format="{'status': 'success/failed', 'message': '...'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        # 系统控制工具
        self.register_tool(ToolMetadata(
            name="wait_seconds",
            category=ToolCategory.SYSTEM_CONTROL,
            description="等待指定秒数",
            parameters={"seconds": {"type": "int", "required": True, "default": 3}},
            return_format="{'status': 'success', 'message': '...'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
        
        self.register_tool(ToolMetadata(
            name="restart_application",
            category=ToolCategory.SYSTEM_CONTROL,
            description="重启应用",
            parameters={"udid": {"type": "string", "required": True}},
            return_format="{'status': 'success/failed', 'message': '...'}",
            transition_type=StateTransitionType.APP_RESTART,
            transition_conditions={"always_triggers_restart": True}
        ))
        
        # 日志记录工具
        self.register_tool(ToolMetadata(
            name="record_agent_summary",
            category=ToolCategory.LOGGING,
            description="记录Agent执行总结",
            parameters={
                "summary_text": {"type": "string", "required": True},
                "summary_type": {"type": "string", "required": False, "default": "状态执行总结"}
            },
            return_format="{'status': 'success', 'message': '...'}",
            transition_type=StateTransitionType.NO_TRANSITION,
            transition_conditions={}
        ))
    
    def export_registry(self, file_path: str) -> None:
        """导出注册表到文件"""
        data = {
            'tools': {name: asdict(metadata) for name, metadata in self._tools.items()},
            'categories': {cat.value: tools for cat, tools in self._categories.items()}
        }
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
    
    def import_registry(self, file_path: str) -> None:
        """从文件导入注册表"""
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        for tool_name, tool_data in data['tools'].items():
            # 重构枚举类型
            tool_data['category'] = ToolCategory(tool_data['category'])
            tool_data['transition_type'] = StateTransitionType(tool_data['transition_type'])
            
            metadata = ToolMetadata(**tool_data)
            self.register_tool(metadata)


# 创建全局工具注册中心实例
_global_registry = None

def get_tool_registry() -> ToolRegistry:
    """获取全局工具注册中心"""
    global _global_registry
    if _global_registry is None:
        _global_registry = ToolRegistry()
    return _global_registry


def register_new_tool(name: str, 
                     category: ToolCategory,
                     description: str,
                     parameters: Dict[str, Any],
                     return_format: str,
                     transition_type: StateTransitionType = StateTransitionType.NO_TRANSITION,
                     transition_conditions: Dict[str, Any] = None,
                     **kwargs) -> None:
    """便捷函数：注册新工具"""
    if transition_conditions is None:
        transition_conditions = {}
    
    metadata = ToolMetadata(
        name=name,
        category=category,
        description=description,
        parameters=parameters,
        return_format=return_format,
        transition_type=transition_type,
        transition_conditions=transition_conditions,
        **kwargs
    )
    
    get_tool_registry().register_tool(metadata)


# 装饰器：自动注册工具
def auto_register_tool(category: ToolCategory, 
                      transition_type: StateTransitionType = StateTransitionType.NO_TRANSITION,
                      transition_conditions: Dict[str, Any] = None,
                      **metadata_kwargs):
    """装饰器：自动注册工具到注册中心"""
    def decorator(func: Callable):
        # 从函数签名提取参数信息
        sig = inspect.signature(func)
        parameters = {}
        for param_name, param in sig.parameters.items():
            param_info = {"type": str(param.annotation).replace("<class '", "").replace("'>", "")}
            if param.default != inspect.Parameter.empty:
                param_info["default"] = param.default
                param_info["required"] = False
            else:
                param_info["required"] = True
            parameters[param_name] = param_info
        
        # 创建工具元数据
        metadata = ToolMetadata(
            name=func.__name__,
            category=category,
            description=func.__doc__ or f"工具函数: {func.__name__}",
            parameters=parameters,
            return_format="由函数返回值格式决定",
            transition_type=transition_type,
            transition_conditions=transition_conditions or {},
            **metadata_kwargs
        )
        
        # 注册工具
        get_tool_registry().register_tool(metadata)
        
        return func
    return decorator


if __name__ == "__main__":
    # 测试工具注册中心
    registry = get_tool_registry()
    
    print("🔧 工具注册中心测试")
    print(f"📊 总工具数量: {len(registry.get_all_tools())}")
    
    # 测试分类查询
    ui_tools = registry.get_tools_by_category(ToolCategory.UI_INTERACTION)
    print(f"🖱️  UI交互工具: {ui_tools}")
    
    # 测试工具序列验证
    test_sequence = ["find_available_device", "tap_device", "ocr_text_validation"]
    warnings = registry.validate_tool_sequence(test_sequence)
    suggestions = registry.suggest_missing_tools(test_sequence)
    
    print(f"⚠️ 验证警告: {warnings}")
    print(f"💡 建议添加: {suggestions}")
    
    # 测试新工具注册
    @auto_register_tool(
        category=ToolCategory.VALIDATION,
        transition_type=StateTransitionType.NO_TRANSITION
    )
    def my_custom_validation_tool(udid: str, validation_text: str, timeout: int = 30):
        """我的自定义验证工具"""
        return {"status": "success", "validated": True}
    
    print(f"✅ 自定义工具已注册: {registry.get_tool_metadata('my_custom_validation_tool').name}")
    print("🎉 工具注册中心测试完成")