{"plan_id": "plan_12345", "original_request": "目标平台: iOS\n测试用例: 点击左上角的地址，进入地址选择页后，校验地址选择页搜索框默认文案是否为“搜索城市/区县/地点”。点击当前页面搜索框，校验进入地址搜索页及搜索框默认文案为“搜索城市/区县/地点”，在搜索框输入文字“北京”，点击搜索结果“北京市”，校验返回首页，校验首页地址展示北京", "summary": "测试美团应用中的地址选择和搜索功能", "platform": "ios", "total_steps": 19, "steps": [{"step_id": 1, "action": "find_available_device", "description": "查找可用的iOS设备", "parameters": {"platform": "ios"}, "parameter_types": {"platform": "static"}, "parameter_sources": {}, "expected_result": "成功找到iOS设备"}, {"step_id": 2, "action": "start_device_test", "description": "启动测试会话", "parameters": {"udid": "{device_udid}"}, "parameter_types": {"udid": "dynamic"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功启动测试会话"}, {"step_id": 3, "action": "find_element_on_page", "description": "查找左上角地址元素", "parameters": {"udid": "{device_udid}", "element": "左上角地址"}, "parameter_types": {"udid": "dynamic", "element": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功找到左上角地址元素"}, {"step_id": 4, "action": "tap_device", "description": "点击左上角地址进入地址选择页", "parameters": {"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}, "parameter_types": {"udid": "dynamic", "x": "dynamic", "y": "dynamic"}, "parameter_sources": {"udid": "来自find_available_device结果", "x": "来自find_element_on_page结果", "y": "来自find_element_on_page结果"}, "expected_result": "成功点击左上角地址进入地址选择页"}, {"step_id": 5, "action": "wait_seconds", "description": "等待页面加载完成", "parameters": {"seconds": 3}, "parameter_types": {"seconds": "static"}, "parameter_sources": {}, "expected_result": "页面加载完成"}, {"step_id": 6, "action": "ocr_text_validation", "description": "校验地址选择页搜索框默认文案为“搜索城市/区县/地点”", "parameters": {"udid": "{device_udid}", "target_text": "搜索城市/区县/地点"}, "parameter_types": {"udid": "dynamic", "target_text": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功校验地址选择页搜索框默认文案"}, {"step_id": 7, "action": "find_element_on_page", "description": "查找当前页面搜索框元素", "parameters": {"udid": "{device_udid}", "element": "搜索框"}, "parameter_types": {"udid": "dynamic", "element": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功找到当前页面搜索框元素"}, {"step_id": 8, "action": "tap_device", "description": "点击搜索框进入地址搜索页", "parameters": {"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}, "parameter_types": {"udid": "dynamic", "x": "dynamic", "y": "dynamic"}, "parameter_sources": {"udid": "来自find_available_device结果", "x": "来自find_element_on_page结果", "y": "来自find_element_on_page结果"}, "expected_result": "成功点击搜索框进入地址搜索页"}, {"step_id": 9, "action": "wait_seconds", "description": "等待页面加载完成", "parameters": {"seconds": 3}, "parameter_types": {"seconds": "static"}, "parameter_sources": {}, "expected_result": "页面加载完成"}, {"step_id": 10, "action": "ocr_text_validation", "description": "校验地址搜索页搜索框默认文案为“搜索城市/区县/地点”", "parameters": {"udid": "{device_udid}", "target_text": "搜索城市/区县/地点"}, "parameter_types": {"udid": "dynamic", "target_text": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功校验地址搜索页搜索框默认文案"}, {"step_id": 11, "action": "find_element_on_page", "description": "查找输入框元素", "parameters": {"udid": "{device_udid}", "element": "输入框"}, "parameter_types": {"udid": "dynamic", "element": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功找到输入框元素"}, {"step_id": 12, "action": "tap_device", "description": "点击输入框激活输入状态", "parameters": {"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}, "parameter_types": {"udid": "dynamic", "x": "dynamic", "y": "dynamic"}, "parameter_sources": {"udid": "来自find_available_device结果", "x": "来自find_element_on_page结果", "y": "来自find_element_on_page结果"}, "expected_result": "成功点击输入框激活输入状态"}, {"step_id": 13, "action": "input_text_smart", "description": "在搜索框中输入文字“北京”", "parameters": {"udid": "{device_udid}", "text": "北京"}, "parameter_types": {"udid": "dynamic", "text": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功输入文字“北京”"}, {"step_id": 14, "action": "wait_seconds", "description": "等待页面加载完成", "parameters": {"seconds": 3}, "parameter_types": {"seconds": "static"}, "parameter_sources": {}, "expected_result": "页面加载完成"}, {"step_id": 15, "action": "find_element_on_page", "description": "查找搜索结果“北京市”元素", "parameters": {"udid": "{device_udid}", "element": "北京市"}, "parameter_types": {"udid": "dynamic", "element": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功找到搜索结果“北京市”元素"}, {"step_id": 16, "action": "tap_device", "description": "点击搜索结果“北京市”", "parameters": {"udid": "{device_udid}", "x": "{element_x}", "y": "{element_y}"}, "parameter_types": {"udid": "dynamic", "x": "dynamic", "y": "dynamic"}, "parameter_sources": {"udid": "来自find_available_device结果", "x": "来自find_element_on_page结果", "y": "来自find_element_on_page结果"}, "expected_result": "成功点击搜索结果“北京市”"}, {"step_id": 17, "action": "wait_seconds", "description": "等待页面加载完成", "parameters": {"seconds": 3}, "parameter_types": {"seconds": "static"}, "parameter_sources": {}, "expected_result": "页面加载完成"}, {"step_id": 18, "action": "ocr_text_validation", "description": "校验返回首页地址展示北京", "parameters": {"udid": "{device_udid}", "target_text": "北京"}, "parameter_types": {"udid": "dynamic", "target_text": "static"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功校验返回首页地址展示北京"}, {"step_id": 19, "action": "end_device_test", "description": "结束测试会话", "parameters": {"udid": "{device_udid}"}, "parameter_types": {"udid": "dynamic"}, "parameter_sources": {"udid": "来自find_available_device结果"}, "expected_result": "成功结束测试会话"}]}