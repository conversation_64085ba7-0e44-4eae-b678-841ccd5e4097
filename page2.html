<!DOCTYPE html><html lang="zh-CN"><head>
    <meta charset="UTF-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <title>基于状态机的移动端App测试Agent设计方案</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/js/all.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/echarts/5.4.3/echarts.min.js"></script>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Serif+SC:wght@400;600;700&amp;family=Inter:wght@300;400;500;600;700&amp;display=swap" rel="stylesheet"/>
    <style>
        :root {
            --primary: #2d6a4f;
            --secondary: #40916c;
            --accent: #74c69d;
            --neutral: #f8f9fa;
            --base-100: #ffffff;
            --base-content: #1a1a1a;
        }
        
        body {
            font-family: 'Inter', sans-serif;
            line-height: 1.7;
            color: var(--base-content);
        }
        
        .serif {
            font-family: 'Noto Serif SC', serif;
        }
        
        .toc {
            position: fixed;
            left: 0;
            top: 0;
            width: 280px;
            height: 100vh;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            z-index: 1000;
            overflow-y: auto;
            padding: 2rem 1.5rem;
            color: white;
        }
        
        .toc::-webkit-scrollbar {
            width: 4px;
        }
        
        .toc::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }
        
        .toc::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 2px;
        }
        
        .main-content {
            margin-left: 280px;
            min-height: 100vh;
        }
        
        .toc a {
            display: block;
            padding: 0.5rem 0;
            color: rgba(255,255,255,0.8);
            text-decoration: none;
            border-bottom: 1px solid rgba(255,255,255,0.1);
            transition: all 0.3s ease;
        }
        
        .toc a:hover {
            color: white;
            padding-left: 0.5rem;
        }
        
        .toc .toc-level-2 {
            padding-left: 1rem;
            font-size: 0.9em;
        }
        
        .toc .toc-level-3 {
            padding-left: 2rem;
            font-size: 0.85em;
        }
        
        .hero-section {
            background: linear-gradient(135deg, var(--neutral) 0%, #f1f3f5 100%);
            min-height: 60vh;
            position: relative;
            overflow: hidden;
        }
        
        .hero-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 3rem;
            align-items: center;
            height: 100%;
        }
        
        .hero-content h1 {
            font-size: 3.5rem;
            font-weight: 700;
            line-height: 1.1;
            margin-bottom: 1.5rem;
            background: linear-gradient(135deg, var(--primary), var(--secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        
        .hero-visual {
            position: relative;
            height: 400px;
            background: linear-gradient(45deg, rgba(45, 106, 79, 0.1), rgba(116, 198, 157, 0.1));
            border-radius: 16px;
            overflow: hidden;
        }
        
        .state-diagram {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            width: 80%;
            height: 80%;
        }
        
        .section-header {
            border-left: 4px solid var(--primary);
            padding-left: 1.5rem;
            margin: 3rem 0 2rem 0;
        }
        
        .section-header h2 {
            font-size: 2.5rem;
            font-weight: 600;
            color: var(--primary);
        }
        
        .callout {
            background: linear-gradient(135deg, rgba(45, 106, 79, 0.05), rgba(116, 198, 157, 0.05));
            border-left: 4px solid var(--accent);
            padding: 1.5rem;
            margin: 2rem 0;
            border-radius: 0 8px 8px 0;
        }
        
        .architecture-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
            margin: 2rem 0;
        }
        
        .architecture-card {
            background: white;
            border-radius: 12px;
            padding: 2rem;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            border: 1px solid #e5e7eb;
            transition: all 0.3s ease;
        }
        
        .architecture-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px -3px rgba(0, 0, 0, 0.1);
        }
        
        .code-block {
            background: #1e293b;
            color: #e2e8f0;
            padding: 1.5rem;
            border-radius: 8px;
            overflow-x: auto;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            margin: 1.5rem 0;
        }
        
        .phase-timeline {
            position: relative;
            margin: 3rem 0;
        }
        
        .phase-timeline::before {
            content: '';
            position: absolute;
            left: 1rem;
            top: 0;
            bottom: 0;
            width: 2px;
            background: linear-gradient(to bottom, var(--primary), var(--accent));
        }
        
        .phase-item {
            display: flex;
            margin-bottom: 2rem;
            position: relative;
        }
        
        .phase-number {
            width: 2.5rem;
            height: 2.5rem;
            background: var(--primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-right: 1.5rem;
            flex-shrink: 0;
        }
        
        .phase-content {
            background: white;
            border-radius: 8px;
            padding: 1.5rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            flex: 1;
        }
        
        .highlight-box {
            background: linear-gradient(135deg, rgba(45, 106, 79, 0.1), rgba(116, 198, 157, 0.05));
            border: 1px solid var(--accent);
            border-radius: 8px;
            padding: 1.5rem;
            margin: 1.5rem 0;
        }
        
        @media (max-width: 1024px) {
            .toc {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            
            .toc.open {
                transform: translateX(0);
            }
            
            .main-content {
                margin-left: 0;
            }
            
            .hero-grid {
                grid-template-columns: 1fr;
                gap: 2rem;
            }
            
            .hero-content h1 {
                font-size: 2.5rem;
            }
        }
        
        @media (max-width: 768px) {
            .container {
                padding-left: 1rem;
                padding-right: 1rem;
            }
            
            .hero-content h1 {
                font-size: 2rem;
            }
            
            .hero-visual {
                height: 300px;
            }
            
            .section-header h2 {
                font-size: 1.8rem;
            }
        }
    </style>
  </head>

  <body>
    <!-- Table of Contents -->
    <nav class="toc">
      <div class="mb-8">
        <h3 class="text-xl font-bold text-white mb-4">目录导航</h3>
      </div>
      <a href="#overview">1. 核心问题与解决方案概述</a>
      <a href="#overview" class="toc-level-2">1.1 当前Agent面临的挑战</a>
      <a href="#overview" class="toc-level-2">1.2 状态机模式的核心价值</a>

      <a href="#architecture">2. 整体架构设计</a>
      <a href="#architecture" class="toc-level-2">2.1 架构概览：分层与模块化</a>
      <a href="#architecture" class="toc-level-2">2.2 关键组件交互流程</a>

      <a href="#core-modules">3. 核心模块详细设计</a>
      <a href="#core-modules" class="toc-level-2">3.1 状态机模块</a>
      <a href="#core-modules" class="toc-level-2">3.2 黑板记忆模块</a>

      <a href="#tools-api">4. 工具执行与API Server</a>
      <a href="#tools-api" class="toc-level-2">4.1 API Server的设计与实现</a>
      <a href="#tools-api" class="toc-level-2">4.2 工具函数的封装与调用</a>

      <a href="#autonomy">5. 自主性与异常处理机制</a>
      <a href="#autonomy" class="toc-level-2">5.1 自主决策规则引擎</a>
      <a href="#autonomy" class="toc-level-2">5.2 异常处理流程</a>

      <a href="#llm-integration">6. 与LLM的集成与交互</a>
      <a href="#llm-integration" class="toc-level-2">6.1 LLM在系统中的角色</a>
      <a href="#llm-integration" class="toc-level-2">6.2 构建传递给LLM的Prompt</a>
      <a href="#llm-integration" class="toc-level-2">6.3 处理LLM的返回与决策</a>

      <a href="#implementation">7. 阶段性测试与实现步骤</a>
      <a href="#implementation" class="toc-level-2">7.1 第一阶段：基础模块搭建</a>
      <a href="#implementation" class="toc-level-2">7.2 第二阶段：核心流程集成</a>
      <a href="#implementation" class="toc-level-2">7.3 第三阶段：自主性实现</a>
      <a href="#implementation" class="toc-level-2">7.4 第四阶段：优化与扩展</a>
    </nav>

    <!-- Main Content -->
    <div class="main-content">
      <!-- Hero Section -->
      <section class="hero-section">
        <div class="container mx-auto px-8 py-12 h-full">
          <div class="hero-grid">
            <div class="hero-content">
              <h1 class="serif">基于状态机的移动端App测试Agent设计方案</h1>
              <p class="text-xl text-gray-600 mb-6">解决长序列任务中的计划遗忘与自主决策挑战</p>
              <div class="flex items-center space-x-6 text-sm text-gray-500">
                <span><i class="fas fa-calendar mr-2"></i>2025年6月</span>
                <span><i class="fas fa-code mr-2"></i>技术架构</span>
                <span><i class="fas fa-mobile-alt mr-2"></i>移动端测试</span>
              </div>
            </div>
            <div class="hero-visual">
              <div class="state-diagram">
                <img src="https://kimi-web-img.moonshot.cn/img/obs-emcsapp-public.obs.cn-north-4.myhwclouds.com/6d0e289d8b952f68635649efe58a4131ea5a8eec.png" alt="分层架构抽象表示" class="w-full h-full object-cover rounded-lg opacity-80" size="medium" aspect="square" query="分层架构抽象图" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>
              </div>
            </div>
          </div>
        </div>
      </section>

      <!-- Main Content Area -->
      <div class="container mx-auto px-8 py-12 max-w-5xl">

        <!-- Section 1: Overview -->
        <section id="overview" class="mb-16">
          <div class="section-header">
            <h2 class="serif">1. 核心问题与解决方案概述</h2>
          </div>

          <div class="highlight-box">
            <p class="text-lg font-medium text-gray-800 mb-4">
              <i class="fas fa-lightbulb text-yellow-500 mr-2"></i>
              本方案针对移动端App测试Agent在执行长序列任务时出现的&#34;计划遗忘&#34;和缺乏自主异常处理能力的问题，提出了一套基于<strong>状态机（State Machine）</strong>和<strong>黑板记忆（Blackboard）</strong>的详细技术架构。
            </p>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-4 mt-8">1.1 当前Agent面临的挑战</h3>

          <div class="architecture-grid">
            <div class="architecture-card">
              <h4 class="text-xl font-semibold text-gray-800 mb-3">
                <i class="fas fa-brain text-red-500 mr-2"></i>
                LLM上下文窗口限制
              </h4>
              <p class="text-gray-600">
                尽管本地模型拥有高达40k的上下文窗口，Agent在执行5-6步后仍会出现&#34;计划遗忘&#34;，偏离预设的测试流程。LLM的注意力机制会逐渐稀释早期的关键指令。
              </p>
            </div>

            <div class="architecture-card">
              <h4 class="text-xl font-semibold text-gray-800 mb-3">
                <i class="fas fa-exclamation-triangle text-orange-500 mr-2"></i>
                缺乏自主异常处理
              </h4>
              <p class="text-gray-600">
                当遇到元素查找失败、页面加载延迟等常见异常时，Agent往往直接报错终止，缺乏自动重试、回退等基本的自我修复能力。
              </p>
            </div>

            <div class="architecture-card">
              <h4 class="text-xl font-semibold text-gray-800 mb-3">
                <i class="fas fa-balance-scale text-blue-500 mr-2"></i>
                执行与自主性平衡
              </h4>
              <p class="text-gray-600">
                在严格遵循测试计划的机械执行与应对变化的灵活性之间难以找到平衡点，需要一种既能保证确定性又能处理意外情况的机制。
              </p>
            </div>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-4 mt-12">1.2 状态机模式的核心价值</h3>

          <div class="callout">
            <p class="text-gray-700 mb-4">
              状态机模式将复杂的测试流程分解为以<strong>App界面状态为核心&#34;主键&#34;</strong>的离散状态，通过状态转换严格控制流程，并利用黑板记忆管理上下文，实现精确执行与自主决策的平衡。
            </p>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mt-8">
            <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-green-500">
              <h4 class="font-semibold text-gray-800 mb-2">界面状态为核心</h4>
              <p class="text-sm text-gray-600">以App页面跳转为状态边界，每个状态内操作上下文高度相关，降低决策复杂性</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-blue-500">
              <h4 class="font-semibold text-gray-800 mb-2">黑板记忆优化</h4>
              <p class="text-sm text-gray-600">通过中央知识库存储跨状态信息，实现上下文精简与历史信息持久化的平衡</p>
            </div>
            <div class="bg-white p-6 rounded-lg shadow-md border-l-4 border-purple-500">
              <h4 class="font-semibold text-gray-800 mb-2">可配置自主性</h4>
              <p class="text-sm text-gray-600">基于规则的重试逻辑与LLM智能决策相结合，实现可控的自主异常处理</p>
            </div>
          </div>
        </section>

        <!-- Section 2: Architecture -->
        <section id="architecture" class="mb-16">
          <div class="section-header">
            <h2 class="serif">2. 整体架构设计</h2>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">2.1 架构概览：分层与模块化</h3>

          <div class="bg-white rounded-lg shadow-lg p-8 mb-8">
            <img src="https://ark-content-generation-v2-cn-beijing.tos-cn-beijing.volces.com/doubao-seedream-3-0-t2i/02175497096573214cb1f592056f180633afb79b9f41344b1f517.jpeg?X-Tos-Algorithm=TOS4-HMAC-SHA256&amp;X-Tos-Credential=AKLTYWJkZTExNjA1ZDUyNDc3YzhjNTM5OGIyNjBhNDcyOTQ%2F20250812%2Fcn-beijing%2Ftos%2Frequest&amp;X-Tos-Date=20250812T035608Z&amp;X-Tos-Expires=86400&amp;X-Tos-Signature=dabc68efdf0c9b9f8a9db6ab2080db8383ac09973b9ab04345859b1dbe721fed&amp;X-Tos-SignedHeaders=host&amp;x-tos-process=image%2Fwatermark%2Cimage_YXNzZXRzL3dhdGVybWFyay5wbmc_eC10b3MtcHJvY2Vzcz1pbWFnZS9yZXNpemUsUF85" alt="测试自动化系统三层架构示意图" class="w-full h-64 object-cover rounded-lg mb-6" size="medium" aspect="wide" style="linedrawing" query="测试自动化系统三层架构图" referrerpolicy="no-referrer" data-modified="1" data-score="0.00"/>

            <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="fas fa-cogs text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-2">核心引擎层</h4>
                <p class="text-sm text-gray-600">状态机 + 黑板记忆</p>
              </div>
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="fas fa-tools text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-2">工具执行层</h4>
                <p class="text-sm text-gray-600">API Server + 工具封装</p>
              </div>
              <div class="text-center">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto mb-4">
                  <i class="fas fa-brain text-white text-xl"></i>
                </div>
                <h4 class="font-semibold text-gray-800 mb-2">决策规划层</h4>
                <p class="text-sm text-gray-600">LLM + 规则引擎</p>
              </div>
            </div>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">2.2 关键组件交互流程</h3>

          <div class="space-y-6">
            <div class="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg">
              <div class="w-8 h-8 bg-green-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">1</div>
              <div>
                <h4 class="font-semibold text-gray-800 mb-2">测试计划转换</h4>
                <p class="text-gray-600 text-sm">将线性的JSON测试计划转换为以界面状态为核心的状态机配置，识别页面跳转点，构建状态节点图</p>
              </div>
            </div>

            <div class="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg">
              <div class="w-8 h-8 bg-blue-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">2</div>
              <div>
                <h4 class="font-semibold text-gray-800 mb-2">状态驱动执行</h4>
                <p class="text-gray-600 text-sm">状态机从初始状态开始，读取动作列表，通过API Server调用工具，处理返回结果</p>
              </div>
            </div>

            <div class="flex items-start space-x-4 p-6 bg-gray-50 rounded-lg">
              <div class="w-8 h-8 bg-purple-500 text-white rounded-full flex items-center justify-center font-semibold text-sm">3</div>
              <div>
                <h4 class="font-semibold text-gray-800 mb-2">状态转换与更新</h4>
                <p class="text-gray-600 text-sm">根据工具返回结果触发状态转换，同时更新黑板记忆，清除旧状态上下文，加载新状态信息</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Section 3: Core Modules -->
        <section id="core-modules" class="mb-16">
          <div class="section-header">
            <h2 class="serif">3. 核心模块详细设计</h2>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">3.1 状态机（State Machine）模块</h3>

          <div class="callout">
            <h4 class="font-semibold text-gray-800 mb-3">关键设计原则</h4>
            <p class="text-gray-700">状态识别严格以<strong>App界面状态为核心&#34;主键&#34;</strong>，状态的边界由明确的页面跳转事件界定。</p>
          </div>

          <h4 class="text-xl font-semibold text-gray-800 mb-4">状态数据结构示例</h4>
          <div class="code-block">
            <pre>{
  &#34;MainPage&#34;: {
    &#34;actions&#34;: [
      {
        &#34;tool&#34;: &#34;find_element&#34;,
        &#34;parameters&#34;: {&#34;element&#34;: &#34;左上角地址&#34;},
        &#34;expected_result&#34;: &#34;element_found&#34;
      }
    ],
    &#34;transitions&#34;: {
      &#34;element_found&#34;: &#34;AddressSelectionPage&#34;,
      &#34;element_not_found&#34;: &#34;ErrorState&#34;
    }
  },
  &#34;AddressSelectionPage&#34;: {
    &#34;actions&#34;: [
      {
        &#34;tool&#34;: &#34;ocr_validate_text&#34;,
        &#34;parameters&#34;: {&#34;target_text&#34;: &#34;搜索城市/区县/地点&#34;},
        &#34;expected_result&#34;: &#34;text_validated&#34;
      },
      {
        &#34;tool&#34;: &#34;tap&#34;,
        &#34;parameters&#34;: {&#34;element&#34;: &#34;搜索框&#34;},
        &#34;expected_result&#34;: &#34;tap_executed&#34;
      }
    ],
    &#34;transitions&#34;: {
      &#34;text_validated&#34;: &#34;AddressSelectionPage&#34;,
      &#34;tap_executed&#34;: &#34;SearchPage&#34;
    }
  }
}</pre>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6 mt-12">3.2 黑板记忆（Blackboard）模块</h3>

          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">核心数据结构设计</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 class="font-medium text-gray-700 mb-2">设备信息</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• udid: 设备唯一标识符</li>
                  <li>• platform: 测试平台 (ios/android)</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">页面信息</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• current_page_name: 当前状态名称</li>
                  <li>• current_page_screenshot: 页面截图</li>
                  <li>• current_page_layout: 布局信息</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">元素信息</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• elements: 关键元素坐标缓存</li>
                  <li>• found_texts: OCR识别文本</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">执行日志</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• execution_history: 动作执行历史</li>
                  <li>• error_log: 异常记录</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="highlight-box">
            <h4 class="font-semibold text-gray-800 mb-3">记忆更新策略</h4>
            <p class="text-gray-700 text-sm mb-3">
              状态切换时执行策略性的&#34;记忆更新&#34;与&#34;上下文清除&#34;：
            </p>
            <ul class="text-sm text-gray-700 space-y-1">
              <li>• 离开旧状态：持久化关键信息到黑板</li>
              <li>• 进入新状态：更新页面信息，清除旧状态上下文</li>
              <li>• 保留跨状态共享信息（如设备UDID）</li>
            </ul>
          </div>
        </section>

        <!-- Section 4: Tools and API -->
        <section id="tools-api" class="mb-16">
          <div class="section-header">
            <h2 class="serif">4. 工具执行与API Server模块</h2>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">4.1 API Server的设计与实现</h3>

          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">技术选型：基于Flask的轻量级服务器</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h5 class="font-medium text-gray-700 mb-2">优势特性</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 轻量快速，低性能开销</li>
                  <li>• 易于与Python工具函数集成</li>
                  <li>• 灵活性强，便于扩展</li>
                  <li>• 活跃的社区支持</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">部署配置</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 独立进程运行</li>
                  <li>• 监听 localhost:5000</li>
                  <li>• RESTful API设计</li>
                  <li>• 统一的JSON请求/响应格式</li>
                </ul>
              </div>
            </div>
          </div>

          <h4 class="text-xl font-semibold text-gray-800 mb-4">核心API端点设计</h4>

          <div class="space-y-6">
            <div class="bg-gray-50 rounded-lg p-6">
              <h5 class="font-semibold text-gray-800 mb-3">POST /find_element - 查找页面元素</h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h6 class="text-sm font-medium text-gray-700 mb-2">请求示例</h6>
                  <div class="code-block text-xs">
                    <pre>{
  &#34;udid&#34;: &#34;device123&#34;,
  &#34;element&#34;: &#34;左上角地址&#34;
}</pre>
                  </div>
                </div>
                <div>
                  <h6 class="text-sm font-medium text-gray-700 mb-2">成功响应</h6>
                  <div class="code-block text-xs">
                    <pre>{
  &#34;status&#34;: &#34;success&#34;,
  &#34;result&#34;: &#34;element_found&#34;,
  &#34;data&#34;: {&#34;x&#34;: 100, &#34;y&#34;: 200}
}</pre>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h5 class="font-semibold text-gray-800 mb-3">POST /ocr_validate_text - OCR文本校验</h5>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h6 class="text-sm font-medium text-gray-700 mb-2">请求示例</h6>
                  <div class="code-block text-xs">
                    <pre>{
  &#34;udid&#34;: &#34;device123&#34;,
  &#34;target_text&#34;: &#34;搜索城市/区县/地点&#34;
}</pre>
                  </div>
                </div>
                <div>
                  <h6 class="text-sm font-medium text-gray-700 mb-2">失败响应</h6>
                  <div class="code-block text-xs">
                    <pre>{
  &#34;status&#34;: &#34;error&#34;,
  &#34;result&#34;: &#34;text_not_found&#34;,
  &#34;message&#34;: &#34;Target text not found&#34;
}</pre>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <div class="callout mt-8">
            <h4 class="font-semibold text-gray-800 mb-3">统一接口设计原则</h4>
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <h5 class="font-medium text-gray-700 mb-1">统一请求格式</h5>
                <p class="text-sm text-gray-600">所有端点使用HTTP POST + JSON请求体</p>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-1">统一响应格式</h5>
                <p class="text-sm text-gray-600">标准化status/result/data字段结构</p>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-1">统一错误处理</h5>
                <p class="text-sm text-gray-600">异常捕获并转换为结构化错误响应</p>
              </div>
            </div>
          </div>
        </section>

        <!-- Section 5: Autonomy -->
        <section id="autonomy" class="mb-16">
          <div class="section-header">
            <h2 class="serif">5. 自主性与异常处理机制</h2>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">5.1 自主决策规则引擎</h3>

          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">规则定义与配置</h4>
            <div class="code-block">
              <pre>{
  &#34;rules&#34;: [
    {
      &#34;trigger&#34;: &#34;element_not_found&#34;,
      &#34;action&#34;: &#34;retry&#34;,
      &#34;parameters&#34;: {
        &#34;wait_seconds&#34;: 3,
        &#34;max_retries&#34;: 2
      },
      &#34;scope&#34;: &#34;global&#34;
    },
    {
      &#34;trigger&#34;: &#34;tap_failed&#34;,
      &#34;action&#34;: &#34;call_llm&#34;,
      &#34;parameters&#34;: {
        &#34;timeout&#34;: 30
      },
      &#34;scope&#34;: &#34;tap_tools&#34;
    }
  ]
}</pre>
            </div>
          </div>

          <h4 class="text-xl font-semibold text-gray-800 mb-4">简单错误的自动重试机制</h4>

          <div class="space-y-4">
            <div class="bg-green-50 border-l-4 border-green-400 p-4 rounded">
              <h5 class="font-semibold text-green-800 mb-2">元素查找失败重试流程</h5>
              <ol class="text-sm text-green-700 space-y-1">
                <li>1. 首次查找失败，返回&#34;element_not_found&#34;</li>
                <li>2. 规则引擎匹配重试规则，等待3秒</li>
                <li>3. 自动重试查找，最多重试2次</li>
                <li>4. 若重试成功，继续执行；否则上报LLM</li>
              </ol>
            </div>

            <div class="bg-blue-50 border-l-4 border-blue-400 p-4 rounded">
              <h5 class="font-semibold text-blue-800 mb-2">页面加载超时处理</h5>
              <ol class="text-sm text-blue-700 space-y-1">
                <li>1. 点击操作后连续查找元素失败</li>
                <li>2. 调用get_current_page检查页面状态</li>
                <li>3. 页面未变：重试点击；已变：增加等待时间</li>
                <li>4. 再次尝试查找目标元素</li>
              </ol>
            </div>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6 mt-12">5.2 异常处理流程</h3>

          <div class="highlight-box">
            <h4 class="font-semibold text-gray-800 mb-3">复杂错误的LLM决策机制</h4>
            <p class="text-gray-700 text-sm mb-3">
              当点击后页面未跳转等复杂错误发生时，规则引擎将决策权移交给LLM：
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-medium text-gray-700 mb-2">LLM分析内容</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 当前页面截图和布局</li>
                  <li>• 操作历史和错误上下文</li>
                  <li>• 可能的原因分析</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">LLM决策建议</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 检查并关闭可能的弹窗</li>
                  <li>• 尝试替代操作路径</li>
                  <li>• 记录错误并优雅终止</li>
                </ul>
              </div>
            </div>
          </div>

          <div class="callout">
            <h4 class="font-semibold text-gray-800 mb-3">连续失败处理策略</h4>
            <ol class="text-sm text-gray-700 space-y-2">
              <li><strong>1. 错误记录</strong> - 记录失败步骤、错误信息和相关截图到测试报告</li>
              <li><strong>2. 清理执行</strong> - 调用end_test工具，释放设备资源</li>
              <li><strong>3. 状态转换</strong> - 切换到TestAbortedState，通知上层管理系统</li>
              <li><strong>4. 报告生成</strong> - 生成详细的错误分析报告，便于后续调试</li>
            </ol>
          </div>
        </section>

        <!-- Section 6: LLM Integration -->
        <section id="llm-integration" class="mb-16">
          <div class="section-header">
            <h2 class="serif">6. 与LLM的集成与交互</h2>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">6.1 LLM在系统中的角色</h3>

          <div class="architecture-grid">
            <div class="architecture-card">
              <div class="text-center mb-4">
                <div class="w-16 h-16 bg-gradient-to-br from-purple-500 to-purple-600 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-decision text-white text-xl"></i>
                </div>
              </div>
              <h4 class="text-xl font-semibold text-gray-800 mb-3 text-center">决策引擎</h4>
              <p class="text-gray-600 text-center text-sm">
                处理规则引擎无法解决的复杂异常，基于当前上下文进行智能决策，提供恢复建议
              </p>
            </div>

            <div class="architecture-card">
              <div class="text-center mb-4">
                <div class="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full flex items-center justify-center mx-auto">
                  <i class="fas fa-route text-white text-xl"></i>
                </div>
              </div>
              <h4 class="text-xl font-semibold text-gray-800 mb-3 text-center">规划器（可选）</h4>
              <p class="text-gray-600 text-center text-sm">
                根据高层次测试需求自动生成详细的测试计划JSON，降低测试用例编写门槛
              </p>
            </div>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">6.2 构建传递给LLM的Prompt</h3>

          <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <h4 class="text-lg font-semibold text-gray-800 mb-4">Prompt结构设计</h4>
            <div class="code-block">
              <pre>{
  &#34;system_instruction&#34;: &#34;你是一个智能的移动端App测试助手...&#34;,
  &#34;current_state&#34;: &#34;AddressSelectionPage&#34;,
  &#34;task_description&#34;: &#34;测试美团App的地址选择功能&#34;,
  &#34;blackboard_summary&#34;: {
    &#34;device_info&#34;: {&#34;udid&#34;: &#34;device123&#34;, &#34;platform&#34;: &#34;ios&#34;},
    &#34;current_page&#34;: &#34;地址选择页面，包含搜索框和城市列表&#34;,
    &#34;last_action&#34;: &#34;点击了搜索框&#34;,
    &#34;recent_logs&#34;: [&#34;find_element: 成功找到搜索框&#34;, &#34;tap: 点击搜索框&#34;]
  },
  &#34;error_information&#34;: {
    &#34;failed_tool&#34;: &#34;find_element&#34;,
    &#34;parameters&#34;: {&#34;element&#34;: &#34;北京市&#34;},
    &#34;error_result&#34;: &#34;element_not_found&#34;,
    &#34;error_message&#34;: &#34;未找到&#39;北京市&#39;元素&#34;
  },
  &#34;decision_request&#34;: &#34;请分析可能的原因，并建议下一步行动...&#34;
}</pre>
            </div>
          </div>

          <div class="highlight-box">
            <h4 class="font-semibold text-gray-800 mb-3">Prompt优化原则</h4>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-medium text-gray-700 mb-2">信息筛选</h5>
                <p class="text-sm text-gray-600">只包含与当前决策最相关的上下文，避免无关历史信息干扰</p>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">结构优化</h5>
                <p class="text-sm text-gray-600">使用清晰标题、列表和JSON格式，便于LLM解析和理解</p>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">长度控制</h5>
                <p class="text-sm text-gray-600">严格控制Prompt长度，避免超出上下文窗口限制</p>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">动态调整</h5>
                <p class="text-sm text-gray-600">根据错误类型动态调整内容，视觉问题强调截图，逻辑问题强调日志</p>
              </div>
            </div>
          </div>

          <h3 class="text-2xl font-semibold text-gray-800 mb-6">6.3 处理LLM的返回与决策</h3>

          <div class="space-y-4">
            <div class="bg-white rounded-lg shadow-md p-6">
              <h4 class="text-lg font-semibold text-gray-800 mb-4">决策解析策略</h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h5 class="font-medium text-gray-700 mb-2">结构化输出（推荐）</h5>
                  <div class="code-block text-xs">
                    <pre>{
  &#34;action&#34;: &#34;retry&#34;,
  &#34;reason&#34;: &#34;页面可能加载延迟&#34;,
  &#34;parameters&#34;: {
    &#34;wait_seconds&#34;: 5
  }
}</pre>
                  </div>
                </div>
                <div>
                  <h5 class="font-medium text-gray-700 mb-2">关键词匹配（备选）</h5>
                  <div class="code-block text-xs">
                    <pre># LLM返回文本：
&#34;建议等待5秒后重试，
因为页面可能还在加载中&#34;

# 提取的关键词：
action = &#34;retry&#34;
wait_seconds = 5</pre>
                  </div>
                </div>
              </div>
            </div>

            <div class="bg-gray-50 rounded-lg p-6">
              <h4 class="text-lg font-semibold text-gray-800 mb-4">决策到状态机操作的映射</h4>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div class="text-center">
                  <div class="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-redo text-green-600"></i>
                  </div>
                  <h5 class="font-medium text-gray-700 mb-1">重试 (retry)</h5>
                  <p class="text-xs text-gray-600">重置计数器，重新执行当前动作</p>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-step-forward text-yellow-600"></i>
                  </div>
                  <h5 class="font-medium text-gray-700 mb-1">跳过 (skip)</h5>
                  <p class="text-xs text-gray-600">忽略失败，转换到下一个状态</p>
                </div>
                <div class="text-center">
                  <div class="w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-2">
                    <i class="fas fa-stop text-red-600"></i>
                  </div>
                  <h5 class="font-medium text-gray-700 mb-1">终止 (abort)</h5>
                  <p class="text-xs text-gray-600">转换到TestAbortedState，执行清理</p>
                </div>
              </div>
            </div>
          </div>
        </section>

        <!-- Section 7: Implementation -->
        <section id="implementation" class="mb-16">
          <div class="section-header">
            <h2 class="serif">7. 阶段性测试与实现步骤</h2>
          </div>

          <div class="phase-timeline">
            <div class="phase-item">
              <div class="phase-number">1</div>
              <div class="phase-content">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">第一阶段：基础模块搭建与测试</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-medium text-green-800 mb-2">黑板记忆模块</h5>
                    <ul class="text-sm text-green-700 space-y-1">
                      <li>• 定义Blackboard类</li>
                      <li>• 实现核心API</li>
                      <li>• 编写单元测试</li>
                    </ul>
                  </div>
                  <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-medium text-blue-800 mb-2">API Server</h5>
                    <ul class="text-sm text-blue-700 space-y-1">
                      <li>• 搭建Flask应用</li>
                      <li>• 封装find_element工具</li>
                      <li>• 实现统一响应格式</li>
                    </ul>
                  </div>
                  <div class="bg-purple-50 p-4 rounded-lg">
                    <h5 class="font-medium text-purple-800 mb-2">状态机框架</h5>
                    <ul class="text-sm text-purple-700 space-y-1">
                      <li>• 定义核心StateMachine类</li>
                      <li>• 实现状态转换逻辑</li>
                      <li>• 测试简单流程</li>
                    </ul>
                  </div>
                </div>
                <div class="text-sm text-gray-600">
                  <i class="fas fa-check-circle text-green-500 mr-2"></i>
                  <strong>交付物：</strong>通过单元测试的blackboard.py、可运行的api_server.py、state_machine.py模块
                </div>
              </div>
            </div>

            <div class="phase-item">
              <div class="phase-number">2</div>
              <div class="phase-content">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">第二阶段：核心流程集成与测试</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-medium text-green-800 mb-2">模块集成</h5>
                    <ul class="text-sm text-green-700 space-y-1">
                      <li>• 状态机调用API Server</li>
                      <li>• 状态转换更新黑板</li>
                      <li>• 验证数据流通畅性</li>
                    </ul>
                  </div>
                  <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-medium text-blue-800 mb-2">配置转换</h5>
                    <ul class="text-sm text-blue-700 space-y-1">
                      <li>• 开发plan_converter.py</li>
                      <li>• 解析测试计划JSON</li>
                      <li>• 生成状态机配置</li>
                    </ul>
                  </div>
                </div>
                <div class="text-sm text-gray-600">
                  <i class="fas fa-check-circle text-green-500 mr-2"></i>
                  <strong>交付物：</strong>集成状态机、转换器脚本、单步执行验证报告
                </div>
              </div>
            </div>

            <div class="phase-item">
              <div class="phase-number">3</div>
              <div class="phase-content">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">第三阶段：自主性与异常处理实现</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-medium text-green-800 mb-2">规则引擎</h5>
                    <ul class="text-sm text-green-700 space-y-1">
                      <li>• 实现规则配置解析</li>
                      <li>• 定义重试规则</li>
                      <li>• 集成到状态机</li>
                    </ul>
                  </div>
                  <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-medium text-blue-800 mb-2">LLM集成</h5>
                    <ul class="text-sm text-blue-700 space-y-1">
                      <li>• 实现llm_client.py</li>
                      <li>• 开发prompt_builder.py</li>
                      <li>• 测试复杂错误决策</li>
                    </ul>
                  </div>
                  <div class="bg-purple-50 p-4 rounded-lg">
                    <h5 class="font-medium text-purple-800 mb-2">完整测试</h5>
                    <ul class="text-sm text-purple-700 space-y-1">
                      <li>• 执行美团测试计划</li>
                      <li>• 人为制造异常</li>
                      <li>• 验证异常处理能力</li>
                    </ul>
                  </div>
                </div>
                <div class="text-sm text-gray-600">
                  <i class="fas fa-check-circle text-green-500 mr-2"></i>
                  <strong>交付物：</strong>具备自主决策能力的Agent、完整的测试执行报告
                </div>
              </div>
            </div>

            <div class="phase-item">
              <div class="phase-number">4</div>
              <div class="phase-content">
                <h4 class="text-lg font-semibold text-gray-800 mb-3">第四阶段：优化与扩展</h4>
                <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div class="bg-green-50 p-4 rounded-lg">
                    <h5 class="font-medium text-green-800 mb-2">状态识别优化</h5>
                    <ul class="text-sm text-green-700 space-y-1">
                      <li>• 改进analysis_now_page</li>
                      <li>• 多特征页面识别</li>
                      <li>• 优化状态图复杂度</li>
                    </ul>
                  </div>
                  <div class="bg-blue-50 p-4 rounded-lg">
                    <h5 class="font-medium text-blue-800 mb-2">规则库扩展</h5>
                    <ul class="text-sm text-blue-700 space-y-1">
                      <li>• 收集分析异常案例</li>
                      <li>• 定义新处理规则</li>
                      <li>• 实现条件决策</li>
                    </ul>
                  </div>
                  <div class="bg-purple-50 p-4 rounded-lg">
                    <h5 class="font-medium text-purple-800 mb-2">性能测试</h5>
                    <ul class="text-sm text-purple-700 space-y-1">
                      <li>• 压力测试</li>
                      <li>• 稳定性测试</li>
                      <li>• 性能基准测试</li>
                    </ul>
                  </div>
                </div>
                <div class="text-sm text-gray-600">
                  <i class="fas fa-check-circle text-green-500 mr-2"></i>
                  <strong>交付物：</strong>优化后的Agent、扩展的规则库、性能测试报告
                </div>
              </div>
            </div>
          </div>

          <div class="callout mt-8">
            <h4 class="font-semibold text-gray-800 mb-3">实现路径总结</h4>
            <p class="text-gray-700 mb-4">
              通过这四个阶段的渐进式开发，可以从零开始构建一个功能完整的移动端App测试Agent。每个阶段都有明确的目标和可交付成果，便于阶段性验证和调整方向。
            </p>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h5 class="font-medium text-gray-700 mb-2">关键成功因素</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 模块化设计，便于独立开发和测试</li>
                  <li>• 每个阶段都有明确的验证标准</li>
                  <li>• 重视异常处理和自主决策能力</li>
                </ul>
              </div>
              <div>
                <h5 class="font-medium text-gray-700 mb-2">预期成果</h5>
                <ul class="text-sm text-gray-600 space-y-1">
                  <li>• 稳定可靠的长序列测试执行</li>
                  <li>• 智能的异常处理和恢复能力</li>
                  <li>• 可扩展的架构设计</li>
                </ul>
              </div>
            </div>
          </div>
        </section>

        <!-- Conclusion -->
        <section class="mb-16">
          <div class="bg-gradient-to-r from-green-50 to-blue-50 rounded-lg p-8">
            <h2 class="text-2xl font-semibold text-gray-800 mb-4 serif">总结与展望</h2>
            <p class="text-gray-700 mb-4">
              基于状态机的移动端App测试Agent设计方案，通过将复杂的测试流程分解为以App界面状态为核心的离散状态，有效解决了长序列任务中的计划遗忘问题。结合黑板记忆机制和分层决策系统，在保证测试计划精确执行的同时，赋予了Agent基于规则的自主决策能力。
            </p>
            <p class="text-gray-700 mb-6">
              该方案不仅适用于当前的美团App测试场景，其模块化设计和可扩展的架构也为未来的功能扩展奠定了基础。随着规则的不断丰富和LLM决策能力的提升，Agent将能够处理更加复杂的测试场景，成为移动端App质量保障的强大工具。
            </p>
            <div class="flex items-center justify-center space-x-8 text-sm text-gray-600">
              <span><i class="fas fa-calendar mr-2"></i>2025年6月</span>
              <span><i class="fas fa-user mr-2"></i>架构设计文档</span>
              <span><i class="fas fa-code mr-2"></i>技术方案</span>
            </div>
          </div>
        </section>
      </div>
    </div>

    <script>
        // Smooth scrolling for navigation links
        document.querySelectorAll('.toc a').forEach(link => {
            link.addEventListener('click', function(e) {
                e.preventDefault();
                const targetId = this.getAttribute('href');
                const targetElement = document.querySelector(targetId);
                if (targetElement) {
                    targetElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });

        // Mobile menu toggle (if needed)
        function toggleMobileMenu() {
            const toc = document.querySelector('.toc');
            toc.classList.toggle('open');
        }

        // Close mobile menu when clicking outside
        document.addEventListener('click', function(event) {
            const toc = document.querySelector('.toc');
            const menuButton = document.querySelector('.mobile-menu-button');
            
            // Check if click is outside TOC and not on menu button
            if (toc.classList.contains('open') && 
                !toc.contains(event.target) && 
                event.target !== menuButton) {
                toc.classList.remove('open');
            }
        });

        // Handle window resize to remove 'open' class on large screens
        function handleResize() {
            const toc = document.querySelector('.toc');
            if (window.innerWidth >= 1024) {
                toc.classList.remove('open');
            }
        }

        // Initial call to set the state on load
        handleResize();
        
        // Listen for resize events
        window.addEventListener('resize', handleResize);
    </script>
  

</body></html>