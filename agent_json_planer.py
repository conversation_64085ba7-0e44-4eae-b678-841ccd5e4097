#!/usr/bin/env python3
"""
自然语言到结构化执行计划的转换器
用于将用户的自然语言测试用例转换为可执行的结构化JSON计划
"""

import json
import re
import threading
import time
import os
from datetime import datetime
from typing import Dict, List, Any, Optional
from langchain_ollama import ChatOllama
from langchain_core.messages import HumanMessage, SystemMessage
from pydantic import BaseModel

# RAG系统导入
try:
    from rag_system.database_manager import DatabaseManager, create_database_manager
    from rag_system.plan_optimizer import PlanOptimizer, create_plan_optimizer
    from rag_system.data_models import DatabaseConfig
    RAG_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ RAG系统不可用: {e}")
    RAG_AVAILABLE = False

# 日志管理器导入
try:
    from log_manager import LogManager
    LOG_MANAGER_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 日志管理器不可用: {e}")
    LOG_MANAGER_AVAILABLE = False


class PlanStep(BaseModel):
    """执行步骤定义"""
    step_id: int
    action: str  # 动作类型：find_available_device, take_screenshot, tap_device, slide_device, check_page_display, input_text_smart, wait_seconds等
    description: str  # 步骤描述
    parameters: Dict[str, Any]  # 具体参数
    parameter_types: Dict[str, str] = {}  # 参数类型标识：static(固定值) 或 dynamic(动态值)
    parameter_sources: Dict[str, str] = {}  # 动态参数的来源说明
    expected_result: str  # 预期结果
    

class ExecutionPlan(BaseModel):
    """完整的执行计划"""
    plan_id: str
    original_request: str  # 原始自然语言请求
    summary: str  # 计划摘要
    platform: str  # 平台：ios 或 android
    total_steps: int
    steps: List[PlanStep]


class AgentJsonPlaner:
    """Agent JSON 计划生成器"""
    
    # 类级别的线程锁，确保只有一个请求能同时使用模型
    _model_lock = threading.Lock()
    # 平台轮询状态文件路径
    _platform_state_file = ".platform_state.json"
    _platform_lock = threading.Lock()
    
    def __init__(self, 
                 model_name: str = "deepseek-r1:32b",
                 base_url: str = "127.0.0.1:11434",
                 temperature: float = 0.1,
                 enable_rag: bool = True,
                 enable_logs: bool = True):
        """初始化计划生成器"""
        self.model_name = model_name
        self.base_url = base_url
        self.temperature = temperature
        self.enable_rag = enable_rag and RAG_AVAILABLE
        self.enable_logs = enable_logs and LOG_MANAGER_AVAILABLE
        
        # 初始化日志管理器
        self.log_manager = None
        if self.enable_logs:
            try:
                # 创建专门的JSON计划日志管理器，使用 log/json_plan 目录
                self.log_manager = LogManager(log_dir="log/json_plan")
                print("✅ 日志管理器初始化成功")
            except Exception as e:
                print(f"⚠️ 日志管理器初始化失败: {e}")
                self.enable_logs = False
        
        # 初始化LLM
        self.llm = ChatOllama(
            model=model_name,
            base_url=base_url,
            temperature=temperature,
            client_kwargs={"trust_env": False}
        )
        
        # 工具定义映射
        self.tool_mapping = self._create_tool_mapping()
        
        # 初始化RAG系统
        self.db_manager = None
        self.plan_optimizer = None
        if self.enable_rag:
            try:
                print("🔧 初始化RAG系统...")
                self.db_manager = create_database_manager()
                self.plan_optimizer = create_plan_optimizer(self.db_manager)
                print("✅ RAG系统初始化成功")
            except Exception as e:
                print(f"⚠️ RAG系统初始化失败: {e}")
                self.enable_rag = False
    
    def _detect_platform_from_text(self, text: str) -> Optional[str]:
        """从自然语言中检测平台"""
        text_lower = text.lower()
        
        # iOS关键词
        ios_keywords = ['ios', 'iphone', 'ipad', '苹果', 'apple', 'ios设备']
        # Android关键词  
        android_keywords = ['android', '安卓', 'google', 'android设备']
        
        ios_count = sum(1 for keyword in ios_keywords if keyword in text_lower)
        android_count = sum(1 for keyword in android_keywords if keyword in text_lower)
        
        if ios_count > android_count:
            return "ios"
        elif android_count > ios_count:
            return "android"
        else:
            return None  # 无法确定
    
    def _load_platform_state(self) -> str:
        """从文件中加载上次使用的平台状态"""
        try:
            if os.path.exists(self._platform_state_file):
                with open(self._platform_state_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    return data.get("last_platform", "android")
            else:
                # 文件不存在，返回默认值
                return "android"
        except Exception as e:
            print(f"⚠️ 读取平台状态文件失败: {e}")
            return "android"
    
    def _save_platform_state(self, platform: str) -> None:
        """保存当前平台状态到文件"""
        try:
            data = {"last_platform": platform}
            with open(self._platform_state_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"⚠️ 保存平台状态文件失败: {e}")
    
    def _get_next_platform(self) -> str:
        """获取下一个平台（轮询方式）"""
        with self._platform_lock:
            # 读取上次使用的平台
            last_platform = self._load_platform_state()
            
            # 轮询到下一个平台
            if last_platform == "ios":
                next_platform = "android"
            else:
                next_platform = "ios"
            
            # 保存新的平台状态
            self._save_platform_state(next_platform)
            
            return next_platform
    
    def determine_platform(self, natural_language: str, specified_platform: Optional[str] = None) -> str:
        """确定目标平台"""
        # 1. 如果明确指定了平台，直接使用
        if specified_platform and specified_platform.lower() in ["ios", "android"]:
            platform = specified_platform.lower()
            print(f"🎯 使用指定平台: {platform.upper()}")
            return platform
        
        # 2. 尝试从自然语言中检测平台
        detected_platform = self._detect_platform_from_text(natural_language)
        if detected_platform:
            print(f"🔍 从文本中检测到平台: {detected_platform.upper()}")
            return detected_platform
        
        # 3. 使用轮询机制
        next_platform = self._get_next_platform()
        print(f"🔄 轮询选择平台: {next_platform.upper()}")
        return next_platform
        
    def _create_tool_mapping(self) -> Dict[str, Dict]:
        """创建工具映射表"""
        return {
            "find_available_device": {
                "description": "查找可用的测试设备",
                "required_params": ["platform"],
                "optional_params": [],
                "example": {"platform": "ios"}
            },
            "ocr_text_validation": {
                "description": "通过OCR搜索包含指定文本的元素信息，返回元素位置和详细信息",
                "required_params": ["udid", "target_text"],
                "optional_params": [],
                "example": {"udid": "{device_udid}", "target_text": "外卖"}
            },
            "start_device_test": {
                "description": "开始设备测试会话",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "take_screenshot": {
                "description": "对设备进行截图",
                "required_params": ["udid"],
                "optional_params": ["description"],
                "example": {"udid": "{device_udid}", "description": "首页截图"}
            },
            "tap_device": {
                "description": "点击屏幕指定位置",
                "required_params": ["udid", "x", "y"],
                "optional_params": ["description"],
                "example": {"udid": "{device_udid}", "x": 100, "y": 200, "description": "点击搜索框"}
            },
            "slide_device": {
                "description": "滑动屏幕",
                "required_params": ["udid", "from_x", "from_y", "to_x", "to_y"],
                "optional_params": ["duration", "description"],
                "example": {"udid": "{device_udid}", "from_x": 0.5, "from_y": 0.8, "to_x": 0.5, "to_y": 0.2, "duration": 0.5, "description": "向上滑动"}
            },
            "input_text_smart": {
                "description": "在输入框中输入文字",
                "required_params": ["udid", "text"],
                "optional_params": ["element_index"],
                "example": {"udid": "{device_udid}", "text": "北京", "element_index": 0}
            },
            "wait_seconds": {
                "description": "等待指定秒数",
                "required_params": ["seconds"],
                "optional_params": [],
                "example": {"seconds": 3}
            },
            "check_page_display": {
                "description": "检查页面是否存在UI bug或显示异常",
                "required_params": ["udid"],
                "optional_params": ["scene_desc"],
                "example": {"udid": "{device_udid}", "scene_desc": "检查首页UI bug"}
            },
            "find_element_on_page": {
                "description": "从页面layout信息和截图中查找指定元素的位置和详细信息",
                "required_params": ["udid", "element"],
                "optional_params": ["scene_desc"],
                "example": {"udid": "{device_udid}", "element": "搜索框", "scene_desc": "从首页布局中定位搜索框"}
            },
            "ocr_text_only": {
                "description": "返回当前页面的OCR结果，可用于判断当前页面的展示内容",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "restart_application": {
                "description": "重启应用",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "end_device_test": {
                "description": "结束设备测试会话",
                "required_params": ["udid"],
                "optional_params": [],
                "example": {"udid": "{device_udid}"}
            },
            "analyze_meituan_page": {
                "description": "分析美团app界面截图，识别当前界面特征和界面类型",
                "required_params": ["udid"],
                "optional_params": ["action_description", "model"],
                "example": {"udid": "{device_udid}", "action_description": "点击搜索框后", "model": "qwen2.5vl:7b"}
            }
        }
    
    def _create_system_prompt(self) -> str:
        """创建系统提示词"""
        tools_info = []
        for action, info in self.tool_mapping.items():
            tools_info.append(f"- {action}: {info['description']}")
            tools_info.append(f"  必需参数: {info['required_params']}")
            tools_info.append(f"  可选参数: {info['optional_params']}")
            tools_info.append(f"  示例: {json.dumps(info['example'], ensure_ascii=False)}")
            tools_info.append("")
        
        tools_text = "\n".join(tools_info)
        
        return f"""你是一个专业的移动应用测试计划生成器。你的任务是将用户提供的自然语言测试用例转换为结构化的JSON执行计划。

**可用的操作类型：**
{tools_text}

**测试流程规则与业务场景说明：**

**1. 测试会话管理：**
- 每个测试计划必须以 find_available_device 开始，以 end_device_test 结束
- 为什么必须以 find_available_device 开始？因为我们需要指定一个具体的测试设备
- find_available_device 之后必须立即调用 start_device_test，因为需要对设备进行初始化和建立测试会话
- 为什么最后必须以 end_device_test 结束？因为需要正确关闭测试会话，释放设备资源

**2. 坐标系统说明：**
- 点击坐标使用具体像素值（如 x: 100, y: 200），通常是根据之前工具中获取到的信息来
- 滑动坐标使用0-1之间的比例值，代表屏幕的相对位置，下面举出一些例子
  - 下滑屏幕：从 (0.5, 0.1) 滑动到 (0.5, 0.9)
  - 上滑屏幕：从 (0.5, 0.9) 滑动到 (0.5, 0.1)
  - 右滑屏幕：从 (0.1, 0.5) 滑动到 (0.9, 0.5)
  - 左滑屏幕：从 (0.9, 0.5) 滑动到 (0.1, 0.5)

**3. 页面检查和元素查找工具区别：**
- check_page_display: 检查当前页面是否有展示异常，只给出页面展示是否正常的结论，不提供元素位置信息
- find_element_on_page: 结合当前页面的layout信息和截图信息来查找指定元素，会返回元素的坐标、大小等详细信息

**4. 文本识别和元素查找策略：**
- ocr_text_only: 自动调用截图并返回当前页面OCR结果信息，适用于指令中有明确文本信息的情况
  - 如"点击'搜索'按钮" → 使用ocr_text_only工具
- find_element_on_page: 适用于查找功能性元素的情况
  - 如"查找搜索框" → 使用find_element_on_page工具

**5. 文本输入流程：**
- input_text_smart工具说明：一般页面的可输入元素只有一个，但通常需要点击后才能变为可输入状态
- 输入文本的完整流程：
  1. find_element_on_page（查找输入框）
  2. tap_device（点击输入框激活）
  3. input_text_smart（输入文本内容）
- 示例：指令"在搜索框中输入'xxx'" → 需要三个步骤：find_element_on_page → tap_device → input_text_smart

**6. 页面状态分析工具使用规则：**
- analyze_meituan_page工具用途：分析当前美团app界面的特征，识别处于哪个界面
- 使用时机：在执行会改变页面状态的操作后，需要记录页面变化
- 需要使用analyze_meituan_page的操作：
  - tap_device（点击后页面可能跳转）
  - slide_device（滑动后页面内容可能改变）
  - restart_application（重启后返回首页）
  - input_text_smart（输入后页面可能显示搜索结果）
- 使用流程：页面操作 → wait_seconds → analyze_meituan_page → 继续下一步
- 参数说明：
  - udid：设备UDID（动态参数）
  - action_description：描述进入当前页面的动作，格式如"点击搜索框后"、"滑动页面后"等（静态参数）

**重要规则：**
1. 严格按照上述测试会话管理流程
2. 根据坐标系统规则正确设置滑动参数
3. 根据业务场景选择合适的页面检查工具
4. 根据文本特征选择合适的元素查找策略
5. 文本输入必须遵循三步流程
6. 根据用户描述智能推断需要的操作步骤
7. 对于"校验"、"检查"类的需求，根据具体情况选择 check_page_display 或 find_element_on_page
8. 对于"点击"操作，需要先用合适的查找工具找到元素位置，然后用 tap_device 点击
9. **⚠️ 关键：页面操作后必须等待** - 每当执行可能改变页面状态的操作（如tap_device、slide_device、input_text_smart、restart_application）后，都必须添加wait_seconds步骤（建议1-3秒），确保页面完全加载和响应后再进行下一步操作
10. **📱 页面状态记录规则** - 在执行会改变app页面的动作（如tap_device、slide_device、restart_application等）后，必须调用analyze_meituan_page工具来记录当前页面的特征和界面类型，用于记录操作后的页面状态变化

**输出格式：**
请严格按照以下JSON格式输出，不要包含任何其他文字：

{{
  "plan_id": "plan_随机ID",
  "original_request": "用户的原始请求",
  "summary": "计划摘要（一句话描述）",
  "platform": "ios",
  "total_steps": 总步骤数,
  "steps": [
    {{
      "step_id": 1,
      "action": "操作类型",
      "description": "步骤描述", 
      "parameters": {{参数字典}},
      "parameter_types": {{
        "参数名": "static/dynamic",
        "参数名": "static/dynamic"
      }},
      "parameter_sources": {{
        "动态参数名": "来源说明"
      }},
      "expected_result": "预期结果"
    }}
  ]
}}

**参数类型说明：**
- **static（固定值）**: 用户明确指定的固定内容，如输入文字"北京"、等待时间3秒等
- **dynamic（动态值）**: 需要在执行过程中获取的值，如设备udid、元素坐标等

**参数来源说明（仅针对动态参数）：**
- "来自find_available_device结果": 设备udid从查找设备步骤获取
- "来自find_element_on_page结果": 元素坐标从查找元素步骤获取
- "来自start_device_test结果": 测试会话信息从启动测试步骤获取

**示例转换：**
用户输入："在iOS设备上打开美团首页，点击搜索框，输入'火锅'，然后截图"

示例步骤（重点展示参数类型）：
```json
{{
  "step_id": 1,
  "action": "find_available_device",
  "description": "查找可用的iOS设备",
  "parameters": {{"platform": "ios"}},
  "parameter_types": {{"platform": "static"}},
  "parameter_sources": {{}},
  "expected_result": "成功找到iOS设备"
}},
{{
  "step_id": 7,
  "action": "input_text_smart",
  "description": "在搜索框中输入'火锅'",
  "parameters": {{"udid": "{{device_udid}}", "text": "火锅"}},
  "parameter_types": {{"udid": "dynamic", "text": "static"}},
  "parameter_sources": {{"udid": "来自find_available_device结果"}},
  "expected_result": "成功输入'火锅'"
}},
{{
  "step_id": 5,
  "action": "tap_device",
  "description": "点击搜索框",
  "parameters": {{"udid": "{{device_udid}}", "x": "{{element_x}}", "y": "{{element_y}}"}},
  "parameter_types": {{"udid": "dynamic", "x": "dynamic", "y": "dynamic"}},
  "parameter_sources": {{"udid": "来自find_available_device结果", "x": "来自find_element_on_page结果", "y": "来自find_element_on_page结果"}},
  "expected_result": "成功点击搜索框"
}}
```

**关键区别：**
- "火锅"是static（用户指定的固定输入内容）
- "ios"是static（用户指定的平台）
- device_udid是dynamic（需要从find_available_device步骤获取）
- element_x/y是dynamic（需要从find_element_on_page步骤获取）

请严格按照JSON格式输出，确保可以被Python的json.loads()正确解析。"""

    def generate_plan(self, natural_language_request: str, platform: Optional[str] = None, debug_mode: bool = False, use_original_plan: bool = False) -> Dict[str, Any]:
        """将自然语言请求转换为结构化执行计划"""
        print(f"🔒 请求获取模型锁，当前线程: {threading.current_thread().name}")
        
        # 使用线程锁确保并发安全
        with self._model_lock:
            try:
                print(f"✅ 已获取模型锁，开始处理请求: {threading.current_thread().name}")
                print(f"📝 处理自然语言请求: {natural_language_request}")
                
                # 确定目标平台
                target_platform = self.determine_platform(natural_language_request, platform)
                print(f"📱 确定目标平台: {target_platform.upper()}")
                
                start_time = time.time()
                
                # 构建消息 - 增强版本，包含历史经验
                system_prompt = self._create_system_prompt()
                historical_context = ""  # 初始化历史上下文
                
                # 如果启用RAG，搜索相关历史经验并注入到prompt中
                if self.enable_rag and self.plan_optimizer:
                    historical_context = self._get_historical_context(natural_language_request, target_platform)
                    if historical_context:
                        # 将历史经验注入到系统提示词的关键位置（在规则后面，示例前面）
                        enhanced_context = f"\n\n🔥 **重要：基于历史执行记录的经验指导** 🔥\n{historical_context}\n\n请在生成计划时务必考虑上述历史经验，特别是成功案例的模式和需要避免的失败情况。"
                        
                        # 插入到系统提示词的合适位置（在"输出格式"之前）
                        insertion_point = system_prompt.find("**输出格式：**")
                        if insertion_point != -1:
                            system_prompt = system_prompt[:insertion_point] + enhanced_context + "\n\n" + system_prompt[insertion_point:]
                        else:
                            # 如果找不到插入点，就添加到末尾
                            system_prompt += enhanced_context
                        
                        print(f"📚 注入历史经验上下文，长度: {len(historical_context)} 字符")
                        print(f"📄 历史经验内容：")
                        print(historical_context)
                        print("="*50)
                
                user_prompt = f"请将以下测试用例转换为结构化执行计划：\n\n目标平台: {target_platform.upper()}\n测试用例: {natural_language_request}"
                
                messages = [
                    SystemMessage(content=system_prompt),
                    HumanMessage(content=user_prompt)
                ]
                
                print(f"🤖 调用模型 {self.model_name} 进行计划生成...")
                print(f"📋 系统提示词长度: {len(system_prompt)} 字符")
                print(f"📝 用户提示词长度: {len(user_prompt)} 字符")
                
                # Debug模式：输出完整的系统提示词
                if debug_mode:
                    print(f"\n{'='*80}")
                    print("📄 完整系统提示词内容：")
                    print("="*80)
                    print(system_prompt)
                    print("="*80)
                    print("📄 用户提示词内容：")
                    print("="*80)
                    print(user_prompt)
                    print("="*80)
                    print()
                else:
                    print(f"📄 完整用户提示词:")
                    print(user_prompt)
                
                # 调用LLM
                response = self.llm.invoke(messages)
                response_text = response.content.strip()
                
                duration = time.time() - start_time
                print(f"⏱️  模型调用完成，耗时: {duration:.2f}秒")
                print(f"🔍 模型原始响应长度: {len(response_text)} 字符")
                print(f"📄 模型完整响应:")
                print(response_text)
                
                # 尝试提取JSON - 多种策略
                json_data = self._extract_json_from_response(response_text)
                
                if json_data:
                    # 确保平台信息正确
                    json_data["platform"] = target_platform
                    print(f"✅ JSON解析成功，生成了 {json_data.get('total_steps', 0)} 个步骤")
                    
                    # 使用RAG系统优化计划
                    final_plan = json_data
                    optimization_result = None
                    
                    # 如果指定使用原始计划，跳过RAG优化
                    if use_original_plan:
                        print("🔄 使用原始计划，跳过RAG优化（因为RAG数据可能不是最新的）")
                        final_plan = json_data
                    elif self.enable_rag and self.plan_optimizer:
                        try:
                            print("🧠 使用RAG系统优化计划...")
                            opt_start_time = time.time()
                            optimization_result = self.plan_optimizer.optimize_plan(
                                json_data, natural_language_request
                            )
                            opt_duration = time.time() - opt_start_time
                            
                            if optimization_result.get("status") == "success":
                                final_plan = optimization_result["optimized_plan"]
                                detected_patterns = optimization_result.get("detected_patterns", [])
                                improvements = optimization_result.get("improvements", [])
                                
                                print(f"🔧 RAG优化完成，耗时: {opt_duration:.2f}秒")
                                print(f"📊 检测到 {len(detected_patterns)} 个错误模式")
                                print(f"💡 应用了 {len(improvements)} 个改进建议")
                                print(f"📈 优化后步骤数: {final_plan.get('total_steps', 0)}")
                                
                                # 打印检测到的模式
                                for pattern in detected_patterns:
                                    print(f"   - {pattern.get('pattern_name', 'Unknown')}: {pattern.get('description', '')}")
                            else:
                                print(f"⚠️ RAG优化失败: {optimization_result.get('error', 'Unknown error')}")
                        except Exception as e:
                            print(f"⚠️ RAG优化异常: {e}")
                    
                    return {
                        "status": "success",
                        "plan": final_plan,
                        "original_plan": json_data if optimization_result else None,
                        "optimization_result": optimization_result,
                        "raw_response": response_text,
                        "processing_time": duration,
                        "detected_platform": target_platform,
                        "rag_enabled": self.enable_rag,
                        "system_prompt": system_prompt if debug_mode else None,
                        "user_prompt": user_prompt if debug_mode else None,
                        "historical_context": historical_context if debug_mode else None
                    }
                else:
                    print("❌ 未找到有效的JSON格式")
                    print(f"原始响应: {response_text}")
                    return {
                        "status": "error", 
                        "error": "未找到有效的JSON格式",
                        "raw_response": response_text,
                        "processing_time": duration,
                        "system_prompt": system_prompt if debug_mode else None,
                        "user_prompt": user_prompt if debug_mode else None,
                        "historical_context": historical_context if debug_mode else None
                    }
                    
            except Exception as e:
                print(f"❌ 生成计划时发生异常: {str(e)}")
                return {
                    "status": "error",
                    "error": f"生成计划时发生异常: {str(e)}",
                    "raw_response": "",
                    "processing_time": 0,
                    "system_prompt": locals().get('system_prompt') if debug_mode else None,
                    "user_prompt": locals().get('user_prompt') if debug_mode else None,
                    "historical_context": locals().get('historical_context') if debug_mode else None
                }
            finally:
                print(f"🔓 释放模型锁: {threading.current_thread().name}")
    
    def _extract_json_from_response(self, response_text: str) -> Optional[Dict[str, Any]]:
        """从响应中提取JSON，使用多种策略"""
        # 策略0: 清理思维链标签
        cleaned_response = response_text
        # 移除 <think>...</think> 标签
        think_pattern = r'<think>.*?</think>'
        cleaned_response = re.sub(think_pattern, '', cleaned_response, flags=re.DOTALL)
        
        # 策略1: 查找代码块中的JSON（优先）
        code_block_pattern = r'```(?:json)?\s*(\{.*?\})\s*```'
        matches = re.findall(code_block_pattern, cleaned_response, re.DOTALL | re.IGNORECASE)
        for match in matches:
            try:
                cleaned_json = self._clean_json_string(match)
                data = json.loads(cleaned_json)
                if self._validate_plan_structure(data):
                    return data
            except json.JSONDecodeError:
                continue
        
        # 策略2: 查找完整的JSON对象
        json_patterns = [
            r'\{[^{}]*(?:\{[^{}]*\}[^{}]*)*\}',  # 简单嵌套
            r'\{.*?\}',  # 最小匹配
            r'\{.*\}',   # 最大匹配
        ]
        
        for pattern in json_patterns:
            matches = re.findall(pattern, cleaned_response, re.DOTALL)
            for match in matches:
                try:
                    # 清理可能的格式问题
                    cleaned_json = self._clean_json_string(match)
                    data = json.loads(cleaned_json)
                    
                    # 验证是否包含必需字段
                    if self._validate_plan_structure(data):
                        return data
                except json.JSONDecodeError:
                    continue
        
        return None
    
    def _clean_json_string(self, json_str: str) -> str:
        """清理JSON字符串中的常见问题"""
        # 移除前后空白
        json_str = json_str.strip()
        
        # 修复常见的格式问题
        json_str = re.sub(r',\s*}', '}', json_str)  # 移除尾随逗号
        json_str = re.sub(r',\s*]', ']', json_str)  # 移除数组尾随逗号
        
        # 尝试解析并重新格式化JSON
        try:
            # 先尝试直接解析
            import json
            data = json.loads(json_str)
            
            # 修复 total_steps 计数问题
            if 'steps' in data and 'total_steps' in data:
                actual_steps = len(data['steps'])
                if data['total_steps'] != actual_steps:
                    data['total_steps'] = actual_steps
            
            # 重新序列化
            return json.dumps(data, ensure_ascii=False, separators=(',', ':'))
        except json.JSONDecodeError:
            # 如果解析失败，尝试修复常见问题
            # 确保字符串值被正确引用（但要小心已经引用的值）
            json_str = re.sub(r':\s*([^",\[\]{}]+)\s*([,}])', r': "\1"\2', json_str)
            
            return json_str
    
    def _validate_plan_structure(self, data: Dict[str, Any]) -> bool:
        """验证计划结构是否有效"""
        required_fields = ['plan_id', 'steps', 'total_steps']
        
        for field in required_fields:
            if field not in data:
                return False
        
        if not isinstance(data['steps'], list):
            return False
            
        if len(data['steps']) == 0:
            return False
            
        # 验证步骤结构
        for step in data['steps']:
            if not isinstance(step, dict):
                return False
            step_required = ['step_id', 'action', 'description', 'parameters']
            for field in step_required:
                if field not in step:
                    return False
        
        return True
    
    def convert_plan_to_agent_instruction(self, plan: Dict[str, Any]) -> str:
        """将结构化计划转换为Agent可以理解的指令"""
        try:
            if not plan or "steps" not in plan:
                return "错误：计划格式无效"
            
            instructions = []
            instructions.append(f"请按照以下步骤执行测试计划：")
            instructions.append(f"计划摘要：{plan.get('summary', '未知')}")
            instructions.append("")
            
            for i, step in enumerate(plan["steps"], 1):
                step_desc = step.get("description", "")
                expected = step.get("expected_result", "")
                
                instruction = f"{i}. {step_desc}"
                if expected:
                    instruction += f"，预期结果：{expected}"
                instructions.append(instruction)
            
            instructions.append("")
            instructions.append("请严格按照上述步骤顺序执行，每完成一个步骤后再进行下一步。")
            
            return "\n".join(instructions)
            
        except Exception as e:
            return f"转换指令时发生错误: {str(e)}"
    
    def convert_plan_to_detailed_instruction(self, plan: Dict[str, Any]) -> str:
        """将结构化计划转换为包含具体工具调用的详细指令"""
        try:
            if not plan or "steps" not in plan:
                return "错误：计划格式无效"
            
            instructions = []
            instructions.append("=" * 80)
            instructions.append("🎯 测试计划摘要")
            instructions.append("=" * 80)
            instructions.append(f"📝 计划描述: {plan.get('summary', '未知')}")
            instructions.append(f"📱 目标平台: {plan.get('platform', 'unknown').upper()}")
            instructions.append(f"🔢 总步骤数: {plan.get('total_steps', len(plan['steps']))}")
            instructions.append(f"📋 计划ID: {plan.get('plan_id', 'unknown')}")
            instructions.append("")
            instructions.append("=" * 80)
            instructions.append("📋 详细执行步骤")
            instructions.append("=" * 80)
            
            for i, step in enumerate(plan.get("steps", [])):
                step_id = step.get("step_id", 0)
                action = step.get("action", "")
                description = step.get("description", "")
                parameters = step.get("parameters", {})
                parameter_types = step.get("parameter_types", {})
                parameter_sources = step.get("parameter_sources", {})
                expected = step.get("expected_result", "")
                
                # 直接使用action作为工具名（已经是原始工具名）
                tool_name = action
                
                instructions.append(f"步骤 {step_id}: {description}")
                instructions.append(f"   🔧 工具调用: {tool_name}")
                instructions.append(f"   📝 参数: {json.dumps(parameters, ensure_ascii=False)}")
                
                # 添加参数类型分析
                if parameter_types:
                    static_params = {k: v for k, v in parameters.items() if parameter_types.get(k) == "static"}
                    dynamic_params = {k: v for k, v in parameters.items() if parameter_types.get(k) == "dynamic"}
                    
                    if static_params:
                        instructions.append(f"   🔹 固定值参数: {json.dumps(static_params, ensure_ascii=False)}")
                    if dynamic_params:
                        instructions.append(f"   🔸 动态值参数: {json.dumps(dynamic_params, ensure_ascii=False)}")
                        
                        # 显示动态参数的来源
                        for param_name in dynamic_params:
                            source = parameter_sources.get(param_name, "未知来源")
                            instructions.append(f"     └─ {param_name}: {source}")
                
                instructions.append(f"   ✅ 预期结果: {expected}")
                
                # 增强步骤间的关联性说明
                if i < len(plan.get("steps", [])) - 1:
                    next_step = plan.get("steps", [])[i + 1]
                    next_action = next_step.get("action", "")
                    
                    # 特定的步骤关联提示
                    if action == "find_element_on_page" and next_action == "tap_device":
                        instructions.append(f"   🔗 **重要**: 下一步将点击本步骤找到的元素位置")
                    elif action == "tap_device" and next_action == "check_page_display":
                        instructions.append(f"   🔗 **关联**: 点击后需要验证页面变化结果")
                    elif action == "input_text_smart" and next_action == "find_element_on_page":
                        instructions.append(f"   🔗 **关联**: 输入文本后将查找相关的搜索结果")
                    elif action in ["find_element_on_page", "tap_device"] and "udid" in parameters:
                        instructions.append(f"   🔗 **设备关联**: 在设备 {parameters.get('udid', '{device_udid}')} 上执行")
                
                instructions.append("")
            
            instructions.append("=" * 80)
            instructions.append("⚠️  重要执行要求")
            instructions.append("=" * 80)
            instructions.append("• **步骤顺序**: 严格按照步骤顺序执行，一次执行一个工具")
            instructions.append("• **等待结果**: 每个工具执行完成后，等待结果再进行下一步")
            instructions.append("• **参数类型区分**: ")
            instructions.append("  - 🔹 固定值参数: 直接使用，不需要替换")
            instructions.append("  - 🔸 动态值参数: 必须从指定的步骤结果中获取实际值")
            instructions.append("• **动态参数获取**: 根据parameter_sources指示，从对应步骤结果中提取实际值")
            instructions.append("• **步骤关联**: 特别注意find_element_on_page找到的元素位置，在后续tap_device中使用")
            instructions.append("• **坐标使用**: tap_device操作时，优先使用find_element_on_page返回的坐标信息")
            instructions.append("• **设备一致性**: 确保所有操作都在同一设备(udid)上执行")
            instructions.append("• **失败重试**: 如果某个步骤失败，尝试1-2次后再继续")
            instructions.append("• **完成记录**: 完成所有步骤后调用record_agent_summary记录总结")
            instructions.append("=" * 80)
            
            return "\n".join(instructions)
            
        except Exception as e:
            return f"转换详细指令时发生错误: {str(e)}"
    
    def save_execution_feedback(self, execution_data: Dict[str, Any], 
                               optimization_result: Optional[Dict] = None) -> bool:
        """保存执行反馈到RAG系统"""
        if not self.enable_rag or not self.db_manager:
            print("⚠️ RAG系统未启用，无法保存执行反馈")
            return False
        
        try:
            from rag_system.data_models import TaskExecution, ExecutionStatus, PlatformType, TaskType
            from datetime import datetime
            
            # 转换执行数据为TaskExecution对象
            execution = TaskExecution(
                execution_id="",  # 自动生成
                round_id=execution_data.get("round_id", f"round_{int(time.time())}"),
                round_number=execution_data.get("round_number", 0),
                original_instruction=execution_data.get("original_instruction", ""),
                structured_plan=execution_data.get("structured_plan"),
                task_type=TaskType(execution_data.get("task_type", "smart")),
                platform=PlatformType(execution_data.get("platform", "unknown")),
                mis_id=execution_data.get("mis_id", "agent_json_planer"),
                device_udid=execution_data.get("device_udid"),
                device_name=execution_data.get("device_name"),
                execution_status=ExecutionStatus(execution_data.get("execution_status", "completed")),
                final_result=execution_data.get("final_result"),
                execution_error=execution_data.get("execution_error"),
                created_at=datetime.fromisoformat(execution_data["created_at"]) if execution_data.get("created_at") else datetime.now(),
                started_at=datetime.fromisoformat(execution_data["started_at"]) if execution_data.get("started_at") else None,
                ended_at=datetime.fromisoformat(execution_data["ended_at"]) if execution_data.get("ended_at") else None,
                total_duration=execution_data.get("total_duration"),
                total_steps=execution_data.get("total_steps", 0),
                successful_steps=execution_data.get("successful_steps", 0),
                failed_steps=execution_data.get("failed_steps", 0),
                log_folder_path=execution_data.get("log_folder_path", ""),
                metadata=execution_data.get("metadata", {})
            )
            
            # 保存到RAG系统
            if self.plan_optimizer:
                return self.plan_optimizer.save_execution_feedback(execution, optimization_result)
            else:
                return self.db_manager.save_task_execution(execution)
                
        except Exception as e:
            print(f"⚠️ 保存执行反馈失败: {e}")
            return False
    
    def _get_historical_context(self, instruction: str, platform: str) -> str:
        """获取相关历史经验上下文"""
        try:
            similar_executions = self.plan_optimizer._search_similar_executions(instruction, platform)
            if not similar_executions:
                return ""
            
            context_parts = []
            context_parts.append("📊 从历史执行记录中总结的关键经验如下，请在生成计划时严格参考：")
            
            # 添加成功案例模式
            successful_executions = [ex for ex in similar_executions[:3] if ex.get("execution_status") == "completed"]
            if successful_executions:
                context_parts.append("\n✅ **成功的工具调用序列模式（请优先采用）：**")
                for i, ex in enumerate(successful_executions, 1):
                    if ex.get("structured_plan") and isinstance(ex["structured_plan"], dict):
                        steps = ex["structured_plan"].get("steps", [])
                        tool_sequence = [step.get("action", "") for step in steps]
                        similarity = ex.get("similarity_score", 0.0)
                        context_parts.append(f"  模式{i} (相似度{similarity:.3f}): {' -> '.join(tool_sequence)}")
                        
                        # 添加具体的成功经验描述
                        total_steps = len(steps)
                        context_parts.append(f"    ├─ 总步骤数: {total_steps}")
                        context_parts.append(f"    ├─ 执行状态: ✅ 成功完成")
                        context_parts.append(f"    └─ 关键特征: 包含完整的测试流程（find_available_device -> start_device_test -> ... -> end_device_test）")
            
            # 添加需要避免的失败模式
            failed_executions = [ex for ex in similar_executions[:2] if ex.get("execution_status") in ["failed", "timeout"]]
            if failed_executions:
                context_parts.append("\n⚠️ **需要避免的失败模式：**")
                for i, ex in enumerate(failed_executions, 1):
                    error = ex.get("execution_error", "") or "执行超时或异常终止"
                    status = ex.get("execution_status", "unknown")
                    context_parts.append(f"  失败案例{i} (状态: {status}):")
                    context_parts.append(f"    └─ 失败原因: {error[:200]}...")
            
            # 添加关键建议
            if successful_executions or failed_executions:
                context_parts.append("\n🎯 **核心指导原则（必须遵循）：**")
                context_parts.append("  1. 测试流程完整性: 必须以find_available_device开始，以end_device_test结束")
                context_parts.append("  2. 文本输入三步法: find_element_on_page -> tap_device(激活) -> input_text_smart")
                context_parts.append("  3. 页面加载等待: 重要操作后使用wait_seconds工具等待页面响应")
                context_parts.append("  4. 元素定位优先: 点击前先用find_element_on_page确定元素位置")
                
            # 添加优先级指导
            context_parts.append("\n🚨 **生成计划时的优先考虑：**")
            context_parts.append("  ▶ 优先参考成功案例的工具序列模式")
            context_parts.append("  ▶ 确保避免已知的失败模式")
            context_parts.append("  ▶ 严格遵循上述核心指导原则")
            
            return "\n".join(context_parts)
            
        except Exception as e:
            print(f"⚠️ 获取历史上下文失败: {e}")
            return ""
    
    def get_rag_stats(self) -> Dict[str, Any]:
        """获取RAG系统统计信息"""
        if not self.enable_rag or not self.plan_optimizer:
            return {"rag_enabled": False, "message": "RAG系统未启用"}
        
        try:
            return self.plan_optimizer.get_optimization_stats()
        except Exception as e:
            return {"error": f"获取RAG统计失败: {e}"}
    
    def _save_plan_to_log(self, original_request: str, result: Dict[str, Any], save_logs: bool = True) -> Optional[str]:
        """将生成的计划保存到日志文件"""
        if not save_logs or not self.enable_logs or not self.log_manager:
            return None
            
        try:
            # 生成时间戳文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            log_filename = f"plan_{timestamp}.json"
            log_filepath = os.path.join(self.log_manager.log_dir, log_filename)
            
            # 构建日志数据
            log_data = {
                "timestamp": datetime.now().isoformat(),
                "original_request": original_request,
                "generation_result": {
                    "status": result.get("status"),
                    "processing_time": result.get("processing_time", 0),
                    "detected_platform": result.get("detected_platform"),
                    "rag_enabled": result.get("rag_enabled", False)
                }
            }
            
            # 如果生成成功，添加计划详情
            if result.get("status") == "success" and "plan" in result:
                plan = result["plan"]
                log_data["structured_plan"] = plan
                log_data["plan_summary"] = {
                    "plan_id": plan.get("plan_id"),
                    "summary": plan.get("summary"),
                    "platform": plan.get("platform"),
                    "total_steps": plan.get("total_steps", 0),
                    "step_count": len(plan.get("steps", []))
                }
                
                # 添加转换后的Agent指令
                agent_instruction = self.convert_plan_to_agent_instruction(plan)
                detailed_instruction = self.convert_plan_to_detailed_instruction(plan)
                log_data["agent_instructions"] = {
                    "simple_instruction": agent_instruction,
                    "detailed_instruction": detailed_instruction
                }
                
                # 如果有RAG优化结果，也保存
                if result.get("optimization_result"):
                    log_data["rag_optimization"] = result["optimization_result"]
                    
                # 使用日志管理器记录成功信息
                self.log_manager.info_agent(f"JSON计划生成成功: {plan.get('plan_id')} ({plan.get('total_steps', 0)}个步骤)")
            else:
                # 如果生成失败，保存错误信息
                log_data["error_info"] = {
                    "error": result.get("error"),
                    "raw_response": result.get("raw_response", "")
                }
                
                # 使用日志管理器记录错误信息
                self.log_manager.error_agent(f"JSON计划生成失败: {result.get('error', 'Unknown error')}")
            
            # 保存到文件
            with open(log_filepath, 'w', encoding='utf-8') as f:
                json.dump(log_data, f, ensure_ascii=False, indent=2)
            
            # 使用日志管理器记录保存信息
            self.log_manager.info_agent(f"计划已保存到: {log_filename}")
            print(f"📁 计划已保存到日志: {log_filepath}")
            return log_filepath
            
        except Exception as e:
            error_msg = f"保存计划日志失败: {e}"
            if self.log_manager:
                self.log_manager.error_agent(error_msg)
            print(f"⚠️ {error_msg}")
            return None
    
    def test_plan_generation(self, test_case: str, debug_mode: bool = False, save_logs: bool = True) -> None:
        """测试计划生成功能"""
        print(f"🧪 测试用例: {test_case}")
        print("="*60)
        
        result = self.generate_plan(test_case, debug_mode=debug_mode)
        
        # 保存到日志
        if save_logs:
            log_filepath = self._save_plan_to_log(test_case, result, save_logs)
            if log_filepath:
                result["log_filepath"] = log_filepath
        
        if result["status"] == "success":
            plan = result["plan"]
            print("✅ 计划生成成功")
            print(f"📋 计划ID: {plan.get('plan_id', 'unknown')}")
            print(f"📝 摘要: {plan.get('summary', 'unknown')}")
            print(f"🔢 总步骤数: {plan.get('total_steps', 0)}")
            print("\n📋 执行步骤:")
            
            for step in plan.get("steps", []):
                print(f"  {step['step_id']}. {step['description']}")
                print(f"     动作: {step['action']}")
                print(f"     参数: {json.dumps(step['parameters'], ensure_ascii=False)}")
                print(f"     预期: {step['expected_result']}")
                print()
            
            # 转换为Agent指令
            agent_instruction = self.convert_plan_to_agent_instruction(plan)
            print("🤖 转换后的Agent指令:")
            print(agent_instruction)
            
        else:
            print("❌ 计划生成失败")
            print(f"错误: {result['error']}")
            print(f"原始响应: {result['raw_response']}")


def main():
    """主函数，用于测试"""
    import argparse
    
    # 解析命令行参数
    parser = argparse.ArgumentParser(description='Agent JSON规划器测试')
    parser.add_argument('--no-rag', action='store_true', help='禁用RAG系统')
    parser.add_argument('--debug', action='store_true', help='启用调试模式')
    parser.add_argument('--no-logs', action='store_true', help='禁用日志保存')
    args = parser.parse_args()
    
    enable_rag = not args.no_rag
    debug_mode = args.debug
    save_logs = not args.no_logs
    
    print("🚀 启动Agent JSON规划器测试")
    print(f"📊 RAG系统: {'启用' if enable_rag else '禁用'}")
    print(f"🐛 调试模式: {'启用' if debug_mode else '禁用'}")
    print(f"📁 日志保存: {'启用' if save_logs else '禁用'}")
    
    # 初始化规划器
    planer = AgentJsonPlaner(enable_rag=enable_rag, enable_logs=save_logs)
    
    # 测试用例
    test_cases = [
        # 原有的复杂测试用例
        '点击左上角的地址，进入地址选择页后，校验地址选择页搜索框默认文案是否为“搜索城市/区县/地点”。点击当前页面搜索框，校验进入地址搜索页及搜索框默认文案为“搜索城市/区县/地点”，在搜索框输入文字“北京”，点击搜索结果“北京市”，校验返回首页，校验首页地址展示北京',
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{'='*80}")
        print(f"测试案例 {i}")
        print(f"{'='*80}")
        planer.test_plan_generation(test_case, debug_mode=debug_mode, save_logs=save_logs)


if __name__ == "__main__":
    main()