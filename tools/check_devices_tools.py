import subprocess
import re
import time
import sys
import os

# 添加父目录到路径，以便导入log_manager
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from tools._concurrent_log_manager import get_current_task_log_manager
from tools._device_status_manage_tools import get_device_status, get_devices_by_status, create_device_status


def get_android_device_info(udid: str) -> tuple:
    """
    获取Android设备的名称和系统版本（一次调用获取所有信息）
    
    Args:
        udid: Android设备的UDID
        
    Returns:
        tuple: (device_name, system_version) 元组
    """
    try:
        # 获取设备型号
        model_result = subprocess.run(['adb', '-s', udid, 'shell', 'getprop', 'ro.product.model'], 
                                    capture_output=True, text=True, check=True, encoding='utf-8')
        model = model_result.stdout.strip()
        
        # 获取系统版本
        version_result = subprocess.run(['adb', '-s', udid, 'shell', 'getprop', 'ro.build.version.release'], 
                                      capture_output=True, text=True, check=True, encoding='utf-8')
        system_version = version_result.stdout.strip()
        
        # 处理设备名称
        device_name = model.replace(' ', '_') if model else "Unknown_Android_Device"
        
        # 处理系统版本
        system_version = system_version if system_version else "Unknown"
        
        return device_name, system_version
        
    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取Android设备信息失败 {udid}: {e}", "get_android_device_info")
        return "Unknown_Android_Device", "Unknown"

def get_android_device_name(udid: str) -> str:
    """
    获取Android设备的真实名称（兼容性方法）
    
    Args:
        udid: Android设备的UDID
        
    Returns:
        设备名称（空格替换为下划线），如果获取失败返回 "Unknown_Android_Device"
    """
    device_name, _ = get_android_device_info(udid)
    return device_name

def get_android_system_version(udid: str) -> str:
    """
    获取Android设备的系统版本（兼容性方法）
    
    Args:
        udid: Android设备的UDID
        
    Returns:
        系统版本号，如果获取失败返回 "Unknown"
    """
    _, system_version = get_android_device_info(udid)
    return system_version

def get_ios_device_info(udid: str) -> tuple:
    """
    获取iOS设备的名称和系统版本（一次调用获取所有信息）
    
    Args:
        udid: iOS设备的UDID
        
    Returns:
        tuple: (device_name, system_version) 元组
    """
    try:
        result = subprocess.run(['ideviceinfo', '-u', udid], 
                              capture_output=True, text=True, check=True, encoding='utf-8')
        info = result.stdout
        if info:
            # 查找设备名称
            name_match = re.search(r"DeviceName: (.+)", info)
            device_name = name_match.group(1).strip().replace(' ', '_') if name_match else "Unknown_iOS_Device"
            
            # 查找系统版本
            version_match = re.search(r"ProductVersion: (.+)", info)
            system_version = version_match.group(1).strip() if version_match else "Unknown"
            
            return device_name, system_version
        else:
            return "Unknown_iOS_Device", "Unknown"
    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取iOS设备信息失败 {udid}: {e}", "get_ios_device_info")
        return "Unknown_iOS_Device", "Unknown"

def get_ios_device_name(udid: str) -> str:
    """
    获取iOS设备的真实名称（兼容性方法）
    
    Args:
        udid: iOS设备的UDID
        
    Returns:
        设备名称（空格替换为下划线），如果获取失败返回 "Unknown_iOS_Device"
    """
    device_name, _ = get_ios_device_info(udid)
    return device_name

def get_ios_system_version(udid: str) -> str:
    """
    获取iOS设备的系统版本（兼容性方法）
    
    Args:
        udid: iOS设备的UDID
        
    Returns:
        系统版本号，如果获取失败返回 "Unknown"
    """
    _, system_version = get_ios_device_info(udid)
    return system_version

def ensure_device_status_file(udid: str, platform: str) -> bool:
    """
    确保设备状态文件存在，如果不存在则创建
    
    Args:
        udid: 设备UDID
        platform: 设备平台 ("android" 或 "ios")
        
    Returns:
        是否成功确保状态文件存在
    """
    try:
        # 检查设备状态文件是否存在
        status = get_device_status(udid)
        if status:
            get_current_task_log_manager().info_tools(f"设备状态文件已存在: {udid}", "ensure_device_status_file")
            return True
        
        # 状态文件不存在，需要创建
        get_current_task_log_manager().info_tools(f"设备状态文件不存在，开始创建: {udid}", "ensure_device_status_file")
        
        # 获取设备真实名称和系统版本（一次调用获取所有信息）
        if platform.lower() == "android":
            device_name, system_version = get_android_device_info(udid)
        elif platform.lower() == "ios":
            device_name, system_version = get_ios_device_info(udid)
        else:
            get_current_task_log_manager().error_tools(f"不支持的平台: {platform}", "ensure_device_status_file")
            return False
        
        # 创建设备状态文件，包含系统版本
        result = create_device_status(udid, device_name, platform, system_version=system_version)
        if result:
            get_current_task_log_manager().info_tools(f"设备状态文件创建成功: {udid} ({device_name})", "ensure_device_status_file")
            return True
        else:
            get_current_task_log_manager().error_tools(f"设备状态文件创建失败: {udid}", "ensure_device_status_file")
            return False
            
    except Exception as e:
        get_current_task_log_manager().error_tools(f"确保设备状态文件时发生错误: {e}", "ensure_device_status_file")
        return False

def check_devices_connect_android() -> list[str]:
    """
    通过 `adb devices` 命令检测当前连接的安卓设备列表。
    只返回指定设备ID集合中的设备序列号。
    """
    # 指定允许操作的Android设备UDID集合
    allowed_android_devices = {"UQG5T20327008560","2MH0224412082233","d6f09d4a"}
    
    try:
        result = subprocess.run(['adb', 'devices'], capture_output=True, text=True, check=True, encoding='utf-8')
        output_lines = result.stdout.strip().split('\n')
        all_devices = []
        # 从第二行开始解析，跳过 "List of devices attached"
        for line in output_lines[1:]:
            if line.strip() and '\tdevice' in line:
                # 分割行以获取设备序列号
                device_id = line.split('\t')[0]
                all_devices.append(device_id)
        
        # 筛选只返回允许的设备
        devices = [device for device in all_devices if device in allowed_android_devices]
        get_current_task_log_manager().info_tools(f"检测到 {len(all_devices)} 个Android设备: {all_devices}", "check_android_devices")
        get_current_task_log_manager().info_tools(f"筛选后的 {len(devices)} 个允许操作的Android设备: {devices}", "check_android_devices")
        return devices
    
    except FileNotFoundError:
        error_msg = "错误: 未找到 'adb' 命令。请确保 ADB 已安装并已添加到系统 PATH。"
        get_current_task_log_manager().error_tools(error_msg, "check_android_devices")
        return []
    
    except subprocess.CalledProcessError as e:
        error_msg = f"执行 'adb devices' 时出错: {e}"
        get_current_task_log_manager().error_tools(f"{error_msg}, Stderr: {e.stderr}", "check_android_devices")
        return []
    
    except Exception as e:
        error_msg = f"解析 'adb devices' 输出时发生未知错误: {e}"
        get_current_task_log_manager().error_tools(error_msg, "check_android_devices")
        return []

def check_devices_connect_ios() -> list[str]:
    """
    通过 `idevice_id` 命令检测当前连接的 iOS 设备列表。
    只返回指定设备ID集合中的设备 UDID。
    """
    # 指定允许操作的iOS设备UDID集合
    allowed_ios_devices = {"00008101-00166C3811BA001E","00008110-001104592252801E","00008140-000238321401801C"}
    
    start_time = time.time()
    action_details = {"platform": "ios", "command": "idevice_id -l"}
    
    try:
        result = subprocess.run(['idevice_id', '-l'], capture_output=True, text=True, check=True, encoding='utf-8')
        # idevice_id -l 输出每个设备的 UDID，每行一个
        output_lines = result.stdout.strip().split('\n')
        # 过滤掉空行
        all_devices = [line for line in output_lines if line.strip()]
        
        # 筛选只返回允许的设备
        devices = [device for device in all_devices if device in allowed_ios_devices]
        
        get_current_task_log_manager().info_tools(f"检测到 {len(all_devices)} 个iOS设备: {all_devices}", "check_ios_devices")
        get_current_task_log_manager().info_tools(f"筛选后的 {len(devices)} 个允许操作的iOS设备: {devices}", "check_ios_devices")
        return devices
    
    except FileNotFoundError:
        error_msg = "错误: 未找到 'idevice_id' 命令。请确保 libimobiledevice 已安装并已添加到系统 PATH。"
        get_current_task_log_manager().error_tools(error_msg, "check_ios_devices")
        return []
    
    except subprocess.CalledProcessError as e:
        error_msg = f"执行 'idevice_id -l' 时出错: {e}"
        get_current_task_log_manager().error_tools(f"{error_msg}, Stderr: {e.stderr}", "check_ios_devices")
        return []
    
    except Exception as e:
        error_msg = f"解析 'idevice_id -l' 输出时发生未知错误: {e}"
        get_current_task_log_manager().error_tools(error_msg, "check_ios_devices")
        return [] 

def get_available_device(platform: str) -> str:
    """
    根据平台获取可用的设备（已连接且状态为ready）
    
    Args:
        platform: 设备平台 ("ios" 或 "android")
        
    Returns:
        可用设备的UDID，如果没有可用设备返回空字符串
    """
    try:
        platform_lower = platform.lower()
        get_current_task_log_manager().info_tools(f"开始查找{platform}平台的可用设备", "get_available_device")
        
        # 1. 获取已连接的设备列表
        if platform_lower == "android":
            connected_devices = check_devices_connect_android()
        elif platform_lower == "ios":
            connected_devices = check_devices_connect_ios()
        else:
            get_current_task_log_manager().error_tools(f"不支持的平台: {platform}", "get_available_device")
            return ""
        
        if not connected_devices:
            get_current_task_log_manager().info_tools(f"没有检测到{platform}平台的已连接设备", "get_available_device")
            return ""
        
        get_current_task_log_manager().info_tools(f"检测到{len(connected_devices)}个已连接的{platform}设备: {connected_devices}", "get_available_device")
        
        # 2. 获取状态为ready的设备
        ready_devices = get_devices_by_status("ready")
        
        # 3. 确保所有已连接设备都有状态文件，并筛选出状态为ready的设备
        available_devices = []
        for udid in connected_devices:
            # 确保设备状态文件存在
            if not ensure_device_status_file(udid, platform_lower):
                get_current_task_log_manager().error_tools(f"无法确保设备状态文件存在: {udid}", "get_available_device")
                continue
            
            # 重新获取设备状态（因为可能刚刚创建了状态文件）
            device_status = get_device_status(udid)
            if not device_status:
                get_current_task_log_manager().error_tools(f"无法获取设备状态: {udid}", "get_available_device")
                continue
            
            device_platform = device_status.get("platform", "").lower()
            device_status_str = device_status.get("status", "")
            
            # 确保平台匹配且状态为ready
            if device_platform == platform_lower and device_status_str == "ready":
                available_devices.append({
                    "udid": udid,
                    "device_name": device_status.get("device_name", "Unknown"),
                    "platform": device_platform
                })
        
        if not available_devices:
            get_current_task_log_manager().info_tools(f"没有找到{platform}平台的可用设备（已连接且状态为ready）", "get_available_device")
            return ""
        
        # 4. 返回第一个可用设备
        selected_device = available_devices[0]
        get_current_task_log_manager().info_tools(f"选择设备: {selected_device['udid']} ({selected_device['device_name']})", "get_available_device")
        
        return selected_device["udid"]
        
    except Exception as e:
        get_current_task_log_manager().error_tools(f"获取可用设备时发生错误: {e}", "get_available_device")
        return ""
    
if __name__ == "__main__":
    print("Android设备:", check_devices_connect_android())
    print("iOS设备:", check_devices_connect_ios())
    print("可用Android设备:", get_available_device("android"))
    print("可用iOS设备:", get_available_device("ios"))