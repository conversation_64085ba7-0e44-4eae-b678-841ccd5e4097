#!/usr/bin/env python3
"""
并发Agent池
使用新的并发任务管理器和并发日志管理器，实现真正的任务隔离
"""

import threading
import time
from typing import Dict, List, Optional, Tuple
from concurrent_agent import ConcurrentAgent
from tools._concurrent_task_manager import get_concurrent_task_manager, TaskStatus
from tools._concurrent_log_manager import create_task_log_manager, cleanup_task_log_manager, get_global_log_manager
from ollama_simple_manager import ollama_manager, DEFAULT_MODEL


class ConcurrentAgentPool:
    """并发Agent池 - 支持真正的任务隔离"""
    
    # 限制只使用这些端口执行任务
    ALLOWED_TASK_PORTS = [11435, 11436]
    
    def __init__(self):
        self.model_name = DEFAULT_MODEL
        self.agents: Dict[int, ConcurrentAgent] = {}  # port -> agent
        self.agent_status: Dict[int, Dict] = {}  # port -> status
        self.task_assignments: Dict[str, Dict] = {}  # task_id -> {port, round_id}
        self.lock = threading.Lock()
        self.task_manager = get_concurrent_task_manager()
        self.global_log_manager = get_global_log_manager()
        
    def initialize_pool(self) -> bool:
        """初始化Agent池"""
        # 获取已加载模型的Ollama端口
        ready_ports = ollama_manager.get_ready_ports()
        
        # 过滤出允许执行任务的端口
        task_ports = [port for port in ready_ports if port in self.ALLOWED_TASK_PORTS]
        
        if not task_ports:
            self.global_log_manager.error_agent(f"没有可用于执行任务的Ollama实例，需要端口: {self.ALLOWED_TASK_PORTS}")
            self.global_log_manager.info_agent(f"当前已就绪端口: {ready_ports}")
            return False
        
        # 获取所有实例的状态信息，包含每个端口对应的模型
        ollama_status = ollama_manager.get_status()
        
        success_count = 0
        with self.lock:
            for port in task_ports:
                try:
                    # 获取该端口对应的模型名称
                    port_status = ollama_status.get(port, {})
                    port_model = port_status.get('model', self.model_name)
                    
                    self.global_log_manager.info_agent(f"为端口 {port} 初始化Agent，模型: {port_model}")
                    
                    # 创建连接到指定端口的Agent实例（暂时不分配任务ID）
                    agent = ConcurrentAgent(
                        model_name=port_model,  # 使用端口对应的具体模型
                        base_url=f"http://localhost:{port}",
                        temperature=0.1,
                        task_id=None,  # 初始化时不分配任务ID
                        log_manager=self.global_log_manager  # 使用全局日志管理器初始化
                    )
                    
                    self.agents[port] = agent
                    self.agent_status[port] = {
                        'status': 'idle',  # idle, busy
                        'current_task': None,
                        'task_start_time': None,
                        'total_tasks': 0
                    }
                    
                    success_count += 1
                    self.global_log_manager.info_agent(f"Agent实例初始化成功，端口: {port}")
                    
                except Exception as e:
                    self.global_log_manager.error_agent(f"Agent实例初始化失败，端口: {port}, 错误: {e}")
        
        self.global_log_manager.info_agent(f"并发Agent池初始化完成: {success_count}/{len(task_ports)} 个成功")
        self.global_log_manager.info_agent(f"仅在端口 {self.ALLOWED_TASK_PORTS} 上执行任务，过滤掉其他端口")
        return success_count > 0
    
    def get_idle_agent(self) -> Tuple[Optional[int], Optional[ConcurrentAgent]]:
        """获取空闲的Agent实例"""
        with self.lock:
            idle_ports = [
                port for port, status in self.agent_status.items()
                if status['status'] == 'idle'
            ]
            
            if idle_ports:
                # 选择任务数最少的Agent
                selected_port = min(idle_ports, 
                                  key=lambda p: self.agent_status[p]['total_tasks'])
                return selected_port, self.agents[selected_port]
            
            return None, None
    
    def assign_task(self, task_description: str, mis_id: str, is_smart_task: bool = False) -> Tuple[bool, str, Optional[str]]:
        """
        分配任务给空闲的Agent
        
        Returns:
            (success, message, round_id)
        """
        # 检查是否可以接受新任务
        if not self.task_manager.can_accept_new_task():
            return False, f"已达到最大并发任务数({self.task_manager.max_concurrent_tasks})，请稍后再试", None
        
        # 获取空闲的Agent
        port, agent = self.get_idle_agent()
        if not agent:
            return False, "没有空闲的Agent实例", None
        
        try:
            # 创建新任务
            task_id, task_info = self.task_manager.create_new_task(task_description, mis_id)
            round_id = task_info.round_id
            
            # 启动任务
            if not self.task_manager.start_task(task_id, port):
                return False, f"启动任务失败: {task_id}", None
            
            # 为任务创建专用的日志管理器
            task_log_manager = create_task_log_manager(task_id, task_info.log_dir)
            
            # 更新Agent以使用任务专用的日志管理器
            agent.log_manager = task_log_manager
            agent.task_id = task_id
            agent.is_smart_task = is_smart_task  # 设置任务类型
            
            with self.lock:
                # 标记Agent为忙碌
                self.agent_status[port].update({
                    'status': 'busy',
                    'current_task': task_id,
                    'task_start_time': time.time(),
                    'total_tasks': self.agent_status[port]['total_tasks'] + 1
                })
                
                # 记录任务分配
                self.task_assignments[task_id] = {
                    'port': port,
                    'round_id': round_id,
                    'agent': agent
                }
            
            task_log_manager.info_agent(f"任务 {task_id} 已分配给端口 {port}")
            
            # 在后台执行任务
            task_thread = threading.Thread(
                target=self._execute_task,
                args=(port, agent, task_id, task_description, mis_id, round_id, is_smart_task)
            )
            task_thread.daemon = True
            task_thread.start()
            
            return True, f"任务已分配给端口 {port}", round_id
            
        except Exception as e:
            error_msg = f"分配任务时出错: {str(e)}"
            self.global_log_manager.error_agent(error_msg)
            return False, error_msg, None
    
    def assign_smart_task(self, natural_language: str, mis_id: str) -> Tuple[bool, str, Optional[str]]:
        """
        分配智能任务给空闲的Agent（异步处理转换）
        
        Returns:
            (success, message, round_id)
        """
        # 检查是否可以接受新任务
        if not self.task_manager.can_accept_new_task():
            return False, f"已达到最大并发任务数({self.task_manager.max_concurrent_tasks})，请稍后再试", None
        
        # 获取空闲的Agent
        port, agent = self.get_idle_agent()
        if not agent:
            return False, "没有空闲的Agent实例", None
        
        try:
            # 创建新任务
            task_id, task_info = self.task_manager.create_new_task(natural_language, mis_id)
            round_id = task_info.round_id
            
            # 启动任务
            if not self.task_manager.start_task(task_id, port):
                return False, f"启动任务失败: {task_id}", None
            
            # 为任务创建专用的日志管理器
            task_log_manager = create_task_log_manager(task_id, task_info.log_dir)
            
            # 更新Agent以使用任务专用的日志管理器
            agent.log_manager = task_log_manager
            agent.task_id = task_id
            agent.is_smart_task = True  # 标记为智能任务
            
            with self.lock:
                # 标记Agent为忙碌
                self.agent_status[port].update({
                    'status': 'busy',
                    'current_task': task_id,
                    'task_start_time': time.time(),
                    'total_tasks': self.agent_status[port]['total_tasks'] + 1
                })
                
                # 记录任务分配
                self.task_assignments[task_id] = {
                    'port': port,
                    'round_id': round_id,
                    'agent': agent
                }
            
            task_log_manager.info_agent(f"智能任务 {task_id} 已分配给端口 {port}")
            
            # 在后台执行智能任务（包含转换）
            task_thread = threading.Thread(
                target=self._execute_smart_task,
                args=(port, agent, task_id, natural_language, mis_id, round_id)
            )
            task_thread.daemon = True
            task_thread.start()
            
            return True, f"智能任务已分配给端口 {port}", round_id
            
        except Exception as e:
            error_msg = f"分配智能任务时出错: {str(e)}"
            self.global_log_manager.error_agent(error_msg)
            return False, error_msg, None
    
    def _execute_smart_task(self, port: int, agent: ConcurrentAgent, task_id: str, 
                           natural_language: str, mis_id: str, round_id: str):
        """在后台执行智能任务（包含转换）"""
        try:
            agent.log_manager.info_agent(f"开始执行智能任务 {task_id}，端口: {port}，轮次: {round_id}")
            
            # 导入json_planer
            from agent_json_planer import AgentJsonPlaner
            json_planer = AgentJsonPlaner()
            
            # 1. 自然语言到结构化计划的转换
            agent.log_manager.info_agent(f"步骤1: 开始自然语言转换")
            plan_result = json_planer.generate_plan(natural_language)
            
            if plan_result["status"] != "success":
                error_msg = f"计划转换失败: {plan_result['error']}"
                agent.log_manager.error_agent(error_msg)
                self.task_manager.complete_task(task_id, result=error_msg, success=False)
                return
            
            plan = plan_result["plan"]
            agent.log_manager.info_agent(f"步骤1完成: 转换成功 {plan.get('total_steps', 0)} 个步骤")
            
            # 2. 转换为Agent指令
            agent.log_manager.info_agent(f"步骤2: 转换为Agent执行指令")
            detailed_instruction = json_planer.convert_plan_to_detailed_instruction(plan)
            agent.log_manager.info_agent(f"步骤2完成: 生成指令 {len(detailed_instruction.split(chr(10)))} 行")
            
            # 3. 清除聊天历史并执行
            agent.log_manager.info_agent(f"步骤3: 开始执行转换后的指令")
            agent.clear_history()
            
            # 设置任务上下文
            agent.set_task_context(task_id, round_id, port)
            
            # 重置设备连接状态
            agent.reset_device_connections()
            
            # 执行转换后的指令
            result = agent.chat(detailed_instruction)
            
            # 完成任务
            self.task_manager.complete_task(task_id, result=result)
            
            agent.log_manager.info_agent(f"智能任务 {task_id} 执行完成，端口: {port}")
            
        except Exception as e:
            error_msg = str(e)
            if hasattr(agent, 'log_manager'):
                agent.log_manager.error_agent(f"智能任务 {task_id} 执行失败，端口: {port}, 错误: {error_msg}")
            else:
                self.global_log_manager.error_agent(f"智能任务 {task_id} 执行失败，端口: {port}, 错误: {error_msg}")
            
            # 标记任务失败
            self.task_manager.complete_task(task_id, result=error_msg, success=False)
        finally:
            # 释放Agent
            with self.lock:
                if task_id in self.task_assignments:
                    del self.task_assignments[task_id]
                
                if port in self.agent_status:
                    self.agent_status[port].update({
                        'status': 'idle',
                        'current_task': None,
                        'task_start_time': None
                    })
    
    def _execute_task(self, port: int, agent: ConcurrentAgent, task_id: str, 
                     task_description: str, mis_id: str, round_id: str, is_smart_task: bool = False):
        """在后台执行任务"""
        try:
            agent.log_manager.info_agent(f"开始执行任务 {task_id}，端口: {port}，轮次: {round_id}")
            
            # 清除聊天历史
            agent.clear_history()
            
            # 设置任务上下文
            agent.set_task_context(task_id, round_id, port)
            
            # 重置设备连接状态
            agent.reset_device_connections()
            
            # 执行任务
            result = agent.chat(task_description)
            
            # 完成任务
            self.task_manager.complete_task(task_id, result=result)
            
            agent.log_manager.info_agent(f"任务 {task_id} 执行完成，端口: {port}")
            
        except Exception as e:
            error_msg = str(e)
            if hasattr(agent, 'log_manager'):
                agent.log_manager.error_agent(f"任务 {task_id} 执行失败，端口: {port}, 错误: {error_msg}")
            else:
                self.global_log_manager.error_agent(f"任务 {task_id} 执行失败，端口: {port}, 错误: {error_msg}")
            
            # 完成任务（失败）
            self.task_manager.complete_task(task_id, error=error_msg)
            
        finally:
            # 清理任务
            self._cleanup_task(task_id, port, agent)
    
    def _cleanup_task(self, task_id: str, port: int, agent: ConcurrentAgent):
        """清理任务"""
        try:
            # 清理任务专用的日志管理器
            cleanup_task_log_manager(task_id)
            
            # 重置Agent的日志管理器为全局日志管理器
            agent.log_manager = self.global_log_manager
            agent.task_id = None
            
            with self.lock:
                # 释放Agent
                if port in self.agent_status:
                    self.agent_status[port].update({
                        'status': 'idle',
                        'current_task': None,
                        'task_start_time': None
                    })
                
                # 移除任务分配记录
                if task_id in self.task_assignments:
                    del self.task_assignments[task_id]
            
            self.global_log_manager.info_agent(f"Agent端口 {port} 已释放，任务 {task_id} 清理完成")
            
        except Exception as e:
            self.global_log_manager.error_agent(f"清理任务 {task_id} 时出错: {e}")
    
    def _force_cleanup_task(self, task_id: str, port: int):
        """强制清理超时任务"""
        try:
            self.global_log_manager.warning_agent(f"强制清理超时任务: {task_id}, 端口: {port}")
            
            with self.lock:
                # 释放Agent
                if port in self.agent_status:
                    self.agent_status[port].update({
                        'status': 'idle',
                        'current_task': None,
                        'task_start_time': None
                    })
                
                # 移除任务分配记录
                if task_id in self.task_assignments:
                    del self.task_assignments[task_id]
            
            self.global_log_manager.info_agent(f"Agent端口 {port} 已强制释放，超时任务 {task_id} 清理完成")
            
        except Exception as e:
            self.global_log_manager.error_agent(f"强制清理任务 {task_id} 时出错: {e}")
    
    def get_pool_status(self) -> Dict:
        """获取池状态"""
        with self.lock:
            idle_count = len([s for s in self.agent_status.values() if s['status'] == 'idle'])
            busy_count = len([s for s in self.agent_status.values() if s['status'] == 'busy'])
            
            return {
                'total_agents': len(self.agents),
                'idle_agents': idle_count,
                'busy_agents': busy_count,
                'agent_details': dict(self.agent_status),
                'active_tasks': len(self.task_assignments),
                'task_manager_status': self.task_manager.get_status_summary(),
                'timeout_seconds': getattr(self.task_manager, 'task_timeout_seconds', 'N/A')
            }
    
    def get_running_tasks(self) -> List[Dict]:
        """获取运行中的任务"""
        return self.task_manager.get_running_tasks_info()
    
    def can_accept_task(self) -> bool:
        """检查是否可以接受新任务"""
        return self.task_manager.can_accept_new_task()
    
    def get_task_result(self, task_id: str) -> Optional[Dict]:
        """获取任务结果"""
        task_info = self.task_manager.get_task_info(task_id)
        if not task_info:
            return None
        
        return {
            "task_id": task_info.task_id,
            "round_id": task_info.round_id,
            "status": task_info.status.value,
            "result": task_info.result,
            "error": task_info.error,
            "port": task_info.port,
            "start_time": task_info.start_time.strftime("%Y-%m-%d %H:%M:%S"),
            "end_time": task_info.end_time.strftime("%Y-%m-%d %H:%M:%S") if task_info.end_time else None
        }


# 全局并发Agent池实例
concurrent_agent_pool = ConcurrentAgentPool()


def initialize_concurrent_agent_pool() -> bool:
    """初始化全局并发Agent池"""
    return concurrent_agent_pool.initialize_pool()


def get_concurrent_agent_pool() -> ConcurrentAgentPool:
    """获取全局并发Agent池"""
    return concurrent_agent_pool


if __name__ == "__main__":
    print("🔄 测试并发Agent池...")
    
    if initialize_concurrent_agent_pool():
        print("✅ 并发Agent池初始化成功")
        
        pool = get_concurrent_agent_pool()
        status = pool.get_pool_status()
        print(f"📊 Agent池状态: {status['idle_agents']}/{status['total_agents']} 个空闲")
        
        # 测试任务分配
        print("\n🧪 测试任务分配...")
        success, message, round_id = pool.assign_task("测试任务", "test_001")
        if success:
            print(f"✅ 任务分配成功: {message}, 轮次: {round_id}")
        else:
            print(f"❌ 任务分配失败: {message}")
        
        # 等待一段时间让任务执行
        time.sleep(2)
        
        # 检查状态
        status = pool.get_pool_status()
        print(f"📊 更新后状态: {status}")
        
    else:
        print("❌ 并发Agent池初始化失败")