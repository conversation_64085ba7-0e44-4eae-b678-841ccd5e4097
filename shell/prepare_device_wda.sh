#!/bin/bash
# 此脚本用于为每个设备准备特定的 WebDriverAgent 目录和配置

# 获取脚本所在目录
SCRIPT_DIR=$(dirname "$(realpath "$0")")
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")

# 自动查找最新的 WebDriverAgent 版本目录
LATEST_WDA_DIR=$(ls -d "$PROJECT_ROOT"/WebDriverAgent-* 2>/dev/null | sort -V | tail -n 1)
if [ -z "$LATEST_WDA_DIR" ]; then
  echo "未找到 WebDriverAgent 版本目录！"
  exit 1
fi
WDA_VERSION=$(basename "$LATEST_WDA_DIR" | sed 's/WebDriverAgent-//')

SOURCE_WDA_DIR="$LATEST_WDA_DIR"

# 检查源目录是否存在
if [ ! -d "$SOURCE_WDA_DIR" ]; then
  echo "错误：源 WebDriverAgent 目录不存在: $SOURCE_WDA_DIR"
  exit 1
fi

# 检查源目录中是否包含必要的文件
if [ ! -f "$SOURCE_WDA_DIR/WebDriverAgent.xcodeproj/project.pbxproj" ]; then
  echo "错误：源 WebDriverAgent 目录中缺少 project.pbxproj 文件"
  exit 1
fi

# 设备列表及其对应的签名配置
DEVICES=(
  "00008030-000D78DE02EA802E:YTZC3SB5GK:com.sankuai.meituan.wda0000001"
  "00008120-001279343ADB401E:YTZC3SB5GK:com.sankuai.meituan.wda0000002"
  "00008120-0002519E1400C01E:YTZC3SB5GK:com.sankuai.meituan.wda0000003"
  "00008101-000979841401001E:YTZC3SB5GK:com.sankuai.meituan.wda0000004"
  "00008130-0002710621E0001C:YTZC3SB5GK:com.sankuai.meituan.wda0000005"
  "00008101-0002612C3608001E:YTZC3SB5GK:com.sankuai.meituan.wda0000006"
  "00008140-000238321401801C:YTZC3SB5GK:com.sankuai.meituan.wda0000007"
  "00008110-001104592252801E:YTZC3SB5GK:com.sankuai.meituan.wda0000008"
  "00008030-001550602192802E:YTZC3SB5GK:com.sankuai.meituan.wda0000009"
  "00008120-000A746E0E80C01E:YTZC3SB5GK:com.sankuai.meituan.wda0000010"
  "00008101-00166C3811BA001E:YTZC3SB5GK:com.sankuai.meituan.wda0000011"
)

# 设备配置文件路径
DEVICE_CONFIG_FILE="$PROJECT_ROOT/device_config.txt"

# 检查是否指定了特定设备
TARGET_UDID=$1

# 获取下一个可用的 Bundle ID 序号
get_next_bundle_id_number() {
  local max_number=0
  
  # 首先检查已知设备列表中的最大序号
  for device_config in "${DEVICES[@]}"; do
    local bundle_id=$(echo "$device_config" | cut -d':' -f3)
    if [[ $bundle_id =~ com\.sankuai\.meituan\.wda([0-9]+)$ ]]; then
      local number=${BASH_REMATCH[1]}
      if (( number > max_number )); then
        max_number=$number
      fi
    fi
  done
  
  # 然后检查配置文件中的最大序号（如果文件存在）
  if [ -f "$DEVICE_CONFIG_FILE" ]; then
    while IFS=':' read -r device_id team_id bundle_id || [[ -n "$device_id" ]]; do
      if [[ $bundle_id =~ com\.sankuai\.meituan\.wda([0-9]+)$ ]]; then
        local number=${BASH_REMATCH[1]}
        if (( number > max_number )); then
          max_number=$number
        fi
      fi
    done < "$DEVICE_CONFIG_FILE"
  fi
  
  # 返回下一个可用序号
  echo $((max_number + 1))
}

# 为新设备生成配置
generate_device_config() {
  local udid=$1
  local team_id="YTZC3SB5GK"  # 默认团队 ID
  
  # 获取下一个可用的 Bundle ID 序号
  local next_number=$(get_next_bundle_id_number)
  
  # 格式化为7位数，前导零填充
  local formatted_number=$(printf "%07d" $next_number)
  
  # 生成新的 Bundle ID
  local bundle_id="com.sankuai.meituan.wda$formatted_number"
  
  echo "为新设备 $udid 生成配置:"
  echo "  团队 ID: $team_id"
  echo "  Bundle ID: $bundle_id"
  
  # 将新配置添加到配置文件
  mkdir -p "$(dirname "$DEVICE_CONFIG_FILE")"
  echo "$udid:$team_id:$bundle_id" >> "$DEVICE_CONFIG_FILE"
  
  # 返回配置
  echo "$team_id:$bundle_id"
}

# 获取设备配置
get_device_config() {
  local udid=$1
  local config=""
  
  # 首先在内置设备列表中查找
  for device_config in "${DEVICES[@]}"; do
    local device_id=$(echo "$device_config" | cut -d':' -f1)
    if [ "$device_id" = "$udid" ]; then
      config=$(echo "$device_config" | cut -d':' -f2,3)
      break
    fi
  done
  
  # 如果内置列表中没有找到，则在配置文件中查找
  if [ -z "$config" ] && [ -f "$DEVICE_CONFIG_FILE" ]; then
    while IFS=':' read -r device_id team_id bundle_id || [[ -n "$device_id" ]]; do
      if [ "$device_id" = "$udid" ]; then
        config="$team_id:$bundle_id"
        break
      fi
    done < "$DEVICE_CONFIG_FILE"
  fi
  
  # 如果仍然没有找到，则生成新配置
  if [ -z "$config" ]; then
    config=$(generate_device_config "$udid")
  fi
  
  echo "$config"
}

# 准备单个设备的 WDA 目录和配置
prepare_device_wda() {
  local UDID=$1
  
  # 获取设备配置
  local CONFIG=$(get_device_config "$UDID")
  
  # 解析签名配置
  IFS=':' read -r TEAM_ID BUNDLE_ID <<< "$CONFIG"
  
  echo "准备设备 $UDID 的 WDA 目录..."
  echo "团队 ID: $TEAM_ID"
  echo "Bundle ID: $BUNDLE_ID"
  
  # 设备特定的 WDA 目录 - 使用新的命名格式
  DEVICE_WDA_DIR="$PROJECT_ROOT/WDA-${UDID}-${WDA_VERSION}"
  
  # 如果目录存在但可能已损坏，则删除它
  if [ -d "$DEVICE_WDA_DIR" ]; then
    echo "删除可能已损坏的 WDA 目录: $DEVICE_WDA_DIR"
    rm -rf "$DEVICE_WDA_DIR"
  fi
  
  # 创建并复制源文件
  echo "创建设备 $UDID 的 WDA 目录: $DEVICE_WDA_DIR"
  mkdir -p "$DEVICE_WDA_DIR"
  
  # 复制 WebDriverAgent 文件
  echo "复制 WebDriverAgent 文件到设备目录..."
  rsync -a "$SOURCE_WDA_DIR/" "$DEVICE_WDA_DIR/"
  
  # 修改项目配置文件以使用特定的签名
  PROJECT_FILE="$DEVICE_WDA_DIR/WebDriverAgent.xcodeproj/project.pbxproj"
  
  if [ -f "$PROJECT_FILE" ]; then
    echo "更新设备 $UDID 的项目配置..."
    
    # 备份原始文件
    cp "$PROJECT_FILE" "$PROJECT_FILE.bak"
    
    # 使用简单的替换方法，避免复杂的正则表达式
    # 替换开发团队 ID
    sed -i '' "s/DEVELOPMENT_TEAM = \"\";/DEVELOPMENT_TEAM = \"$TEAM_ID\";/g" "$PROJECT_FILE"
    sed -i '' "s/DEVELOPMENT_TEAM = \"[^\"]*\";/DEVELOPMENT_TEAM = \"$TEAM_ID\";/g" "$PROJECT_FILE"
    
    # 替换 Bundle ID
    sed -i '' "s/PRODUCT_BUNDLE_IDENTIFIER = \"com.facebook.WebDriverAgentRunner\";/PRODUCT_BUNDLE_IDENTIFIER = \"$BUNDLE_ID\";/g" "$PROJECT_FILE"
    
    # 验证 project.pbxproj 文件格式
    if plutil -lint "$PROJECT_FILE" > /dev/null; then
      echo "项目文件格式验证通过"
    else
      echo "警告：项目文件格式可能已损坏，恢复备份"
      cp "$PROJECT_FILE.bak" "$PROJECT_FILE"
      echo "使用原始项目文件，仅通过命令行参数设置签名信息"
    fi
    
    # 修改 Info.plist 文件
    INFO_PLIST="$DEVICE_WDA_DIR/WebDriverAgentRunner/Info.plist"
    if [ -f "$INFO_PLIST" ]; then
      # 备份 Info.plist
      cp "$INFO_PLIST" "$INFO_PLIST.bak"
      
      # 使用 PlistBuddy 直接修改 Info.plist
      if /usr/libexec/PlistBuddy -c "Set :CFBundleIdentifier $BUNDLE_ID" "$INFO_PLIST" 2>/dev/null; then
        echo "已更新 Info.plist 中的 Bundle ID 为 $BUNDLE_ID"
      else
        echo "警告：无法修改 Info.plist，恢复备份"
        cp "$INFO_PLIST.bak" "$INFO_PLIST"
      fi
    fi
    
    # 清理项目缓存
    echo "清理项目缓存..."
    (cd "$DEVICE_WDA_DIR" && xcodebuild clean -project WebDriverAgent.xcodeproj -scheme WebDriverAgentRunner -quiet)
    
    echo "设备 $UDID 的 WDA 准备完成: $DEVICE_WDA_DIR"
  else
    echo "错误：找不到设备 $UDID 的项目配置文件！"
  fi
  
  # 创建符号链接到 device_wda 目录（为了兼容性）
  LEGACY_DIR="$PROJECT_ROOT/device_wda/WDA_${UDID}"
  if [ ! -d "$PROJECT_ROOT/device_wda" ]; then
    mkdir -p "$PROJECT_ROOT/device_wda"
  fi
  
  # 如果旧目录存在但不是符号链接，则备份它
  if [ -d "$LEGACY_DIR" ] && [ ! -L "$LEGACY_DIR" ]; then
    echo "备份旧的 WDA 目录: $LEGACY_DIR -> ${LEGACY_DIR}_backup"
    mv "$LEGACY_DIR" "${LEGACY_DIR}_backup"
  fi
  
  # 创建符号链接
  if [ ! -e "$LEGACY_DIR" ]; then
    echo "创建符号链接: $LEGACY_DIR -> $DEVICE_WDA_DIR"
    ln -sf "$DEVICE_WDA_DIR" "$LEGACY_DIR"
  fi
}

# 主程序
if [ -n "$TARGET_UDID" ]; then
  # 如果指定了特定设备，则只准备该设备的 WDA
  echo "为指定设备 $TARGET_UDID 准备 WDA 目录和配置"
  prepare_device_wda "$TARGET_UDID"
else
  # 否则，准备所有设备的 WDA
  echo "为所有设备准备 WDA 目录和配置"
  
  # 首先处理内置设备列表
  for device_config in "${DEVICES[@]}"; do
    UDID=$(echo "$device_config" | cut -d':' -f1)
    prepare_device_wda "$UDID"
  done
  
  # 然后处理配置文件中的设备（如果文件存在）
  if [ -f "$DEVICE_CONFIG_FILE" ]; then
    while IFS=':' read -r device_id _ _ || [[ -n "$device_id" ]]; do
      # 检查该设备是否已在内置列表中处理过
      local already_processed=false
      for device_config in "${DEVICES[@]}"; do
        if [ "$(echo "$device_config" | cut -d':' -f1)" = "$device_id" ]; then
          already_processed=true
          break
        fi
      done
      
      if [ "$already_processed" = false ]; then
        prepare_device_wda "$device_id"
      fi
    done < "$DEVICE_CONFIG_FILE"
  fi
fi

echo "WDA 目录准备完成。" 