#!/bin/bash
UDID=$1
PORT=$2
LOG_FILE=$3
IOS_TOOL_LOG_FILE=$4

SCRIPT_DIR=$(dirname "$(realpath "$0")")
PROJECT_ROOT=$(dirname "$SCRIPT_DIR")

# 动态查找 ios 命令位置的函数
find_ios_command() {
  local ios_cmd=""
  
  # 方法1: 检查 NVM 中的 Node.js 版本
  if [ -f ~/.nvm/nvm.sh ]; then
    # 加载 NVM 环境
    export NVM_DIR="$HOME/.nvm"
    [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
    
    # 获取当前使用的 Node.js 版本
    if command -v nvm >/dev/null 2>&1; then
      current_version=$(nvm current 2>/dev/null)
      if [ "$current_version" != "system" ] && [ -n "$current_version" ]; then
        nvm_ios_path="$HOME/.nvm/versions/node/$current_version/bin/ios"
        if [ -x "$nvm_ios_path" ]; then
          ios_cmd="$nvm_ios_path"
          echo "找到 NVM 中的 ios 命令: $ios_cmd" >&2
          echo "$ios_cmd"
          return 0
        fi
      fi
    fi
    
    # 如果当前版本不可用，搜索所有 NVM 版本
    if [ -d "$HOME/.nvm/versions/node" ]; then
      for version_dir in "$HOME/.nvm/versions/node"/*; do
        if [ -d "$version_dir" ]; then
          version_ios_path="$version_dir/bin/ios"
          if [ -x "$version_ios_path" ]; then
            ios_cmd="$version_ios_path"
            echo "找到 NVM 版本中的 ios 命令: $ios_cmd" >&2
            echo "$ios_cmd"
            return 0
          fi
        fi
      done
    fi
  fi
  
  # 方法2: 使用 which 命令查找全局安装的 ios
  if command -v ios >/dev/null 2>&1; then
    ios_cmd=$(which ios)
    echo "找到全局 ios 命令: $ios_cmd" >&2
    echo "$ios_cmd"
    return 0
  fi
  
  # 方法3: 在常见路径中搜索
  local common_paths=(
    "/usr/local/bin/ios"
    "/opt/homebrew/bin/ios"
    "/usr/bin/ios"
    "$HOME/.local/bin/ios"
    "$HOME/bin/ios"
  )
  
  for path in "${common_paths[@]}"; do
    if [ -x "$path" ]; then
      ios_cmd="$path"
      echo "在常见路径中找到 ios 命令: $ios_cmd" >&2
      echo "$ios_cmd"
      return 0
    fi
  done
  
  # 方法4: 使用 find 命令在用户目录搜索（最后的手段）
  echo "正在搜索 ios 命令..." >&2
  local found_ios=$(find "$HOME" -name "ios" -type f -executable 2>/dev/null | head -1)
  if [ -n "$found_ios" ]; then
    ios_cmd="$found_ios"
    echo "通过搜索找到 ios 命令: $ios_cmd" >&2
    echo "$ios_cmd"
    return 0
  fi
  
  # 如果都找不到，返回错误
  echo "错误: 无法找到 ios 命令！请确保已正确安装 go-ios" >&2
  return 1
}

# 获取 ios 命令路径
IOS_COMMAND=$(find_ios_command)
if [ $? -ne 0 ] || [ -z "$IOS_COMMAND" ]; then
  echo "错误: 无法找到 ios 命令，请检查 go-ios 安装" >&2
  exit 1
fi

echo "使用 ios 命令: $IOS_COMMAND" >&2

# 自动查找最新的 WebDriverAgent 版本目录
LATEST_WDA_DIR=$(ls -d "$PROJECT_ROOT"/WebDriverAgent-* 2>/dev/null | sort -V | tail -n 1)
if [ -z "$LATEST_WDA_DIR" ]; then
  echo "未找到 WebDriverAgent 版本目录！"
  exit 1
fi
WDA_VERSION=$(basename "$LATEST_WDA_DIR" | sed 's/WebDriverAgent-//')

DEVICE_WDA_DIR="WDA-${UDID}-${WDA_VERSION}"
DEVICE_WDA_PATH="$PROJECT_ROOT/$DEVICE_WDA_DIR"

# 设备特定的签名配置
# 可以根据设备 UDID 设置不同的开发团队 ID 和 Bundle ID
case "$UDID" in
  "00008030-000D78DE02EA802E")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000001"
    ;;
  "00008120-001279343ADB401E")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000002"
    ;;
  "00008120-0002519E1400C01E")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000003"
    ;;
  "00008101-000979841401001E")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000004"
    ;;
  "00008130-0002710621E0001C")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000005"
    ;;
  "00008101-0002612C3608001E")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000006"
    ;;
  "00008101-00166C3811BA001E")
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.sankuai.meituan.wda0000011"
    ;;
  *)
    # 默认配置
    DEVELOPMENT_TEAM="YTZC3SB5GK"
    PRODUCT_BUNDLE_IDENTIFIER="com.facebook.WebDriverAgentRunner"
    ;;
esac

# 检查设备特定的 WDA 目录是否存在
if [ -d "$DEVICE_WDA_PATH" ] && [ -d "$DEVICE_WDA_PATH/WebDriverAgent.xcodeproj" ]; then
  echo "使用设备特定的 WDA 目录: $DEVICE_WDA_PATH" >> "$LOG_FILE"
  TARGET_DIR="$DEVICE_WDA_PATH"
else
  echo "设备特定的 WDA 目录不存在，准备创建..." >> "$LOG_FILE"
  
  # 调用准备脚本来创建设备特定的 WDA 目录
  PREPARE_SCRIPT="$SCRIPT_DIR/prepare_device_wda.sh"
  if [ -f "$PREPARE_SCRIPT" ]; then
    echo "执行准备脚本: $PREPARE_SCRIPT $UDID" >> "$LOG_FILE"
    bash "$PREPARE_SCRIPT" "$UDID" >> "$LOG_FILE" 2>&1
    
    # 检查准备脚本是否成功创建了目录
    if [ -d "$DEVICE_WDA_PATH" ] && [ -d "$DEVICE_WDA_PATH/WebDriverAgent.xcodeproj" ]; then
      echo "准备脚本成功创建了设备特定的 WDA 目录" >> "$LOG_FILE"
      TARGET_DIR="$DEVICE_WDA_PATH"
    else
      echo "准备脚本未能创建设备特定的 WDA 目录，使用默认目录" >> "$LOG_FILE"
      TARGET_DIR="$PROJECT_ROOT/WebDriverAgent-${WDA_VERSION}"
    fi
  else
    echo "准备脚本不存在: $PREPARE_SCRIPT，使用默认目录" >> "$LOG_FILE"
    TARGET_DIR="$PROJECT_ROOT/WebDriverAgent-${WDA_VERSION}"
  fi
fi

if [ ! -d "$TARGET_DIR" ]; then
  echo "Error: Directory $TARGET_DIR not found!" >&2
  exit 1
fi

cd "$TARGET_DIR" || exit 1

# ===== 修改：清理 WebDriverAgent 的 DerivedData 缓存，只清理当前设备相关的缓存 =====
echo "===== 开始清理 WebDriverAgent DerivedData 缓存 (仅限当前设备) =====" >> "$LOG_FILE"
echo "清理前 DerivedData 目录状态:" >> "$LOG_FILE"
ls -la ~/Library/Developer/Xcode/DerivedData | grep "WebDriverAgent-" >> "$LOG_FILE" 2>&1

# 执行清理操作 - 只清理与当前设备相关的缓存
echo "执行清理操作 (仅限设备 $UDID)..." >> "$LOG_FILE"

# 设备特定的 WDA 路径模式，用于匹配
WDA_PATH_PATTERN="WDA-${UDID}-${WDA_VERSION}"
echo "查找包含路径 $WDA_PATH_PATTERN 的缓存目录" >> "$LOG_FILE"

# 清除的目录计数
CLEARED_DIRS=0

# 遍历所有 WebDriverAgent 缓存目录
for dir in $(find ~/Library/Developer/Xcode/DerivedData -name "WebDriverAgent-*" -type d -maxdepth 1); do
  # 检查目录中的 info.plist 文件是否存在
  if [ -f "$dir/info.plist" ]; then
    # 从 info.plist 中提取 WorkspacePath
    WORKSPACE_PATH=$(plutil -extract WorkspacePath raw "$dir/info.plist" 2>/dev/null || true)
    
    # 检查 WorkspacePath 是否包含当前设备的 WDA 路径模式
    if [[ "$WORKSPACE_PATH" == *"$WDA_PATH_PATTERN"* ]]; then
      echo "找到与设备 $UDID 相关的缓存目录: $dir" >> "$LOG_FILE"
      echo "目录的 WorkspacePath: $WORKSPACE_PATH" >> "$LOG_FILE"
      
      # 删除该目录
      rm -rf "$dir"
      echo "已删除缓存目录: $dir" >> "$LOG_FILE"
      ((CLEARED_DIRS++))
    fi
  fi
done

if [ $CLEARED_DIRS -gt 0 ]; then
  echo "已清理 $CLEARED_DIRS 个与设备 $UDID 相关的缓存目录" >> "$LOG_FILE"
else
  echo "未找到与设备 $UDID 相关的缓存目录，跳过清理" >> "$LOG_FILE"
fi

echo "清理后 DerivedData 目录状态:" >> "$LOG_FILE"
ls -la ~/Library/Developer/Xcode/DerivedData | grep "WebDriverAgent-" >> "$LOG_FILE" 2>&1
echo "===== WebDriverAgent DerivedData 缓存清理完成 (仅限当前设备) =====" >> "$LOG_FILE"
# ===== 清理代码结束 =====

# 构建 DerivedData 路径
BUILD_DIR="$HOME/build/wda"

# 自动修复签名配置（可选）
xcodebuild clean -project WebDriverAgent.xcodeproj -scheme WebDriverAgentRunner -destination "id=$UDID"

DATE_TIME=$(date +"%Y-%m-%d %H:%M:%S")
printf "\n----------%s----------\n" "$DATE_TIME" >> "$LOG_FILE"
echo "使用开发团队 ID: $DEVELOPMENT_TEAM" >> "$LOG_FILE"
echo "使用 Bundle ID: $PRODUCT_BUNDLE_IDENTIFIER" >> "$LOG_FILE"
echo "使用 Runner Bundle ID: ${PRODUCT_BUNDLE_IDENTIFIER}.xctrunner" >> "$LOG_FILE"
echo "构建目录: $BUILD_DIR" >> "$LOG_FILE"

# 创建构建目录
mkdir -p "$BUILD_DIR"

# 第一步：使用 xcodebuild build-for-testing 编译 WDA
echo "开始编译 WDA..." >> "$LOG_FILE"
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "开始编译 WDA..." >> "$IOS_TOOL_LOG_FILE"
  echo "构建目录: $BUILD_DIR" >> "$IOS_TOOL_LOG_FILE"
  echo "设备UDID: $UDID" >> "$IOS_TOOL_LOG_FILE"
  echo "开发团队ID: $DEVELOPMENT_TEAM" >> "$IOS_TOOL_LOG_FILE"
  echo "Bundle ID: $PRODUCT_BUNDLE_IDENTIFIER" >> "$IOS_TOOL_LOG_FILE"
  echo "==================== xcodebuild build-for-testing ====================" >> "$IOS_TOOL_LOG_FILE"
fi

xcodebuild build-for-testing \
  -project WebDriverAgent.xcodeproj \
  -scheme WebDriverAgentRunner \
  -sdk iphoneos \
  -configuration Release \
  -derivedDataPath "$BUILD_DIR" \
  DEVELOPMENT_TEAM="$DEVELOPMENT_TEAM" \
  PRODUCT_BUNDLE_IDENTIFIER="$PRODUCT_BUNDLE_IDENTIFIER" \
  ONLY_ACTIVE_ARCH=YES \
  IPHONEOS_DEPLOYMENT_TARGET=15.0 \
  >> "${IOS_TOOL_LOG_FILE:-$LOG_FILE}" 2>&1

BUILD_RESULT=$?
if [ $BUILD_RESULT -ne 0 ]; then
  echo "WDA 编译失败，退出码: $BUILD_RESULT" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "WDA 编译失败，退出码: $BUILD_RESULT" >> "$IOS_TOOL_LOG_FILE"
  exit $BUILD_RESULT
fi

echo "WDA 编译成功" >> "$LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "WDA 编译成功" >> "$IOS_TOOL_LOG_FILE"

# 等待编译完成并确保所有文件都已生成
echo "等待编译完成并确保所有文件都已生成..." >> "$LOG_FILE"
sleep 3

# 第二步：生成 IPA 文件
echo "开始生成 IPA 文件..." >> "$LOG_FILE"
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "==================== IPA 文件生成 ====================" >> "$IOS_TOOL_LOG_FILE"
  echo "开始生成 IPA 文件..." >> "$IOS_TOOL_LOG_FILE"
fi

RELEASE_DIR="$BUILD_DIR/Build/Products/Release-iphoneos"
if [ ! -d "$RELEASE_DIR" ]; then
  echo "错误：找不到构建产物目录 $RELEASE_DIR" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "错误：找不到构建产物目录 $RELEASE_DIR" >> "$IOS_TOOL_LOG_FILE"
  exit 1
fi

cd "$RELEASE_DIR"

# 检查 .app 文件是否存在
if [ ! -d "WebDriverAgentRunner-Runner.app" ]; then
  echo "错误：找不到 WebDriverAgentRunner-Runner.app" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "错误：找不到 WebDriverAgentRunner-Runner.app" >> "$IOS_TOOL_LOG_FILE"
  exit 1
fi

# 创建 Payload 目录并复制 .app 文件
mkdir -p Payload
cp -R WebDriverAgentRunner-Runner.app Payload/

# 生成 IPA 文件
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "正在打包 IPA 文件..." >> "$IOS_TOOL_LOG_FILE"
  zip -r WDA.ipa Payload >> "$IOS_TOOL_LOG_FILE" 2>&1
else
  zip -r WDA.ipa Payload >> "$LOG_FILE" 2>&1
fi

if [ ! -f "WDA.ipa" ]; then
  echo "错误：IPA 文件生成失败" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "错误：IPA 文件生成失败" >> "$IOS_TOOL_LOG_FILE"
  exit 1
fi

echo "IPA 文件生成成功: $RELEASE_DIR/WDA.ipa" >> "$LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "IPA 文件生成成功: $RELEASE_DIR/WDA.ipa" >> "$IOS_TOOL_LOG_FILE"

# 等待IPA文件生成完成
echo "等待IPA文件生成完成..." >> "$LOG_FILE"
sleep 2

# 第三步：设备特定的tunnel连接管理
echo "检查设备 $UDID 的tunnel连接..." >> "$LOG_FILE"
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "==================== 设备tunnel连接检查 ====================" >> "$IOS_TOOL_LOG_FILE"
  echo "检查设备 $UDID 的tunnel连接..." >> "$IOS_TOOL_LOG_FILE"
fi

# 创建tunnel专用日志文件
TUNNEL_LOG_DIR="$PROJECT_ROOT/log/tunnel_logs"
mkdir -p "$TUNNEL_LOG_DIR"
TUNNEL_LOG_FILE="$TUNNEL_LOG_DIR/tunnel_$(date '+%Y-%m-%d_%H-%M-%S').log"
echo "创建tunnel日志文件: $TUNNEL_LOG_FILE" >> "$LOG_FILE"

# 网络环境诊断（帮助分析IPv6连接问题）
diagnose_network_environment() {
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始网络环境诊断..." >> "$TUNNEL_LOG_FILE"
    
    # 检查IPv6支持
    if sysctl net.inet6.ip6.forwarding >/dev/null 2>&1; then
        IPV6_SUPPORT=$(sysctl -n net.inet6.ip6.forwarding 2>/dev/null || echo "unknown")
        echo "$(date '+%Y-%m-%d %H:%M:%S') - IPv6转发状态: $IPV6_SUPPORT" >> "$TUNNEL_LOG_FILE"
    fi
    
    # 检查网络接口IPv6地址
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 网络接口IPv6地址:" >> "$TUNNEL_LOG_FILE"
    ifconfig | grep -A 1 "inet6" | head -10 >> "$TUNNEL_LOG_FILE" 2>/dev/null || echo "无法获取IPv6信息" >> "$TUNNEL_LOG_FILE"
    
    # 检查系统版本
    MACOS_VERSION=$(sw_vers -productVersion)
    echo "$(date '+%Y-%m-%d %H:%M:%S') - macOS版本: $MACOS_VERSION" >> "$TUNNEL_LOG_FILE"
    
    # 检查go-ios版本
    IOS_VERSION=$("$IOS_COMMAND" --version 2>/dev/null || echo "unknown")
    echo "$(date '+%Y-%m-%d %H:%M:%S') - go-ios版本: $IOS_VERSION" >> "$TUNNEL_LOG_FILE"
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 网络环境诊断完成" >> "$TUNNEL_LOG_FILE"
}

# 执行网络环境诊断
diagnose_network_environment

# 安全的tunnel服务状态检查
check_tunnel_health() {
    local udid=$1
    # 首先检查tunnel进程是否存在
    if ! pgrep -f "ios tunnel start" > /dev/null; then
        echo "no_tunnel_process"
        return 1
    fi
    
    # 检查设备是否可以通过tunnel连接
    if "$IOS_COMMAND" info --udid="$udid" >/dev/null 2>&1; then
        echo "tunnel_healthy"
        return 0
    else
        echo "tunnel_unhealthy"
        return 2
    fi
}

# 检查tunnel服务状态
echo "$(date '+%Y-%m-%d %H:%M:%S') - 检查tunnel服务健康状态..." >> "$LOG_FILE"
TUNNEL_STATUS=$(check_tunnel_health "$UDID")
TUNNEL_CHECK_RESULT=$?

echo "$(date '+%Y-%m-%d %H:%M:%S') - Tunnel状态检查结果: $TUNNEL_STATUS" >> "$LOG_FILE"
echo "$(date '+%Y-%m-%d %H:%M:%S') - 设备 $UDID tunnel状态: $TUNNEL_STATUS" >> "$TUNNEL_LOG_FILE"

# 根据tunnel健康状态决定处理策略
if [ "$TUNNEL_STATUS" = "tunnel_healthy" ]; then
  echo "$(date '+%Y-%m-%d %H:%M:%S') - tunnel服务健康，设备连接正常" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "tunnel服务健康，设备连接正常" >> "$IOS_TOOL_LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 设备 $UDID 可以正常通过tunnel连接" >> "$TUNNEL_LOG_FILE"

elif [ "$TUNNEL_STATUS" = "tunnel_unhealthy" ]; then
  echo "$(date '+%Y-%m-%d %H:%M:%S') - tunnel进程存在但设备连接异常，尝试恢复连接" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "tunnel进程存在但设备连接异常，尝试恢复连接" >> "$IOS_TOOL_LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 设备 $UDID 连接异常，开始恢复流程" >> "$TUNNEL_LOG_FILE"
  
  # 先停止可能存在的问题代理连接
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 清理异常的tunnel代理连接..." >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 执行命令: $IOS_COMMAND tunnel stopagent" >> "$TUNNEL_LOG_FILE"
  "$IOS_COMMAND" tunnel stopagent >> "$TUNNEL_LOG_FILE" 2>&1 || true
  
  # 尝试连接恢复（避免重启tunnel，因为多设备共享）
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 等待tunnel连接自动恢复..." >> "$TUNNEL_LOG_FILE"
  sleep 8
  
  # 再次检查连接状态
  if "$IOS_COMMAND" info --udid="$UDID" >/dev/null 2>&1; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 设备 $UDID tunnel连接已恢复" >> "$LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 连接恢复成功" >> "$TUNNEL_LOG_FILE"
  else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 警告：设备 $UDID tunnel连接仍然异常，但继续尝试安装" >> "$LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 连接恢复失败，但不中断流程" >> "$TUNNEL_LOG_FILE"
  fi

elif [ "$TUNNEL_STATUS" = "no_tunnel_process" ]; then
  echo "启动全局tunnel服务..." >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "启动全局tunnel服务..." >> "$IOS_TOOL_LOG_FILE"
  
  # 先停止可能存在的tunnel代理连接
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 停止潜在的tunnel代理连接..." >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 执行命令: $IOS_COMMAND tunnel stopagent" >> "$TUNNEL_LOG_FILE"
  "$IOS_COMMAND" tunnel stopagent >> "$TUNNEL_LOG_FILE" 2>&1 || true
  
  # 等待停止完成
  sleep 2
  
  # 在后台启动 ios tunnel，使用专门的日志文件
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 启动tunnel服务，日志文件: $TUNNEL_LOG_FILE" >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 启动命令: $IOS_COMMAND tunnel start" >> "$TUNNEL_LOG_FILE"
  
  nohup sudo "$IOS_COMMAND" tunnel start >> "$TUNNEL_LOG_FILE" 2>&1 &
  
  TUNNEL_PID=$!
  echo "tunnel服务已启动，PID: $TUNNEL_PID" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "tunnel服务已启动，PID: $TUNNEL_PID" >> "$IOS_TOOL_LOG_FILE"
  
  # 等待 tunnel 服务充分启动
  echo "等待tunnel服务完全启动..." >> "$LOG_FILE"
  sleep 8
  
  # 验证tunnel服务启动成功
  if "$IOS_COMMAND" info --udid="$UDID" >/dev/null 2>&1; then
    echo "tunnel服务启动成功，设备 $UDID 连接正常" >> "$LOG_FILE"
    [ -n "$IOS_TOOL_LOG_FILE" ] && echo "tunnel服务启动成功，设备 $UDID 连接正常" >> "$IOS_TOOL_LOG_FILE"
  else
    echo "警告：tunnel服务可能未完全启动，继续尝试安装" >> "$LOG_FILE"
    [ -n "$IOS_TOOL_LOG_FILE" ] && echo "警告：tunnel服务可能未完全启动" >> "$IOS_TOOL_LOG_FILE"
  fi
fi

# 第四步：安装 WDA IPA 文件
echo "开始安装 WDA IPA 文件..." >> "$LOG_FILE"
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "==================== 安装 WDA IPA 文件 ====================" >> "$IOS_TOOL_LOG_FILE"
  echo "开始安装 WDA IPA 文件..." >> "$IOS_TOOL_LOG_FILE"
  echo "IPA 文件路径: $RELEASE_DIR/WDA.ipa" >> "$IOS_TOOL_LOG_FILE"
  echo "设备 UDID: $UDID" >> "$IOS_TOOL_LOG_FILE"
fi

# 安装 IPA 文件
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  "$IOS_COMMAND" install --path="$RELEASE_DIR/WDA.ipa" --udid="$UDID" >> "$IOS_TOOL_LOG_FILE" 2>&1
else
  "$IOS_COMMAND" install --path="$RELEASE_DIR/WDA.ipa" --udid="$UDID" >> "$LOG_FILE" 2>&1
fi

INSTALL_RESULT=$?
if [ $INSTALL_RESULT -ne 0 ]; then
  echo "WDA IPA 安装失败，退出码: $INSTALL_RESULT" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "WDA IPA 安装失败，退出码: $INSTALL_RESULT" >> "$IOS_TOOL_LOG_FILE"
  exit $INSTALL_RESULT
fi

echo "WDA IPA 安装成功" >> "$LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "WDA IPA 安装成功" >> "$IOS_TOOL_LOG_FILE"

# 等待安装完成
echo "等待 WDA IPA 安装完成..." >> "$LOG_FILE"
sleep 3

# 第五步：分配设备端口并启动 WDA
echo "==================== 分配设备WDA端口 ====================" >> "$LOG_FILE"

# 设备端口检测和分配函数
check_device_port_available() {
    local port=$1
    local udid=$2
    # 检查设备上是否有进程占用该端口
    # 使用ios ps命令检查设备上的进程
    if "$IOS_COMMAND" ps --udid="$udid" 2>/dev/null | grep -q ":$port\|$port"; then
        return 1  # 端口被占用
    else
        return 0  # 端口可用
    fi
}

# 动态分配设备端口
allocate_device_port() {
    local udid=$1
    local start_port=$2
    local max_attempts=${3:-20}
    
    local device_port=$start_port
    local attempts=0
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始为设备 $udid 分配WDA端口，起始端口: $start_port" >> "$LOG_FILE"
    
    while [ $attempts -lt $max_attempts ]; do
        if check_device_port_available $device_port "$udid"; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 找到可用的设备端口: $device_port" >> "$LOG_FILE"
            echo "$device_port"
            return 0
        else
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 设备端口 $device_port 被占用，尝试下一个端口" >> "$LOG_FILE"
        fi
        
        device_port=$((device_port + 1))
        attempts=$((attempts + 1))
    done
    
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误：在尝试 $max_attempts 个端口后仍未找到可用的设备端口" >> "$LOG_FILE"
    return 1
}

DEVICE_WDA_PORT=$(allocate_device_port "$UDID" 8100 20)
DEVICE_PORT_RESULT=$?

if [ $DEVICE_PORT_RESULT -ne 0 ] || [ -z "$DEVICE_WDA_PORT" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误：无法为设备 $UDID 分配可用的WDA端口" >> "$LOG_FILE"
    echo "设备WDA端口分配失败，使用默认端口8100" >> "$LOG_FILE"
    DEVICE_WDA_PORT=8100
fi

echo "$(date '+%Y-%m-%d %H:%M:%S') - 设备 $UDID 将使用WDA设备端口: $DEVICE_WDA_PORT" >> "$LOG_FILE"

# 第六步：使用 ios runwda 启动 WDA
echo "使用 ios runwda 启动 WDA..." >> "$LOG_FILE"
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "==================== ios runwda 启动 WDA ====================" >> "$IOS_TOOL_LOG_FILE"
  echo "使用 ios runwda 启动 WDA..." >> "$IOS_TOOL_LOG_FILE"
fi

# 从设备状态文件中获取 bundle_id
BUNDLE_ID="com.meituan.imeituan"
STATUS_FILE="$PROJECT_ROOT/status/${UDID}.json"
if [ -f "$STATUS_FILE" ]; then
  # 使用 jq 或 python 解析 JSON 文件获取 bundle_id
  if command -v jq >/dev/null 2>&1; then
    BUNDLE_ID=$(jq -r '.bundle_id // "com.meituan.imeituan"' "$STATUS_FILE")
  elif command -v python3 >/dev/null 2>&1; then
    BUNDLE_ID=$(python3 -c "import json; f=open('$STATUS_FILE'); data=json.load(f); print(data.get('bundle_id', 'com.meituan.imeituan')); f.close()")
  fi
fi

echo "使用 Bundle ID: $BUNDLE_ID" >> "$LOG_FILE"
if [ -n "$IOS_TOOL_LOG_FILE" ]; then
  echo "设备UDID: $UDID" >> "$IOS_TOOL_LOG_FILE"
  echo "Bundle ID: $BUNDLE_ID" >> "$IOS_TOOL_LOG_FILE"
  echo "Test Runner Bundle ID: ${PRODUCT_BUNDLE_IDENTIFIER}.xctrunner" >> "$IOS_TOOL_LOG_FILE"
  echo "XCTest Config: WebDriverAgentRunner.xctest" >> "$IOS_TOOL_LOG_FILE"
fi

# 创建独立的日志文件
WDA_LOG_FILE="${LOG_FILE%.log}_wda.log"
FORWARD_LOG_FILE="${LOG_FILE%.log}_forward.log"

echo "创建独立日志文件:" >> "$LOG_FILE"
echo "  WDA日志: $WDA_LOG_FILE" >> "$LOG_FILE"
echo "  转发日志: $FORWARD_LOG_FILE" >> "$LOG_FILE"

# 启动 WDA 并记录到独立日志
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始启动 ios runwda 进程" >> "$WDA_LOG_FILE"
echo "$(date '+%Y-%m-%d %H:%M:%S') - 命令: ios runwda --udid=$UDID --bundleid=$BUNDLE_ID --testrunnerbundleid=${PRODUCT_BUNDLE_IDENTIFIER}.xctrunner --xctestconfig=WebDriverAgentRunner.xctest --env USE_PORT=$DEVICE_WDA_PORT" >> "$WDA_LOG_FILE"

# 使用 nohup 确保进程脱离当前会话，在macOS中不使用setsid
nohup "$IOS_COMMAND" runwda \
  --udid="$UDID" \
  --bundleid="$BUNDLE_ID" \
  --testrunnerbundleid="${PRODUCT_BUNDLE_IDENTIFIER}.xctrunner" \
  --xctestconfig="WebDriverAgentRunner.xctest" \
  --env USE_PORT="$DEVICE_WDA_PORT" \
  >> "$WDA_LOG_FILE" 2>&1 &

WDA_PID=$!
echo "WDA started with PID: $WDA_PID" >> "$LOG_FILE"
echo "WDA will run on device port $DEVICE_WDA_PORT" >> "$LOG_FILE"
echo "$(date '+%Y-%m-%d %H:%M:%S') - ios runwda process started with PID: $WDA_PID" >> "$WDA_LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "WDA started with PID: $WDA_PID, will run on device port $DEVICE_WDA_PORT" >> "$IOS_TOOL_LOG_FILE"

# 等待 WDA 服务完全启动
echo "等待 WDA 服务完全启动..." >> "$LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "等待 WDA 服务完全启动..." >> "$IOS_TOOL_LOG_FILE"
echo "$(date '+%Y-%m-%d %H:%M:%S') - 等待 WDA 服务启动，检查进程状态..." >> "$WDA_LOG_FILE"

# 监控WDA进程状态并等待启动完成
sleep 2
if kill -0 $WDA_PID 2>/dev/null; then
  echo "$(date '+%Y-%m-%d %H:%M:%S') - WDA进程运行正常，PID: $WDA_PID" >> "$WDA_LOG_FILE"
  
  # 等待WDA服务完全启动（检查设备端口是否有WDA服务）
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 等待WDA服务在设备端口${DEVICE_WDA_PORT}上完全启动..." >> "$WDA_LOG_FILE"
  WDA_READY=false
  for i in {1..30}; do
    if kill -0 $WDA_PID 2>/dev/null; then
      # 检查WDA日志中是否有"WebDriverAgent is ready"相关信息
      if grep -q "authorized.*true" "$WDA_LOG_FILE" 2>/dev/null; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - WDA服务已准备就绪，第 $i 次检查" >> "$WDA_LOG_FILE"
        WDA_READY=true
        break
      fi
    else
      echo "$(date '+%Y-%m-%d %H:%M:%S') - WDA进程在等待期间退出，PID: $WDA_PID" >> "$WDA_LOG_FILE"
      break
    fi
    sleep 1
  done
  
  if [ "$WDA_READY" = true ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - WDA服务启动成功" >> "$WDA_LOG_FILE"
  else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - WDA服务启动超时或失败" >> "$WDA_LOG_FILE"
  fi
else
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 警告：WDA进程可能已经退出，PID: $WDA_PID" >> "$WDA_LOG_FILE"
fi

# 第六步：建立端口转发（支持端口占用检测和自动端口分配）
echo "==================== 建立端口转发 ====================" >> "$LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "==================== 建立端口转发 ====================" >> "$IOS_TOOL_LOG_FILE"

# 函数：检查端口是否被占用
check_port_occupied() {
    local port=$1
    if lsof -i:$port > /dev/null 2>&1; then
        return 0  # 端口被占用
    else
        return 1  # 端口可用
    fi
}

# 函数：检查是否已有相同的端口转发
check_existing_forward() {
    local port=$1
    local udid=$2
    local device_port=$3
    local existing_forward=$(ps -ax | grep "ios forward.*$port.*$device_port.*$udid" | grep -v grep)
    if [ -n "$existing_forward" ]; then
        echo "$existing_forward"
        return 0  # 已有转发
    else
        return 1  # 无转发
    fi
}

# 函数：更新设备状态文件中的端口信息
update_device_port_status() {
    local udid=$1
    local new_port=$2
    local status_file="$PROJECT_ROOT/status/${udid}.json"
    
    if [ -f "$status_file" ]; then
        # 使用Python脚本更新端口信息
        python3 - <<EOF
import json
import os
import sys

status_file = "$status_file"
new_port = $new_port

try:
    with open(status_file, 'r', encoding='utf-8') as f:
        status_data = json.load(f)
    
    # 更新端口信息
    status_data['wda_forward_port'] = new_port
    status_data['local_wda_port'] = new_port  # 添加local_wda_port字段，与wda_forward_port保持一致
    
    with open(status_file, 'w', encoding='utf-8') as f:
        json.dump(status_data, f, ensure_ascii=False, indent=2)
    
    print(f"已更新设备 $udid 的端口信息为: wda_forward_port={new_port}, local_wda_port={new_port}")
except Exception as e:
    print(f"更新设备状态文件失败: {e}")
    sys.exit(1)
EOF
    else
        echo "警告：设备状态文件不存在: $status_file" >> "$LOG_FILE"
    fi
}

# 更新设备WDA端口信息（包括设备端口和转发端口）
update_device_wda_ports() {
    local udid=$1
    local forward_port=$2
    local device_port=$3
    local status_file="$PROJECT_ROOT/status/${udid}.json"
    
    if [ -f "$status_file" ]; then
        # 使用Python脚本更新WDA端口信息
        python3 - <<EOF
import json
import os
import sys

status_file = "$status_file"
forward_port = $forward_port
device_port = $device_port

try:
    with open(status_file, 'r', encoding='utf-8') as f:
        status_data = json.load(f)
    
    # 更新WDA端口信息
    if forward_port > 0:
        status_data['wda_forward_port'] = forward_port
        status_data['local_wda_port'] = forward_port  # local_wda_port与wda_forward_port保持一致
    if device_port > 0:
        status_data['wda_port'] = device_port  # 设备上WDA运行的端口
    
    with open(status_file, 'w', encoding='utf-8') as f:
        json.dump(status_data, f, ensure_ascii=False, indent=2)
    
    print(f"已更新设备 $udid 的WDA端口信息 - 转发端口: {forward_port}, 设备端口: {device_port}, 本地端口: {forward_port}")
except Exception as e:
    print(f"更新设备状态文件失败: {e}")
    sys.exit(1)
EOF
    else
        echo "警告：设备状态文件不存在: $status_file" >> "$LOG_FILE"
    fi
}

# 检查是否已有相同的端口转发
ORIGINAL_PORT=$PORT
if existing_forward=$(check_existing_forward "$PORT" "$UDID" "$DEVICE_WDA_PORT"); then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 发现已存在的端口转发:" >> "$FORWARD_LOG_FILE"
    echo "$existing_forward" >> "$FORWARD_LOG_FILE"
    echo "端口转发已存在，跳过创建: 设备${DEVICE_WDA_PORT} -> macOS${PORT}" >> "$LOG_FILE"
    [ -n "$IOS_TOOL_LOG_FILE" ] && echo "端口转发已存在，跳过创建: 设备${DEVICE_WDA_PORT} -> macOS${PORT}" >> "$IOS_TOOL_LOG_FILE"
    
    # 提取现有转发进程的PID
    FORWARD_PID=$(echo "$existing_forward" | awk '{print $1}')
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 使用现有端口转发，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
    EXISTING_FORWARD=true
else
    # 检查端口是否被占用，如果被占用则自动分配新端口
    if check_port_occupied "$PORT"; then
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口 $PORT 已被占用，正在寻找可用端口..." >> "$FORWARD_LOG_FILE"
        echo "端口 $PORT 已被占用，正在寻找可用端口..." >> "$LOG_FILE"
        [ -n "$IOS_TOOL_LOG_FILE" ] && echo "端口 $PORT 已被占用，正在寻找可用端口..." >> "$IOS_TOOL_LOG_FILE"
        
        # 寻找可用端口（从原端口开始，向上递增）
        NEW_PORT=$PORT
        MAX_ATTEMPTS=50
        ATTEMPTS=0
        
        while [ $ATTEMPTS -lt $MAX_ATTEMPTS ]; do
            if ! check_port_occupied "$NEW_PORT"; then
                echo "$(date '+%Y-%m-%d %H:%M:%S') - 找到可用端口: $NEW_PORT" >> "$FORWARD_LOG_FILE"
                echo "找到可用端口: $NEW_PORT" >> "$LOG_FILE"
                [ -n "$IOS_TOOL_LOG_FILE" ] && echo "找到可用端口: $NEW_PORT" >> "$IOS_TOOL_LOG_FILE"
                
                # 更新设备状态文件中的端口信息
                update_device_port_status "$UDID" "$NEW_PORT"
                PORT=$NEW_PORT
                break
            fi
            NEW_PORT=$((NEW_PORT + 1))
            ATTEMPTS=$((ATTEMPTS + 1))
        done
        
        if [ $ATTEMPTS -ge $MAX_ATTEMPTS ]; then
            echo "$(date '+%Y-%m-%d %H:%M:%S') - 错误：在尝试 $MAX_ATTEMPTS 个端口后仍未找到可用端口" >> "$FORWARD_LOG_FILE"
            echo "错误：在尝试 $MAX_ATTEMPTS 个端口后仍未找到可用端口" >> "$LOG_FILE"
            [ -n "$IOS_TOOL_LOG_FILE" ] && echo "错误：在尝试 $MAX_ATTEMPTS 个端口后仍未找到可用端口" >> "$IOS_TOOL_LOG_FILE"
            exit 1
        fi
    fi
    
    echo "开始建立端口转发: 设备${DEVICE_WDA_PORT} -> macOS${PORT}" >> "$LOG_FILE"
    [ -n "$IOS_TOOL_LOG_FILE" ] && echo "开始建立端口转发: 设备${DEVICE_WDA_PORT} -> macOS${PORT}" >> "$IOS_TOOL_LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始建立端口转发: 设备${DEVICE_WDA_PORT} -> macOS${PORT}" >> "$FORWARD_LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 命令: $IOS_COMMAND forward $PORT $DEVICE_WDA_PORT --udid=$UDID" >> "$FORWARD_LOG_FILE"
    
    # 启动端口转发，使用 nohup 确保进程脱离当前会话，在macOS中不使用setsid
    nohup "$IOS_COMMAND" forward "$PORT" "$DEVICE_WDA_PORT" --udid="$UDID" >> "$FORWARD_LOG_FILE" 2>&1 &
    
    FORWARD_PID=$!
    echo "端口转发已启动，PID: $FORWARD_PID" >> "$LOG_FILE"
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口转发进程已启动，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
    [ -n "$IOS_TOOL_LOG_FILE" ] && echo "端口转发已启动，PID: $FORWARD_PID" >> "$IOS_TOOL_LOG_FILE"
    EXISTING_FORWARD=false
fi

# 如果端口发生了变化，记录相关信息
if [ "$PORT" != "$ORIGINAL_PORT" ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口已从 $ORIGINAL_PORT 更改为 $PORT" >> "$FORWARD_LOG_FILE"
    echo "端口已从 $ORIGINAL_PORT 更改为 $PORT" >> "$LOG_FILE"
    [ -n "$IOS_TOOL_LOG_FILE" ] && echo "端口已从 $ORIGINAL_PORT 更改为 $PORT" >> "$IOS_TOOL_LOG_FILE"
fi

# 等待端口转发建立（只有在新建转发时才需要等待）
if [ "$EXISTING_FORWARD" = false ]; then
  echo "等待端口转发建立..." >> "$LOG_FILE"
  sleep 3

  # 检查端口转发状态并等待建立
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 检查端口转发状态..." >> "$FORWARD_LOG_FILE"
  if kill -0 $FORWARD_PID 2>/dev/null; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口转发进程运行正常，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
    
    # 等待端口转发完全建立
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 等待端口转发完全建立..." >> "$FORWARD_LOG_FILE"
    FORWARD_READY=false
    for i in {1..15}; do
      if kill -0 $FORWARD_PID 2>/dev/null; then
        # 检查端口转发日志中是否有"Start listening"信息
        if grep -q "Start listening" "$FORWARD_LOG_FILE" 2>/dev/null; then
          echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口转发已开始监听，第 $i 次检查" >> "$FORWARD_LOG_FILE"
          FORWARD_READY=true
          break
        fi
      else
        echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口转发进程在等待期间退出，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
        break
      fi
      sleep 1
    done
    
    if [ "$FORWARD_READY" = true ]; then
      echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口转发建立成功" >> "$FORWARD_LOG_FILE"
    else
      echo "$(date '+%Y-%m-%d %H:%M:%S') - 端口转发建立超时或失败" >> "$FORWARD_LOG_FILE"
    fi
  else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - 警告：端口转发进程可能已经退出，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
  fi
else
  echo "使用现有端口转发，跳过等待步骤" >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 使用现有端口转发，跳过等待步骤" >> "$FORWARD_LOG_FILE"
  FORWARD_READY=true
fi

# 测试端口转发，多次重试
echo "$(date '+%Y-%m-%d %H:%M:%S') - Testing port forwarding connection..." >> "$FORWARD_LOG_FILE"
FORWARD_TEST_SUCCESS=false

# 先等待更长时间让WDA服务完全启动
sleep 10

for i in {1..10}; do  # 增加重试次数
  # 更详细的测试命令
  test_result=$(curl -s --max-time 10 --connect-timeout 5 "http://127.0.0.1:${PORT}/status" 2>&1)
  curl_exit_code=$?
  
  if [ $curl_exit_code -eq 0 ]; then
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Port forwarding test successful on attempt $i" >> "$FORWARD_LOG_FILE"
    FORWARD_TEST_SUCCESS=true
    break
  else
    echo "$(date '+%Y-%m-%d %H:%M:%S') - Port forwarding test failed on attempt $i, exit code: $curl_exit_code, error: $test_result" >> "$FORWARD_LOG_FILE"
    sleep 3  # 增加等待间隔
  fi
done

if [ "$FORWARD_TEST_SUCCESS" = true ]; then
  echo "Port forwarding test successful, WDA service accessible" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "Port forwarding test successful, WDA service accessible" >> "$IOS_TOOL_LOG_FILE"
else
  echo "Port forwarding test failed, WDA service not accessible" >> "$LOG_FILE"
  [ -n "$IOS_TOOL_LOG_FILE" ] && echo "Port forwarding test failed, WDA service not accessible" >> "$IOS_TOOL_LOG_FILE"
fi

# 最终检查所有进程状态
echo "$(date '+%Y-%m-%d %H:%M:%S') - 最终进程状态检查:" >> "$LOG_FILE"
echo "  WDA进程 PID: $WDA_PID" >> "$LOG_FILE"
echo "  转发进程 PID: $FORWARD_PID" >> "$LOG_FILE"

if kill -0 $WDA_PID 2>/dev/null; then
  echo "  WDA进程状态: 运行中" >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 最终检查：WDA进程运行中，PID: $WDA_PID" >> "$WDA_LOG_FILE"
else
  echo "  WDA进程状态: 已退出" >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 最终检查：WDA进程已退出，PID: $WDA_PID" >> "$WDA_LOG_FILE"
fi

if kill -0 $FORWARD_PID 2>/dev/null; then
  echo "  转发进程状态: 运行中" >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 最终检查：端口转发进程运行中，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
else
  echo "  转发进程状态: 已退出" >> "$LOG_FILE"
  echo "$(date '+%Y-%m-%d %H:%M:%S') - 最终检查：端口转发进程已退出，PID: $FORWARD_PID" >> "$FORWARD_LOG_FILE"
fi

echo "WDA 启动流程完成，设备WDA运行在端口${DEVICE_WDA_PORT}，转发到macOS端口${PORT}" >> "$LOG_FILE"
[ -n "$IOS_TOOL_LOG_FILE" ] && echo "WDA 启动流程完成，设备WDA运行在端口${DEVICE_WDA_PORT}，转发到macOS端口${PORT}" >> "$IOS_TOOL_LOG_FILE"

# 更新设备状态文件中的端口信息
echo "$(date '+%Y-%m-%d %H:%M:%S') - 更新设备状态文件中的WDA端口信息..." >> "$LOG_FILE"
update_device_wda_ports "$UDID" "$PORT" "$DEVICE_WDA_PORT"

# 保存进程ID到状态文件，供后续管理
echo "$(date '+%Y-%m-%d %H:%M:%S') - 保存进程信息:" >> "$LOG_FILE"
echo "  WDA_PID=$WDA_PID" >> "$LOG_FILE"
echo "  FORWARD_PID=$FORWARD_PID" >> "$LOG_FILE"

# 持续监控进程状态（可选，用于调试）
echo "$(date '+%Y-%m-%d %H:%M:%S') - 开始持续监控进程状态..." >> "$LOG_FILE"